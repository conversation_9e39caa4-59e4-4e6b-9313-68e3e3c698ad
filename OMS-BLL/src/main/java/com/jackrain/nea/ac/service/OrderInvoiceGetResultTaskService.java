package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.DataType;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootObject;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultData;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApplyItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceLogService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName OrderInvoiceGetResultTaskService
 * @Description 定时对开票中的发票获取开票结果并更新结果
 * @Date 2022/9/30 下午3:17
 * @Created by wuhang
 */
@Slf4j
@Component
public class OrderInvoiceGetResultTaskService {

    @Autowired
    private AcFOrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private AcFInvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private AcFInvoiceApplyItemMapper invoiceApplyItemMapper;

    @Autowired
    private InvoiceLogService logService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    @NacosValue(value = "${lts.OrderInvoiceGetResultTaskService.range:100}", autoRefreshed = true)
    public Integer range;

    public void getResult() {
        executeThread();
    }

    //使用线程
    public void executeThread() {
        String threadPoolName = "R3_OMS_AC_F_ORDER_INVOICE_GET_INVOICE_%d";
        log.info("---| 开始获取开票结果定时任务");
        try {
            final String taskTableName = "ac_f_order_invoice";
            List<AcFOrderInvoice> invoiceList = orderInvoiceMapper.selectInInvoiceList(range * 24, taskTableName);
            if(CollectionUtils.isNotEmpty(invoiceList)) {
                Stopwatch started = Stopwatch.createStarted();
                log.debug("---| 开始执行开票结果获取任务,执行条数:"+invoiceList.size());
                executeGetResult(invoiceList);
                long elapsed = started.elapsed(TimeUnit.MILLISECONDS);
                log.info("---| 获取开票结果结束,执行条数:"+invoiceList.size()+",执行时间:"+elapsed);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderInvoiceGetResultTaskService.Execute Error：{}", threadPoolName, "OrderInvoiceGetResultTaskService"), Throwables.getStackTraceAsString(ex));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    public void executeGetResult(List<AcFOrderInvoice> invoiceList){
        if(CollectionUtils.isEmpty(invoiceList)){
            return;
        }
        User user = SystemUserResource.getRootUser();
        for (AcFOrderInvoice invoice : invoiceList) {
            JsonRootObject param = new JsonRootObject();
            param.setDocNum(invoice.getBillNo());
            ResultType resultType = hubInvoicingCmd.queryInvoicing(param);
            log.info("---| tid:[" + invoice.getTid() + "],获取开票结果入参:" + JSON.toJSONString(param) + ",         获取开票结果出参:" + JSON.toJSONString(resultType));
            UpdateWrapper<AcFOrderInvoice> update = new UpdateWrapper<>();
            LambdaUpdateWrapper<AcFOrderInvoice> lambdaUpdate = update.lambda();
            lambdaUpdate.eq(AcFOrderInvoice::getId, invoice.getId());

            /*默认-老版本的返回信息*/
            String logMessage = resultType.getMsg();
            if (InvoiceConst.getInvoiceResultApiCode.SUCCESS.equals(resultType.getCode())) {
                /*老版本-成功*/
                logMessage = invoiceResultSuccessV1(invoice, resultType, update, lambdaUpdate);
            } else if ("200".equals(resultType.getCode())) {
                /*新版本-成功*/
                logMessage = invoiceResultSuccessV2(invoice, resultType, update, lambdaUpdate);
            } else if (InvoiceConst.getInvoiceResultApiCode.FAIL.equals(resultType.getCode())) {
                /*老版本-失败*/
                logMessage = invoiceResultFailV1(user, invoice, resultType, update, lambdaUpdate, logMessage);
                if (logMessage == null) {
                    continue;
                }
            } else {
                /*其他-新版本-失败「新版本的失败码：500」*/
                logMessage = invoiceResultFailV2(user, invoice, resultType, update, lambdaUpdate, logMessage);
                if (logMessage == null) {
                    continue;
                }
            }

            logService.addUserOrderLog(invoice.getId(), "获取开票结果", logMessage, user);
        }
    }

    private String invoiceResultFailV1(User user, AcFOrderInvoice invoice, ResultType resultType,
                                       UpdateWrapper<AcFOrderInvoice> update, LambdaUpdateWrapper<AcFOrderInvoice> lambdaUpdate,
                                       String logMessage) {
        if (StringUtils.isNotBlank(resultType.getMsg()) && resultType.getMsg().contains("暂未")) {
            logService.addUserOrderLog(invoice.getId(), "获取开票结果", logMessage, user);
            return null;
        }
        // 开票失败,更新开票状态,失败原意
        lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
        if (Objects.nonNull(resultType.getData())) {
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, resultType.getData().getMsg());
        } else {
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, resultType.getMsg());
        }
        logMessage = "获取开票结果失败:" + resultType.getMsg();
        orderInvoiceMapper.update(null, update);
        return logMessage;
    }

    private String invoiceResultFailV2(User user, AcFOrderInvoice invoice, ResultType resultType,
                                       UpdateWrapper<AcFOrderInvoice> update, LambdaUpdateWrapper<AcFOrderInvoice> lambdaUpdate,
                                       String logMessage) {
        if (StringUtils.isNotBlank(resultType.getMessage()) && resultType.getMessage().contains("暂未")) {
            logService.addUserOrderLog(invoice.getId(), "获取开票结果", logMessage, user);
            return null;
        }

        // 开票失败,更新开票状态,失败原意
        lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
        if (Objects.nonNull(resultType.getResult())) {
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, resultType.getResult().getMsg());
        } else {
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, resultType.getMessage());
        }
        logMessage = "获取开票结果失败:" + resultType.getMessage();
        orderInvoiceMapper.update(null, update);
        return logMessage;
    }


    private String invoiceResultSuccessV1(AcFOrderInvoice invoice,
                                          ResultType resultType,
                                          UpdateWrapper<AcFOrderInvoice> update,
                                          LambdaUpdateWrapper<AcFOrderInvoice> lambdaUpdate) {
        String logMessage;
        DataType data = resultType.getData();
        /*老版本接口，内层还有一次code的判断*/
        if (InvoiceConst.getInvoiceResultApiCode.SUCCESS.equals(data.getCode())) {
            // 开票成功,更新开票状态,发票代码,发票号码,开票时间,发票url
            try {
                lambdaUpdate.set(AcFOrderInvoice::getInvoiceDate, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(data.getBillGdate()));
            } catch (Exception e) {
                log.error("invoice get result parse date error : " + invoice.getTid());
                lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
                lambdaUpdate.set(AcFOrderInvoice::getFailReason, "parse invoice date error! [" + data.getBillGdate() + "]");
            }
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_SUCCESS);
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceCode, data.getGoldtaxCode());
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceNumber, data.getGoldtaxNum());
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceLinkAddress, data.getEPdfUrl());
            String eXmlUrl = data.geteXmlUrl() == null ? "" : data.geteXmlUrl();
            lambdaUpdate.set(AcFOrderInvoice::getEXmlUrl, eXmlUrl);
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, "");
            logMessage = "获取开票结果成功";
            // 将原蓝票更新为已红冲,并且将发票申请明细tid后缀修改,防止无法继续申请发票
            if (InvoiceConst.TicketType.RED.equals(invoice.getTicketType()) && Objects.nonNull(invoice.getBlueTicketId())) {
                UpdateWrapper<AcFOrderInvoice> blueInvoiceUpdate = new UpdateWrapper<>();
                blueInvoiceUpdate.lambda().eq(AcFOrderInvoice::getId, invoice.getBlueTicketId()).set(AcFOrderInvoice::getRedRushStatus, InvoiceConst.RedRushStatus.RED_RUSHED);
                orderInvoiceMapper.update(null, blueInvoiceUpdate);
                Long applyId = invoice.getInvoiceApplyId();
                if (Objects.nonNull(applyId) && applyId.compareTo(0L) > 0) {
                    invoiceApplyService.updateInvoiceApplyItemTidSuffix(applyId);
                }
            }

        } else {
            String msg = data.getMsg();
            // 开票失败,更新开票状态,失败原意
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, msg);
            logMessage = "获取开票结果失败:" + data.getMsg();
        }
        orderInvoiceMapper.update(null, update);
        return logMessage;
    }

    private String invoiceResultSuccessV2(AcFOrderInvoice invoice,
                                          ResultType resultType,
                                          UpdateWrapper<AcFOrderInvoice> update,
                                          LambdaUpdateWrapper<AcFOrderInvoice> lambdaUpdate) {
        String logMessage;
        ResultData data = resultType.getResult();
        // 开票成功,更新开票状态,发票代码,发票号码,开票时间,发票url
        try {
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceDate, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(data.getBillGdate()));
        } catch (Exception e) {
            log.error("invoice get result parse date error : " + invoice.getTid());
            lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
            lambdaUpdate.set(AcFOrderInvoice::getFailReason, "parse invoice date error! [" + data.getBillGdate() + "]");
        }
        lambdaUpdate.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_SUCCESS);
        lambdaUpdate.set(AcFOrderInvoice::getInvoiceCode, data.getGoldtaxCode());
        lambdaUpdate.set(AcFOrderInvoice::getInvoiceNumber, data.getGoldtaxNum());
        lambdaUpdate.set(AcFOrderInvoice::getInvoiceLinkAddress, data.getPdfUrl());
        String eXmlUrl = data.getXmlUrl() == null ? "" : data.getXmlUrl();
        lambdaUpdate.set(AcFOrderInvoice::getEXmlUrl, eXmlUrl);
        lambdaUpdate.set(AcFOrderInvoice::getFailReason, "");
        logMessage = "获取开票结果成功";
        // 将原蓝票更新为已红冲,并且将发票申请明细tid后缀修改,防止无法继续申请发票
        if (InvoiceConst.TicketType.RED.equals(invoice.getTicketType()) && Objects.nonNull(invoice.getBlueTicketId())) {
            UpdateWrapper<AcFOrderInvoice> blueInvoiceUpdate = new UpdateWrapper<>();
            blueInvoiceUpdate.lambda().eq(AcFOrderInvoice::getId, invoice.getBlueTicketId()).set(AcFOrderInvoice::getRedRushStatus, InvoiceConst.RedRushStatus.RED_RUSHED);
            orderInvoiceMapper.update(null, blueInvoiceUpdate);
            Long applyId = invoice.getInvoiceApplyId();
            if (Objects.nonNull(applyId) && applyId.compareTo(0L) > 0) {
                invoiceApplyService.updateInvoiceApplyItemTidSuffix(applyId);
            }
        }
        orderInvoiceMapper.update(null, update);
        return logMessage;
    }

    /**
     * 红冲发票开票成功后将原蓝票对应的申请明细平台单号增加一个后缀
     *
     * @param applyId
     */
    public void updateSuffixByApplyId(Long applyId) {
        if (Objects.isNull(applyId)) {
            return;
        }
        AcFInvoiceApply apply = invoiceApplyMapper.selectById(applyId);
        if(Objects.isNull(apply)){
            return;
        }
        List<AcFInvoiceApplyItem> list = invoiceApplyItemMapper.selectByApplyId(apply.getId());
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(AcFInvoiceApplyItem item : list){
            UpdateWrapper<AcFInvoiceApplyItem> update = new UpdateWrapper<>();
            update.lambda().eq(AcFInvoiceApplyItem::getId,item.getId()).set(AcFInvoiceApplyItem::getTid,item.getTid()+"_"+System.currentTimeMillis());
            invoiceApplyItemMapper.update(null, update);
        }
    }
}
