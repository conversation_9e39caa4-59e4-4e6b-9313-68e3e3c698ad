package com.jackrain.nea.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 删除对等换货策略明细请求
 **/
@Data
public class StCEquityBarterStrategyItemDeleteRequest {

    /**
     * 店铺类型：1-指定店铺；2-所选指定店铺
     */
    private Integer shopType;

    /**
     * 店铺id的list(当店铺类型为指定店铺时必填)
     */
    private List<Long> shopIds;

    /**
     * 换货商品id
     */
    private Long exchangeSkuId;

    /**
     * 换货数量
     */
    private BigDecimal exchangeQty;

    /**
     * 对等商品id
     */
    private Long equitySkuId;

    /**
     * 对等数量
     */
    private BigDecimal equityQty;
}
