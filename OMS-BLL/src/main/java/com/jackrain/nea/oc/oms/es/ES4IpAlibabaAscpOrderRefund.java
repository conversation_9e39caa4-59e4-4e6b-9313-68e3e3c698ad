package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.AlibabaAscpOrderExt;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 猫超退单主表
 *
 * <AUTHOR>
 * @date 2020/11/11 1:18 下午
 */
public class ES4IpAlibabaAscpOrderRefund {

    private ES4IpAlibabaAscpOrderRefund() {
    }

    /**
     * 业务：猫超直发退货转换Task
     * 根据状态查询 逆向退货单号biz_order_code
     *
     * @param pageIndex 起始页
     * @param pageSize 每夜条数
     * @return List biz_order_code
     */
    public static List<String> findBizOrderCodeByTransStatus(int pageIndex, int pageSize) {

        List<String> bizOrderCodes = new ArrayList<>();

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject whereKeys = new JSONObject();
        String[] returnFields = {"BIZ_ORDER_CODE"};
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());

        JSONObject search = ElasticSearchUtil.search(AlibabaAscpOrderExt.TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_REFUND,
                AlibabaAscpOrderExt.TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_REFUND, whereKeys, null,
                null, pageSize, startIndex, returnFields);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                bizOrderCodes.add(jsonObject.getString("BIZ_ORDER_CODE"));
            }
        }
        if (CollectionUtils.isNotEmpty(bizOrderCodes)) {
            bizOrderCodes = bizOrderCodes.stream()
                    .filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
        }
        return bizOrderCodes;
    }
}
