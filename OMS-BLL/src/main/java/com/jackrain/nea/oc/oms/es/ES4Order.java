package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.AutoAuditStatus;
import com.jackrain.nea.oc.oms.model.enums.AutoSplitStatus;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.result.MergeEsHavingcountResult;
import com.jackrain.nea.oc.oms.model.result.MergeOrderEsResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME;
import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/11/2
 */
@Slf4j
public class ES4Order {

    private ES4Order() {
    }

    /**
     * 平台单号信息-1: 默认取第一条
     * 1. sourceCode 2. tb_dispute_id 3. oc_b_return_order.tid
     *
     * @return order id
     */
    public static Long getIdBySourceCode(String sourceCode) {

        JSONArray ary = queryIdBySourceCode(sourceCode);
        if (ary == null || ary.size() < 1) {
            return null;
        }
        return ary.getJSONObject(0).getLong("ID");
    }


    public static Long getIdByBillNo(String billNo) {

        JSONArray ary = queryIdByKey("BILL_NO", billNo);
        if (ary == null || ary.size() < 1) {
            return null;
        }
        return ary.getJSONObject(0).getLong("ID");
    }

    /**
     * 平台单号信息-2
     *
     * @param sourceCode 1.sourceCode 2. tb_dispute_id
     * @return ids
     */
    public static List<Long> getIdsBySourceCode(Object sourceCode) {

        JSONArray ary = queryIdBySourceCode(sourceCode);
        return statisticsResult(ary, "ID");

    }


    /**
     * 原始退单编号
     *
     * @param origReturnOrderId 原始退货单号
     * @return List 订单编号
     */
    public static List<Long> getIdsByOrigReturnOrderId(Long origReturnOrderId) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ORIG_RETURN_ORDER_ID", origReturnOrderId);
        whereKeys.put("ORDER_STATUS", "!=" + OmsOrderStatus.SYS_VOID.toInteger());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER,
                whereKeys, null, null, 10, 0, new String[]{"ID"});

        JSONArray ary = search.getJSONArray("data");
        return statisticsResult(ary, "ID");
    }

    /**
     * 平台单号信息-0
     *
     * @param sourceCode 合并后发货的订单号 1. sourceCode 2. tb_dispute_id
     * @return es search result
     */
    public static JSONArray queryIdBySourceCode(Object sourceCode) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_CODE", sourceCode);
        JSONObject result = ElasticSearchUtil.search(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, null, 30, 0, new String[]{"ID"});
        return result.getJSONArray("data");
    }


    /**
     * @param val val
     * @return es search result
     */
    public static JSONArray queryIdByKey(String key, Object val) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put(key, val);
        JSONObject result = ElasticSearchUtil.search(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, null, 30, 0, new String[]{"ID"});
        return result.getJSONArray("data");
    }

    /**
     * 根据物流单号
     *
     * @param logisticsNum 物流单号
     * @return ids
     */
    public static List<Long> queryIdsByLogisticsCode(String logisticsNum) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("EXPRESSCODE", logisticsNum);
        return searchOrder4RefundMatch(whereKeys);
    }

    /**
     * 收货人手机号码
     *
     * @param mobile 手机号码
     * @return ids
     */
    public static List<Long> queryIdsByReceiveMobile(String mobile) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RECEIVER_MOBILE", mobile);
        return searchOrder4RefundMatch(whereKeys);
    }

    /**
     * @param whereKeys 物流. 手机
     * @return 订单编号集
     */
    private static List<Long> searchOrder4RefundMatch(JSONObject whereKeys) {
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", false);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);

        JSONObject filterKey = new JSONObject();
        JSONArray statusAry = new JSONArray();
        statusAry.add(OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal());
        statusAry.add(OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal());
        statusAry.add(OcOrderCheckBoxEnum.CHECKBOX_TRANSACTION_COMPLETED.getVal());
        whereKeys.put("ORDER_STATUS", statusAry);

        filterKey.put("SCAN_TIME", Timestamp.valueOf(LocalDateTime.now().minusMonths(3)).getTime() + "~");

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, whereKeys, filterKey, orderJAry,
                100, 0, new String[]{"ID"});
        JSONArray returnIdAry = search.getJSONArray("data");
        return ES4Order.statisticsResult(returnIdAry, "ID");
    }


    /**
     * 商品明细ID-0
     * 主,子查询
     *
     * @param id 订单商品id
     * @return 订单id
     */
    public static Long getIdByItemId(Long id) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ID", id);
        JSONObject search = ElasticSearchUtil.search(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_ITEM_TYPE_NAME,
                whereKeys, null, null, 30, 0, new String[]{"OC_B_ORDER_ID"});

        JSONArray jsnAry = search.getJSONArray("data");
        if (jsnAry != null && jsnAry.size() > 0) {
            return jsnAry.getJSONObject(0).getLong("OC_B_ORDER_ID");
        }
        return null;
    }

    /**
     * 商品明细OOID
     * 主子查询
     *
     * @param oOid 子订单编号(明细编号)
     * @return JSONArray
     */
    public static JSONArray getIdsByItemOoId(Long oOid) {

        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OOID", oOid);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, 30, 0, returnFields);
        return search.getJSONArray("data");
    }

    /**
     * 多个ooid一起查询
     *
     * @param array
     * @return
     */
    public static JSONArray getIdsByItemsOoId(JSONArray array) {

        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OOID", array);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, 30, 0, returnFields);
        return search.getJSONArray("data");
    }

    /***
     * 订单导入 判断订单是否存在,查看是否存在重复数据
     * @param ocBOrderList
     * @return
     */
    public static Boolean isExist(List<OcBOrderExtend> ocBOrderList) {
        HashMap<String, Set<String>> isExistMap = Maps.newHashMap();
        StringBuilder sb = new StringBuilder();
        ocBOrderList.forEach(current -> {
            String sourceCode = current.getSourceCode();
            sb.append(current.getCpCShopTitle())
                    .append(current.getReceiverName())
                    .append(current.getReceiverMobile())
                    .append(current.getReceiverAddress())
                    .append("-Repeat-").append(sourceCode);
            if (isExistMap.containsKey(sourceCode)) {
                isExistMap.get(sourceCode).add(sb.toString());
                sb.setLength(0);
            } else {
                isExistMap.put(sourceCode, Sets.newHashSet(sb.toString()));
                sb.setLength(0);
            }

        });
        for (Map.Entry<String, Set<String>> entry : isExistMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                return true;
            }
        }
        String[] returnFileds = {"ID"};
        JSONObject whereKeys = new JSONObject();
        JSONArray objects = new JSONArray();
        objects.addAll(isExistMap.keySet());
        whereKeys.put("SOURCE_CODE", objects);
        //根据平台单号ES查询订单id
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER,
                whereKeys, null, null, 1000, 0, returnFileds);
        return search.containsKey("rowcount") && search.getInteger("rowcount") > 0;
    }

    /**
     * 统计查询结果
     *
     * @param ary       查询结果
     * @param returnKey 返回标识字段
     * @return list
     */
    public static List<Long> statisticsResult(JSONArray ary, String returnKey) {
        if (ary == null || ary.size() < 1) {
            return null;
        }
        List<Long> list = new ArrayList<>();
        for (int i = 0, l = ary.size(); i < l; i++) {
            Long id = ary.getJSONObject(i).getLong(returnKey);
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list.size() > 0 ? list : null;
    }

    /**
     * 根据 oOid 查询 订单编号
     * 主、子查询
     *
     * @param oOid 子订单编号(明细编号)
     * @return List<Long>  订单编号(oc_b_order_id)
     */
    public static List<Long> findIdsByOid(Object oOid) {
        //原换货单id
        List<Long> oriOrderIdList = new ArrayList<>();
        //根据平台换货单号找到所有的明细
        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OOID", oOid);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object returnDatum : returnData) {
                JSONObject jsonObject = (JSONObject) returnDatum;
                Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
                oriOrderIdList.add(orderId);
            }
        }
        return oriOrderIdList;
    }

    /**
     * 根据 合包码查询 订单编号
     * 主、子查询
     *
     * @return List<Long>  订单编号(oc_b_order_id)
     */
    public static List<Long> findIdsByMergedCode(OcBOrder order) {
        //原换货单id
        List<Long> oriOrderIdList = new ArrayList<>();
        //
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("MERGED_CODE", order.getMergedCode());
        whereKeys.put("CP_C_SHOP_ID", order.getCpCShopId());
        whereKeys.put("CP_C_PHY_WAREHOUSE_ID", order.getCpCPhyWarehouseId());
        //待审核 待分配
        JSONArray statusArray = new JSONArray();
        statusArray.add(OmsOrderStatus.UNCONFIRMED.toInteger());
        statusArray.add(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        whereKeys.put("ORDER_STATUS", statusArray);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object returnDatum : returnData) {
                JSONObject jsonObject = (JSONObject) returnDatum;
                Long orderId = jsonObject.getLong("ID");
                oriOrderIdList.add(orderId);
            }
        }
        return oriOrderIdList;
    }

    /**
     * 业务：页面查询
     * 根据修改时间范围过滤查询订单编号
     * 主、子查询
     *
     * @param modifiedDate 修改时间
     * @return List<Long>  订单编号(oc_b_order_id)
     */
    public static List<Long> findIdByModifiedDate(String modifiedDate) {
        // es查询订单明细表返回数据
        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("MODIFIEDDATE", modifiedDate);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM,
                whereKeys, filterKeys, null, 1000, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isEmpty(returnData)) {
            return null;
        }
        List<Long> list = new ArrayList<>();
        for (int i = 0, l = returnData.size(); i < l; i++) {
            JSONObject jsonObject = returnData.getJSONObject(i);
            Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
            list.add(orderId);
        }
        return list;
    }

    /**
     * 业务：页面查询
     * 根据取消状态分页查询 订单编号
     * 主、子查询
     *
     * @param refundStatus 取消状态(0,1:否,6:是)
     * @param page         当前页码
     * @param size         每页现实条数
     * @return List<Long> 订单编号(oc_b_order_id)
     */
    public static List<Long> findIdByRefundStatusByPagination(int refundStatus, int page, int size) {
        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("REFUND_STATUS", refundStatus);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, size, page, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isEmpty(returnData)) {
            return null;
        }
        List<Long> ids = new ArrayList<>();
        for (int i = 0, l = returnData.size(); i < l; i++) {
            JSONObject jsonObject = returnData.getJSONObject(i);
            Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
            ids.add(orderId);
        }
        return ids;
    }

    /**
     * 根据 tid 查询 订单编号
     * 主、子查询
     *
     * @param tid 平台单号
     * @return Set<Long> 订单编号(oc_b_order_id)
     */
    public static Set<Long> findIdsByTid(Object tid) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TID", tid);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                Long orderId = returnData.getJSONObject(i).getLong("OC_B_ORDER_ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 业务查询：通用退单
     * 根据 oOid 或 tid 查询 订单编号
     * 主子、查询
     *
     * @param subOrderIds 子订单编号(明细编号)
     * @param tid         平台单号
     * @return Set<Long> 订单编号(oc_b_order_id)
     */
    public static Set<Long> findIdByItemOidOrTid(Set<String> subOrderIds, Object tid) {
        Set<Long> ids = new HashSet<>(50);
        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        if (CollectionUtils.isNotEmpty(subOrderIds)) {
            JSONArray ooIds = new JSONArray();
            ooIds.addAll(subOrderIds);
            whereKeys.put("OOID", ooIds);
        } else {
            whereKeys.put("TID", tid);
        }

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                Long orderId = returnData.getJSONObject(i).getLong("OC_B_ORDER_ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 业务查询
     * 根据 oOid、isGift 查询 订单编号
     * 主、子查询
     *
     * @param oOid   子订单编号(明细编号)
     * @param isGift 是否是赠品
     * @return Set<Long> 订单编号(oc_b_order_id)
     */
    public static Set<Long> findIdByOidAndIsGift(Object oOid, Integer isGift) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"OC_B_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OOID", oOid);
        whereKeys.put("IS_GIFT", isGift);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDERITEM, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                Long orderId = returnData.getJSONObject(i).getLong("OC_B_ORDER_ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 业务：页面查询
     * 根据 qtySplit 查询 id
     *
     * @param qtySplit 拆单次数
     * @param pageSize 页面显示条数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByQtySplitAndPage(Integer qtySplit, Integer pageSize) {
        List<Long> ids = new ArrayList<>();
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("QTY_SPLIT", qtySplit);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER, whereKeys, null,
                null, pageSize, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object obj : returnData) {
                JSONObject jsonObject = (JSONObject) obj;
                Long id = jsonObject.getLongValue("ID");
                if (id > 0) {
                    ids.add(id);
                }
            }
        }
        return ids;
    }

    /**
     * 业务查询 根据tid、orderType 查询 id
     *
     * @param tid       初始平台单号（确定唯一）
     * @param orderType 订单类型
     * @return Set<Long> id 订单编号
     */
    public static Set<Long> findIdByTidAndOrderType(Object tid, Integer orderType) {
        Set<Long> ids = new HashSet<>(50);
        String[] returnFields = new String[]{"ID"};
        JSONObject whereKeysByOrder = new JSONObject();
        //根据平台单号且订单类型非虚拟订单
        whereKeysByOrder.put("TID", tid);
        whereKeysByOrder.put("ORDER_TYPE", "!=" + orderType);

        JSONObject searchByOrder = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER, whereKeysByOrder, null,
                null, 50, 0, returnFields);

        JSONArray returnData = searchByOrder.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0, l = returnData.size(); i < l; i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 业务查询
     * 根据 sourceCode、orderDate(过滤条件) 查询 id
     *
     * @param sourceCodes 平台单号信息
     * @param outDate     下单时间
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdsBySourceCodeAndFilterByOrderDate(List<String> sourceCodes, String outDate) {
        JSONArray statusArray = new JSONArray();
        String[] returnFields = {"ID"};

        statusArray.addAll(sourceCodes);

        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("ORDER_DATE", "~" + outDate);
        whereKeys.put("SOURCE_CODE", statusArray);

        // 根据平台单号ES查询订单id
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER,
                whereKeys, null, null, 1000, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");

        List<Long> orderIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0, l = returnData.size(); i < l; i++) {
                Long id = returnData.getJSONObject(i).getLong("ID");
                orderIds.add(id);
            }
        }
        return orderIds;
    }

    /**
     * 业务：JitX订单审核补偿任务
     * 根据 orderStatus(订单状态)、orderType(订单类型)、isIntercept(是否已经拦截)、
     * platform(平台类型)、autoAuditStatus(自动审核状态)、创建时间(creationDate)排序查询 id
     *
     * @param pageIndex 页码
     * @param pageSize  每页显示条数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findJitXIdsByPagination(int pageIndex, int pageSize) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED);
        whereKey.put("ORDER_TYPE", "!=" + OrderTypeEnum.EXCHANGE.getVal());
        whereKey.put("IS_INTERECEPT", "0");
        whereKey.put("PLATFORM", PlatFormEnum.VIP_JITX.getCode());
        //查询自动审核状态为1[审核失败]的订单and自动审核状态为3但是更新时间间隔大于3600s的数据
        JSONArray ja = new JSONArray();
        ja.add(AutoAuditStatus.Audit_TRAN.toInteger());
        ja.add(AutoAuditStatus.Audit_FAIL.toInteger());
        whereKey.put("AUTO_AUDIT_STATUS", ja);

        List<Long> orderList = new ArrayList<>();

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("name", "CREATIONDATE");
        orderKey.put("asc", false);
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKey, null, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

    /**
     * 业务：自动审核订单补偿定时任务（OrderType=非换货订单）
     * 根据 orderStatus(订单状态),orderType(订单类型),
     * IsIntercept(是否已经拦截),autoAuditStatus(自动审核状态),
     * modifiedDate(过滤条件) 查询 id, 按creationDate(排序条件)排序
     *
     * @param pageIndex       页码
     * @param pageSize        每页条数
     * @param makeupAuditTime 时间差值 默认60分钟
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByOrderStatusAndTypeAndIntercept(int pageIndex, int pageSize, Long makeupAuditTime) {

        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKey.put("ORDER_TYPE", "!=" + OrderTypeEnum.EXCHANGE.getVal());
        whereKey.put("IS_INTERECEPT", "0");
        //查询自动审核状态为1[审核失败]的订单and自动审核状态为3但是更新时间间隔大于3600s的数据
        JSONArray ja = new JSONArray();
        ja.add(AutoAuditStatus.Audit_TRAN.toInteger());
        ja.add(AutoAuditStatus.Audit_FAIL.toInteger());
        whereKey.put("AUTO_AUDIT_STATUS", ja);

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //计算时间差值[1分(min)=60000毫秒(ms)]
        Long value = System.currentTimeMillis() - makeupAuditTime * 60000;
        JSONObject filterKeys = new JSONObject();
        //大于3600s
        filterKeys.put("MODIFIEDDATE", "~" + value);

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKey, filterKeys, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }


    /**
     * 业务：虚拟订单传wing定时任务（OrderType=虚拟||虚拟定金）
     * 根据 orderStatus(订单状态),orderType(订单类型),
     *
     * @param pageIndex 页码
     * @param pageSize  每页条数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdVirtualOrder(int pageIndex, int pageSize) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        JSONArray ja = new JSONArray();
        ja.add(OrderTypeEnum.VIRTUAL_DEPOSIT.getVal());
        ja.add(OrderTypeEnum.DIFFPRICE.getVal());
        whereKey.put("ORDER_TYPE", ja);
        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("TO_DRP_COUNT", "~" + 5);

        JSONArray toDrpSatus = new JSONArray();
        toDrpSatus.add(ToDRPStatusEnum.NOT.getCode());
        toDrpSatus.add(ToDRPStatusEnum.FAIL.getCode());
        whereKey.put("TO_DRP_STATUS", toDrpSatus);

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKey, null, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 业务：自动审核退换货订单补偿定时任务（OrderType=换货订单）
     * 根据 orderStatus(订单状态),orderType(订单类型),
     * IsIntercept(是否已经拦截),autoAuditStatus(自动审核状态),
     * modifiedDate(过滤条件) isExchangeNoIn(换货已入库状态)
     * 查询 id, 按creationDate(排序条件)排序
     *
     * @param pageIndex       页码
     * @param pageSize        每页条数
     * @param makeupAuditTime 默认60分钟 条件：换货类型且已入库
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByStatusAndTypeAndInterceptAndInStorage(int pageIndex, int pageSize,
                                                                           Long makeupAuditTime) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKey.put("ORDER_TYPE", OrderTypeEnum.EXCHANGE.getVal());
        whereKey.put("IS_EXCHANGE_NO_IN", 0);//换货已入库
        whereKey.put("IS_INTERECEPT", "0");
        //查询自动审核状态为1[审核失败]的订单and自动审核状态为3[审核中]但是更新时间间隔大于3600s的数据
        JSONArray ja = new JSONArray();
        ja.add(AutoAuditStatus.Audit_TRAN.toInteger());
        ja.add(AutoAuditStatus.Audit_FAIL.toInteger());
        whereKey.put("AUTO_AUDIT_STATUS", ja);

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //计算时间差值[1分(min)=60000毫秒(ms)]
        Long value = System.currentTimeMillis() - makeupAuditTime * 60000;
        JSONObject filterKeys = new JSONObject();
        //大于3600s
        filterKeys.put("MODIFIEDDATE", "~" + value);

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKey, filterKeys, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 业务：占单服务定时任务
     * 根据 orderStatus(订单状态)、occupyStatus(订单占单状态)
     * 按 payTime 排序查询 id
     *
     * @param pageIndex 页码
     * @param pageSize  查询条数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdsByOrderStatusAndOccupyStatus(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        // whereKeys.put("IS_INTERECEPT", "0");
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "MODIFIEDDATE");
        orderKes.put("asc", true);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);
        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, orderKeys, pageSize, pageIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }


    /**
     * <AUTHOR>
     * @Date 11:37 2021/7/17
     * @Description 缺货重新寻源占单
     */
    public static List<Long> findIdsByOrderStatusAndOutStockOccupyStatus(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        // whereKeys.put("IS_INTERECEPT", "0");
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "MODIFIEDDATE");
        orderKes.put("asc", true);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);
        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, orderKeys, pageSize, pageIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

    /**
     * 业务：订单传wms的补偿任务
     * 根据 orderStatus(订单状态), auditTime(过滤条件:审核时间)
     * 按 creationDate(创建时间)排序 查询 id
     *
     * @param pageIndex 页码
     * @param pageSize  查询条数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByOrderStatusAndAuditTime(int pageIndex, int pageSize) {
        //获取当前时间之前的一个小时的毫秒数
        Long hourDate = 60 * 1000 * 60L;
        Long dayDate = 60 * 1000 * 60L * 24;
        long endDate = System.currentTimeMillis() - hourDate;
        long startDate = System.currentTimeMillis() - dayDate;

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.PENDING_WMS.toInteger());
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("AUDIT_TIME", startDate + "~" + endDate);

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, filterKeys, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }


    /**
     * 根据 sourceCode 或 shopId 查询 id
     *
     * @param sourceCode 平台单号信息
     * @param shopId     下单店铺id
     * @param isStanPlat 是否添加 下单店铺id 查询标识
     * @return List<Long> id 订单编号
     */
    public static List<Long> findBySourceCodeOrShopId(String sourceCode, Long shopId, boolean isStanPlat) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_CODE", "*" + sourceCode + "*");
        if (isStanPlat) {
            whereKeys.put("CP_C_SHOP_ID", shopId);
        }
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, null,
                100, 0, new String[]{"ID"});

        List<Long> ids = new ArrayList<>(search.size());

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");

            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                Long id = jsonObject.getLongValue("ID");
                ids.add(id);
            }
        }
        return ids;
    }

    /**
     * 业务：查询自动拆单状态为2【拆单中】的订单，且更新时间间隔大于3600s的数据
     * 根据 orderStatus(订单状态)、isIntercept(是否已经拦截)、isSplit(是否拆分订单)、
     * modifiedDate(过滤条件) 按 creationDate(创建时间) 排序 查询 id
     *
     * @param pageIndex            页码
     * @param pageSize             查询记录数
     * @param autoReleaseSplitTime 更新时间间隔
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByOrderStatusAndIsIntercept(int pageIndex, int pageSize, Long autoReleaseSplitTime) {

        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        whereKey.put("IS_INTERECEPT", "0");
        //查询自动拆单状态为2【拆单中】的订单，且更新时间间隔大于3600s的数据
        JSONArray ja = new JSONArray();
        ja.add(AutoSplitStatus.TRAN_SPLIT.toInteger());
        whereKey.put("IS_SPLIT", ja);
        List<Long> orderList = new ArrayList<>();

        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //计算时间差值[1分(min)=60000毫秒(ms)]
        Long value = System.currentTimeMillis() - autoReleaseSplitTime * 60000;
        JSONObject filterKeys = new JSONObject();
        //大于3600s
        filterKeys.put("MODIFIEDDATE", "~" + value);

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKey, filterKeys, orderKeys, pageSize, startIndex, new String[]{"ID"});

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 根据 sgPhyOutResultBillNo 查询订单id
     *
     * @param sgPhyOutResultBillNo 出库通知单号
     * @return id 订单编号
     */
    public static Long findIdBySgPhyOutResultBillNo(Object sgPhyOutResultBillNo) {
        Long id = null;
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SG_B_OUT_BILL_NO", sgPhyOutResultBillNo);
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, null, 100, 0, new String[]{"ID"});

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray data = search.getJSONArray("data");

            id = ((JSONObject) data.get(0)).getLong("ID");
        }
        return id;
    }

    /**
     * 业务：订单平台发货服务，补偿服务
     * 根据 orderStatus(订单状态),isForce(强制平台货标记),orderSource(订单来源)
     * makeUpFailNum(平台发货补偿失败次数 过滤条件) 按 makeUpFailNum 排序 查询 id
     *
     * @param failNum   平台发货补偿失败次数
     * @param pageIndex 页码
     * @param pageSize  查询记录数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdsByOrderStatusAndSourceAndForce(Integer failNum, int pageIndex, int pageSize, Long platformId) {
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        JSONArray forceStatus = new JSONArray();
        forceStatus.add(0);
        forceStatus.add(2);
        //失败次数小于5次 平台发货补偿失败次数
        filterKeys.put("MAKEUP_FAIL_NUM", "~" + failNum);
        //查订单状态为仓库发货的
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        //失败和初始状态
        whereKeys.put("IS_FORCE", forceStatus);
        if (ObjectUtils.isNotEmpty(platformId)) {
            whereKeys.put("PLATFORM", platformId);
        }

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();

        // @20200913 youhua#dingding-excel AutoMakeUpPlaformDeliveryTask效率优化 - 2 - 查询排序

        // MODIFIEDDATE 修改日期升序排序
        JSONObject orderKeyCreationDate = new JSONObject();
        orderKeyCreationDate.put("asc", true);
        orderKeyCreationDate.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKeyCreationDate);

        // MAKEUP_FAIL_NUM 错误次数升序排序
        JSONObject orderKeyMakeUpFailNum = new JSONObject();
        orderKeyMakeUpFailNum.put("asc", true);
        orderKeyMakeUpFailNum.put("name", "MAKEUP_FAIL_NUM");
        orderKeys.add(orderKeyMakeUpFailNum);

        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, filterKeys, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 公共交互ES拉取订单Id集合  ***根据查询条件多处调用，待后续抽离优化***
     *
     * @param whereKeyJson 组装条件json
     * @param pageIndex    初始页面index
     * @param pageSize     页面数据大小
     * @return List<Long> id 订单编号
     */
    public static List<Long> queryEsOrderList(JSONObject whereKeyJson, int pageIndex, int pageSize) {
        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        //压测 先把排序去掉1016
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeyJson, null, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 同上，只是添加了过滤条件
     *
     * @param whereKeyJson  组装条件json
     * @param filterKeyJson 过滤条件json
     * @param pageIndex     初始页面index
     * @param pageSize      页面数据大小
     * @return List<Long> id 订单编号
     */
    public static List<Long> queryEsOrderList(JSONObject whereKeyJson, JSONObject filterKeyJson,
                                              int pageIndex, int pageSize) {
        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        //压测 先把排序去掉1016
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeyJson, filterKeyJson, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 业务：全渠道订单处理服务
     * 根据 toSettleStatus(待传结算标志)、orderStatus(订单状态)、platform(平台类型)
     * 按 creationDate (创建时间)排序 查询 id
     *
     * @param pageIndex 初始页面index
     * @param pageSize  页面数据大小
     * @return List<Long> id 订单编号
     */
    public static List<Long> findTBIdByToSettleStatusAndOrderStatus(int pageIndex, int pageSize) {

        JSONObject whereKey = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(ToACStatusEnum.INIT.getValue().longValue());
        jsonArray.add(ToACStatusEnum.PENDING.getValue().longValue());
        jsonArray.add(ToACStatusEnum.FAILED.getValue().longValue());
        whereKey.put("TO_SETTLE_STATUS", jsonArray);
        JSONArray statusJsonArr = new JSONArray();
        statusJsonArr.add(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        statusJsonArr.add(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        whereKey.put("ORDER_STATUS", statusJsonArr);
        whereKey.put("PLATFORM", PlatFormEnum.TAOBAO.getCode());

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        //压测 先把排序去掉1016
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKey, null, orderKeys, pageSize, startIndex, new String[]{"ID"});

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 业务：自动拆单定时任务
     * 根据 查询条件： OrderStatus(订单状态)、Platform(平台类型)、IsIntercept(拦截状态)、IsSplit(自动拆单状态)、PayType(支付方式),
     * 过滤条件：QtySplit(拆单次数)
     * 按 creationDate(创建时间)排序 查询id
     *
     * @param pageIndex 页码
     * @param pageSize  查询记录数
     * @param splitNum  拆单次数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByOrderStatusAndIsInterceptAndIsSplit(int pageIndex, int pageSize, int splitNum) {
        JSONObject whereKeys = new JSONObject();
        //筛选“订单状态”为缺货，且“是否已经拦截”等于否，且“是否为合并订单”为否
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        whereKeys.put("PLATFORM", "!=" + PlatFormEnum.VIP_JITX.getCode());
        whereKeys.put("IS_INTERECEPT", "0");
        JSONObject filterKeys = new JSONObject();

        filterKeys.put("QTY_SPLIT", "~" + splitNum);
        whereKeys.put("IS_SPLIT", "!=" + AutoSplitStatus.TRAN_SPLIT.toInteger());
        whereKeys.put("PAY_TYPE", "!=" + OmsPayType.CASH_ON_DELIVERY.toInteger());

        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        //压测 先把排序去掉1016
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_ORDER_TYPE_NAME,
                whereKeys, filterKeys, orderKeys, pageSize, startIndex, new String[]{"ID"});

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 业务：批量导入
     * 根据 sourceCoe 平台单号信息 查询 id、sourceCode、orderFlag、sellerMemo、billNo
     *
     * @param sourceCode 平台单号信息
     * @return es search result
     */
    public static JSONArray findDataBySourceCode(Object sourceCode) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("SOURCE_CODE", sourceCode);
        String[] param = {"ID", "SOURCE_CODE", "ORDER_FLAG", "SELLER_MEMO", "BILL_NO"};
        int ranger = 10000;
        int start = 0;
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKey,
                null, null, ranger, start, param);
        return search.getJSONArray("data");
    }

    /**
     * 业务：线上资金占用定时任务
     * 根据 查询条件：orderStatus(订单状态)、consignAmtStatus(代销资金处理状态)、
     * 过滤条件：consignAmtRetryFlag(代销资金处理重试标识)
     * 按 modifiedDate(修改时间) 排序 查询 id
     *
     * @param pageIndex 起始位置
     * @param pageSize  每页条数
     * @param num       重试次数
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByOrderStatusAndRetryTimes(int pageIndex, int pageSize, int num) {
        if (true) {
            return new ArrayList<>();
        }
        List<Long> orderIdList = new ArrayList<>();
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ISACTIVE", "Y");
        JSONArray orderStatus = new JSONArray();
        orderStatus.add(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        orderStatus.add(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        // whereKeys.put("ORDER_STATUS", orderStatus);
        //1标识未处理
        // whereKeys.put("CONSIGN_AMT_STATUS", OrderWorehouseIsRetry.NO.getVal());
        JSONObject filterKeys = new JSONObject();
        //重试次数
        filterKeys.put("CONSIGN_AMT_RETRY_FLAG", "~" + num);


        String[] returnFieldNames = new String[]{"ID"};
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKey);
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                whereKeys, filterKeys, orderKeys,
                pageSize, startIndex, returnFieldNames);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");

            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                Long orderId = jsonObject.getLong("ID");
                orderIdList.add(orderId);
            }
        }
        return orderIdList;
    }

    /**
     * 业务：作废订单-淘宝预售-未付尾款
     * 根据orderStatus(订单状态)、statusPayStep(阶段付款状态) 查询id，
     * 按 orderDate(下单时间)排序
     *
     * @param index 起始位置
     * @param size  数量
     * @return List<Long> id 订单编号
     */
    public static List<Long> findIdByOrderStatusAndStatusPayStep(int index, int size) {
        JSONObject whereKey = new JSONObject();
        JSONArray whereAry = new JSONArray();
        whereAry.add(OmsOrderStatus.UNCONFIRMED.toInteger());
        whereAry.add(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        whereKey.put("ORDER_STATUS", whereAry);
        whereKey.put("STATUS_PAY_STEP", TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID);
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", true);
        orderJo.put("name", "ORDER_DATE");
        orderJAry.add(orderJo);
        List<Long> keyList = new ArrayList<>();
        try {

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, whereKey, null, orderJAry, size,
                    index, new String[]{"ID"});
            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray jsnAry = search.getJSONArray("data");
                for (int i = 0, l = jsnAry.size(); i < l; i++) {
                    JSONObject jsn = jsnAry.getJSONObject(i);
                    Long key = jsn.getLong("ID");
                    keyList.add(key);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return keyList;
    }

    /**
     * 根据 查询条件 refundStatus(退款状态)、
     * 过滤条件 modifiedDate(修改时间)
     * 查询 id、 ocBOrderId
     * 主、子查询
     *
     * @return es search result
     */
    public static JSONArray getItemIdsByRfStatusAndFilterByMdDate() {
        JSONObject whereKey = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        whereKey.put("REFUND_STATUS", OcOrderRefundStatusEnum.SUCCESS.getVal());
        // 从ES获取24h内的退款完成明细数据
        long time = System.currentTimeMillis() - 300000;
        filterKeys.put("MODIFIEDDATE", "" + (time - 86400000L) + "~" + time + "");

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, whereKey,
                null, null, 500, 0, new String[]{"ID", "OC_B_ORDER_ID"});
        return search.getJSONArray("data");
    }

    /**
     * 根据查询条件 orderStatus(订单状态)、
     * 过滤条件 modifiedDate(修改时间)
     * 查询 id
     *
     * @return es search result
     */
    public static JSONArray getIdsByOrderStatusAndFilterByMfDate(Integer orderStatus, String modifiedDate, int range) {
        //查询已取消的订单
        JSONObject whereKey = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        whereKey.put("ORDER_STATUS", orderStatus);

        filterKeys.put("MODIFIEDDATE", modifiedDate);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKey, null, null, range, 0, new String[]{"ID"});
        return search.getJSONArray("data");
    }

    /**
     * 根据 orderStatus 查询 id
     *
     * @param orderStatus 订单状态
     * @return es search result
     */
    public static JSONArray getIdsByOrderStatus(Integer orderStatus) {
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        //订单状态为待确认
        whereKeys.put("ORDER_STATUS", orderStatus);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys,
                filterKeys, null, 1000, 0, new String[]{"ID"});

        return search.getJSONArray("data");
    }

    /**
     * tid 分组查询
     *
     * @param creationDate 过滤条件
     * @return es search result
     */
    public static JSONObject findTidGroupByCreationDate(String creationDate) {
        JSONObject filterKeyJo = new JSONObject();
        String[] groups = new String[]{"TID"};
        filterKeyJo.put("CREATIONDATE", creationDate);

        JSONObject objJsonList = ElasticSearchUtil.havingCount(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, null, filterKeyJo, "1~", groups);
        return objJsonList;
    }

    /**
     * 根据 tid 查询 id
     *
     * @param tid 初始平台单号（确定唯一）
     * @return es search result
     */
    public static JSONArray findIdByTid(String tid) {
        JSONObject filterKeyJo = new JSONObject();
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TID", tid);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys,
                filterKeyJo, null, 1000, 0, new String[]{"ID"});

        return search.getJSONArray("data");
    }

    /**
     * 根据 查询条件 orderStatus
     * 过滤条件creationDate 查询 id
     *
     * @param orderStatus  订单状态
     * @param creationDate 创建时间
     * @param range        查询范围
     * @return es search result
     */
    public static JSONObject findIdsByOrderStatusFilterByCreationDate(Integer orderStatus, String creationDate,
                                                                      Integer range) {
        JSONObject whereKey = new JSONObject();
        JSONObject filterKey = new JSONObject();
        filterKey.put("CREATIONDATE", creationDate);
        JSONArray orderKey = new JSONArray();
        Integer start = 0;
        String[] fieldKey = {"ID"};

        whereKey.put("ORDER_STATUS", orderStatus);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKey, filterKey, orderKey, range, start, fieldKey);
        return search;
    }

    /**
     * 业务：检查拦截后的状态
     * 根据 condition: isIntercept、modifierName
     * filter: creationDate 查询 id
     *
     * @param creationDate 创建时间
     * @param range        查询范围
     * @param IsIntercept  拦截状态
     * @return es search result
     */
    public static JSONObject findIdsByIsInterceptAndMfNameFilterByCDate(String creationDate, Integer range,
                                                                        Integer IsIntercept) {
        JSONObject whereKey = new JSONObject();
        JSONObject filterKey = new JSONObject();
        filterKey.put("CREATIONDATE", creationDate);
        JSONArray orderKey = new JSONArray();
        Integer start = 0;
        String[] fieldKey = {"ID"};
        whereKey.put("IS_INTERECEPT", IsIntercept);
        whereKey.put("MODIFIERNAME", "!=root");

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKey,
                filterKey, orderKey, range, start, fieldKey);
        return search;
    }

    /**
     * #### 界面查询 ###
     *
     * @param whereKey   查询条件
     * @param filterKey  过滤条件
     * @param orderByKey 排序规则
     * @param range      查询范围
     * @param startIndex 起始索引
     * @return es search result
     */
    public static JSONObject queryOrderOutboundList(JSONObject whereKey, JSONObject filterKey, JSONArray orderByKey,
                                                    Integer range, Integer startIndex) {

        JSONObject esResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKey, filterKey, orderByKey,
                range, startIndex, new String[]{"ID"});
        return esResult;
    }

    /**
     * 业务：LTS任务调用库存中心接口（生成入库通知单/结果单，逻辑收货单）:补偿任务可补充旧数据
     * 根据 condition: orderStatus、platform、isActive
     * filter: creationDate 查询 id
     *
     * @param creationDate 创建时间
     * @param orderStatus  订单状态
     * @param platform     平台类型
     * @param number       显示条数
     * @param index        页面索引
     * @return es search result
     */
    public static JSONObject getIdsByOrderStatusAndPlaFormFilterByCDate(Object creationDate, Object orderStatus
            , Long platform, Integer number, Integer index) {
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("CREATIONDATE", creationDate);
        whereKeys.put("ORDER_STATUS", orderStatus);
        whereKeys.put("PLATFORM", platform);
        whereKeys.put("ISACTIVE", "Y");
        String[] returnFields = {"ID"};

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKeys, filterKeys, null, number, index, returnFields);
        return search;
    }


    /**
     * 获取一组id 存放到JSONArray里
     *
     * @param mergeEsHavingcountResult
     * @return
     */
    public static JSONArray getIds(MergeEsHavingcountResult mergeEsHavingcountResult) {
        if (null == mergeEsHavingcountResult.getMergeOderGroups()) {
            return null;
        }
        MergeOderGroups mergeOderGroups = mergeEsHavingcountResult.getMergeOderGroups();
        String jsonString = JSONObject.toJSONString(mergeOderGroups);
        JSONObject whereKeyJo = JSONObject.parseObject(jsonString);


        //分组后需要拼接的查询参数
        JSONObject whereKeys = new JSONObject();//查询条件
        JSONArray statusArray = new JSONArray();
        //订单状态待审核
        statusArray.add(OmsOrderStatus.UNCONFIRMED.toInteger());
        //订单状态已审核
        statusArray.add(OmsOrderStatus.CHECKED.toInteger());
        whereKeys.put("ORDER_STATUS", statusArray);
        whereKeys.put("IS_INRETURNING", "0");//是否退款为否
        whereKeys.put("IS_INTERECEPT", "0");//是否拦截为否
        // whereKeys.put("IS_HASPRESALESKU", "0"); //是否包含预售商品为否


        whereKeyJo.putAll(whereKeys);
        JSONObject esOrderJo = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeyJo, null, null,
                100, 0, new String[]{"ID"});
        if (null == esOrderJo) {
            return null;
        }
        JSONArray aryIds = esOrderJo.getJSONArray("data");
        if (aryIds == null || aryIds.size() < 2) {
            return null;
        }
        JSONArray jsonArray = new JSONArray();
        for (Object o : aryIds) {
            JSONObject jsonObject = (JSONObject) o;
            Map<String, Long> mas = new HashMap<>();
            Integer id = (Integer) jsonObject.get("ID");
            mas.put("ID", id.longValue());
            jsonArray.add(mas);
        }
        return jsonArray;
    }


    /**
     * 查询全渠道订单及订单明细
     *
     * @param sourceCode 平台单号
     * @return FLAG:0/1(0是全渠道 1 是退换货)
     */
    public static JSONObject findJSONObjectBySourceCode(String sourceCode, Integer range) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_CODE", sourceCode);
        JSONObject search = ElasticSearchUtil.search(OC_B_ORDER_INDEX_NAME,
                OC_B_ORDER_TYPE_NAME,
                whereKeys, null, null, range, 0, new String[]{"ID"});
        return search;
    }

    /**
     * 检查参数合法性
     *
     * @param sourceCode 平台单号信息
     * @param suffixInfo 订单补充信息
     * @return
     */
    public static JSONArray findJSONArrayBySourceCode(String sourceCode, String suffixInfo) {
        // 平台单号+订单补充信息不唯一，不允许保存 排除已取消 和 已作废
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_CODE", sourceCode);// 平台单号
        whereKeys.put("SUFFIX_INFO", suffixInfo);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys, null, null,
                100, 0, new String[]{"ID"});
        JSONArray aryIds = search.getJSONArray("data");
        return aryIds;
    }

    /**
     * 业务：财务查询
     * 根据 shopId、orderSource、isWriteOff、orderType、orderStatus 查询 id
     *
     * @param shopId      店铺id
     * @param orderSource 订单来源
     * @param isWriteOff  是否插入核销流水
     * @param orderType   订单类型
     * @param orderStatus 订单状态
     * @param pageSize    每页显示条数
     * @param pageNum     当前页
     * @return JSONObject
     */
    public static JSONObject getIdByShopIdAndSourceAndWriteAndTypeAndStatus(Long shopId, String orderSource,
                                                                            Long isWriteOff,
                                                                            List<Long> orderType,
                                                                            List<Long> orderStatus,
                                                                            Integer pageSize, Integer pageNum) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("CP_C_SHOP_ID", shopId);
        whereKeys.put("ORDER_SOURCE", orderSource);
        // whereKeys.put("IS_WRITEOFF", isWriteOff);
        whereKeys.put("ORDER_TYPE", JSONArray.toJSONString(orderType));
        whereKeys.put("ORDER_STATUS", JSONArray.toJSONString(orderStatus));
        whereKeys.put("ISACTIVE", "Y");
        String[] queryFieldsName = {"ID"};

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys, null, null,
                pageSize, (pageNum - 1) * pageSize, queryFieldsName);
        return search;
    }

    /**
     * 根据 sourceCode 查询 id, order by id
     *
     * @param sourceCode 平台单号信息
     * @return es search result
     */
    public static JSONObject getIdBySourceCodeOrderById(String sourceCode) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("SOURCE_CODE", sourceCode);
        //根据订单ID设置排序键
        JSONObject orderKeys = new JSONObject();
        orderKeys.put("asc", false);
        orderKeys.put("name", "ID");

        JSONArray orderByKeys = new JSONArray();
        orderByKeys.add(orderKeys);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKey, null, orderByKeys, 100, 0, new String[]{"ID"});
        return search;
    }

    /**
     * 根据 sourceCode、suffixInfo 查询 id, order by id
     *
     * @param sourceCode 平台单号信息
     * @param suffixInfo 订单补充信息
     * @return es search result
     */
    public static JSONObject getIdBySourceCodeAndSuffixInfoOrderById(String sourceCode, String suffixInfo) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_CODE", sourceCode);
        whereKeys.put("SUFFIX_INFO", suffixInfo);
        //根据订单ID设置排序键
        JSONObject orderKeys = new JSONObject();
        orderKeys.put("asc", false);
        orderKeys.put("name", "ID");

        JSONArray orderByKeys = new JSONArray();
        orderByKeys.add(orderKeys);
        JSONObject search = ElasticSearchUtil.search(
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKeys, null, orderByKeys, 100, 0, new String[]{"ID"});
        return search;
    }

    /**
     * 查询能够合单的数据
     *
     * @param order
     * @return
     */
    public static List<Long> findMergeOrderIdByGroup(OcBOrder order, MergeOderGroups mergeOderGroups) {
        mergeOderGroups.setCpCPhyWarehouseId(order.getCpCPhyWarehouseId());
        mergeOderGroups.setCpCregionAreaId(order.getCpCRegionAreaId());
        mergeOderGroups.setCpCregionCityId(order.getCpCRegionCityId());
        mergeOderGroups.setCpCregionProvinceId(order.getCpCRegionProvinceId());
        mergeOderGroups.setCpCShopId(order.getCpCShopId());
        mergeOderGroups.setPayType(order.getPayType());
        mergeOderGroups.setPlatform(order.getPlatform());
        mergeOderGroups.setUserNick(order.getUserNick());
        mergeOderGroups.setReceiverAddress(order.getReceiverAddress());
        mergeOderGroups.setReceiverMobile(order.getReceiverMobile());
        mergeOderGroups.setReceiverName(order.getReceiverName());
        mergeOderGroups.setReceiverPhone(order.getReceiverPhone());
        mergeOderGroups.setOrderType(order.getOrderType());

        String jsonString = JSON.toJSONString(mergeOderGroups);
        JSONObject whereKeyJo = JSONObject.parseObject(jsonString);

        JSONObject whereKeys = new JSONObject();//查询条件
        JSONArray statusArray = new JSONArray();
        //订单状态待审核
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKeys.put("IS_INRETURNING", "0");//是否退款为否
        whereKeys.put("IS_INTERECEPT", "0");//是否拦截为否
        // whereKeys.put("IS_HASPRESALESKU", "0"); //是否包含预售商品为否

        whereKeyJo.putAll(whereKeys);

        JSONObject esOrderJo = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeyJo, null, null,
                100, 0, new String[]{"ID"});
        if (null == esOrderJo) {
            return null;
        }
        JSONArray aryIds = esOrderJo.getJSONArray("data");
        if (aryIds == null || aryIds.size() < 2) {
            return null;
        }

        List<Long> ids = Lists.newArrayList();
        for (int i = 0; i < aryIds.size(); i++) {
            Map<String, Object> idMap = (Map<String, Object>) aryIds.get(i);
            ids.add(Long.valueOf(idMap.get("ID").toString()));
        }
        return ids;
    }

    /**
     * 循环分组取出所有的id 根据id到数据库查询到分组的数据
     *
     * @param groupList
     * @return
     */
    public static List<Integer> findJSONObjectByGroupList(List<MergeEsHavingcountResult> groupList) {
        List<Integer> ids = new ArrayList<>();
        for (MergeEsHavingcountResult mergeEsHavingcountResult : groupList) {
            MergeOderGroups mergeOderGroups = mergeEsHavingcountResult.getMergeOderGroups();
            String jsonString = JSONObject.toJSONString(mergeOderGroups);
            JSONObject whereKeyJo = JSONObject.parseObject(jsonString);

            JSONObject whereKeys = new JSONObject();//查询条件
            JSONArray statusArray = new JSONArray();
            //订单状态待审核
            statusArray.add(OmsOrderStatus.UNCONFIRMED.toInteger());
            //订单状态已审核
            statusArray.add(OmsOrderStatus.CHECKED.toInteger());
            whereKeys.put("ORDER_STATUS", statusArray);
            whereKeys.put("IS_INRETURNING", "0");//是否退款为否
            whereKeys.put("IS_INTERECEPT", "0");//是否拦截为否
            // whereKeys.put("IS_HASPRESALESKU", "0"); //是否包含预售商品为否
            whereKeyJo.putAll(whereKeys);

            JSONObject esOrderJo = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeyJo, null, null,
                    100, 0, new String[]{"ID"});
            if (null == esOrderJo) {
                return null;
            }
            JSONArray aryIds = esOrderJo.getJSONArray("data");
            if (aryIds == null) {
                continue;
            }
            for (int i = 0; i < aryIds.size(); i++) {
                Map<String, Integer> map = (Map<String, Integer>) aryIds.get(i);
                ids.add(map.get("ID"));
            }

        }
        return ids;
    }


    /**
     * 平台发货时，如果有拆标，则需要判断是数量拆还是分行拆
     *
     * @param tid
     * @return 行拆，true，数量拆false
     */
    public static boolean splitOrderCheck(String tid, Integer split) {
        if (!OcBorderListEnums.YesOrNoEnum.IS_NO.getVal().equals(split)) {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("TID", tid);
            JSONObject data = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, whereKeys, null, null,
                    100, 0, new String[]{"OOID"});
            JSONArray dataList = data.getJSONArray("data");
            if (CollectionUtils.isNotEmpty(dataList)) {
                List<String> ooidList = dataList.stream().map(a -> ((JSONObject) a).getString("OOID")).distinct().collect(Collectors.toList());
                return ooidList.size() != 1;
            }
            return true;
        }
        return false;
    }


    /**
     * es 查询
     *
     * @param
     * @return 分库键字符串
     */
    public static JSONObject findJSONObjectByIndexName(Integer eachSize, String indexName, String typeName, String tbName,
                                                       String fieldName, String sendTime, String shardKey) {

        JSONObject whereKey = new JSONObject();
        whereKey.put(fieldName, 3);
        JSONObject filterKey = new JSONObject();
        if ("OC_B_RETURN_AF_SEND".equalsIgnoreCase(tbName)) {
            sendTime = "SAP_FAILURE_TIMES";
        }
        filterKey.put(sendTime, "0~5");

        JSONObject esJsn = ElasticSearchUtil.search(indexName, typeName, whereKey, filterKey, null,
                eachSize, 0, new String[]{shardKey});
        return esJsn;
    }

    /**
     * @return 合单加密串
     */
    public static MergeOrderEsResult searchMergeGroupField(JSONArray openMergeOrderShops) {

        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKey.put("IS_INRETURNING", InreturningStatus.INRETURN_NO);
        whereKey.put("IS_INTERECEPT", InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
        whereKey.put("PAY_TYPE", "!=" + OmsPayType.CASH_ON_DELIVERY.toInteger());
//        whereKey.put("PLATFORM", "!=" + PlatFormEnum.VIP_JITX.getCode());
        whereKey.put("IS_SAME_CITY_PURCHASE", "!=1");
        whereKey.put("CP_C_SHOP_ID", openMergeOrderShops);

        JSONObject filterKey = new JSONObject();
        String[] groupByField = {"ORDER_ENCRYPTION_CODE"};
        JSONObject groupResultJsn = ElasticSearchUtil.havingCount(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKey, filterKey, "1~", groupByField);
        JSONArray groupAry = groupResultJsn.getJSONArray("data");
        if (CollectionUtils.isEmpty(groupAry)) {
            return null;
        }

        int itemCount = 0;
        int size = groupAry.size();
        List<String> encryptList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            JSONObject eachJsn = groupAry.getJSONObject(i).getJSONObject("key");
            if (eachJsn == null) {
                continue;
            }
            String encryptCode = eachJsn.getString("ORDER_ENCRYPTION_CODE");
            itemCount += groupAry.getJSONObject(i).getIntValue("total");
            encryptList.add(encryptCode);
        }
        MergeOrderEsResult mergeOrderEsResult = new MergeOrderEsResult();
        mergeOrderEsResult.setEncryptCodes(encryptList);
        mergeOrderEsResult.setTotalItemCount(itemCount);
        return mergeOrderEsResult;

    }

    /**
     * @param mergeOrderEsResult 合单条件加密串
     * @return 订单编号集
     */
    public static List<Long> searchOrderIdByEncryptCode(MergeOrderEsResult mergeOrderEsResult) {

        String[] searchFields = {"ID"};
        JSONArray resultJsnAay = searchOrderInfoByEncryptCode(mergeOrderEsResult, searchFields);
        if (CollectionUtils.isEmpty(resultJsnAay)) {
            return null;
        }
        List<JSONObject> jsonObjects = JSON.parseArray(resultJsnAay.toJSONString(), JSONObject.class);
        return jsonObjects.parallelStream().map(e -> e.getLong("ID")).collect(Collectors.toList());
    }

    /**
     * 合单查询
     *
     * @param mergeOrderEsResult 预合并,信息
     * @param searchFields       查询字段
     * @return es查询ids
     */
    public static JSONArray searchOrderInfoByEncryptCode(MergeOrderEsResult mergeOrderEsResult, String[] searchFields) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKey.put("IS_INRETURNING", InreturningStatus.INRETURN_NO);
        whereKey.put("IS_INTERECEPT", InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
        whereKey.put("PAY_TYPE", "!=" + OmsPayType.CASH_ON_DELIVERY.toInteger());
//        whereKey.put("PLATFORM", "!=" + PlatFormEnum.VIP_JITX.getCode());
        whereKey.put("IS_SAME_CITY_PURCHASE", "!=1");
        whereKey.put("ORDER_ENCRYPTION_CODE", mergeOrderEsResult.encryptCodes2JsonAry());

        JSONObject filterKey = new JSONObject();
        JSONObject searchResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, whereKey, filterKey, null,
                mergeOrderEsResult.getTotalItemCount(), 0, searchFields);
        return searchResult.getJSONArray("data");
    }

    /**
     * description:查询es出库通知单
     *
     * @Author: liuwenjin
     * @Date 2021/9/29 2:43 下午
     */
    public static List<String> findOrderDistributionByGroupOutBillNo(List<String> sourceCodes) {
        JSONArray statusArray = new JSONArray();
        String[] returnFields = {"SG_B_OUT_BILL_NO"};
        statusArray.addAll(sourceCodes);
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_CODE", statusArray);
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.IN_DISTRIBUTION.toInteger());
        whereKeys.put("SUGGEST_PRESINK_STATUS", TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y);
        // 根据平台单号ES查询订单id
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCORDER,
                TaobaoReturnOrderExt.TABLENAME_OCORDER,
                whereKeys, null, null, 1000, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        List<String> orderIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0, l = returnData.size(); i < l; i++) {
                String sgBOutBillNo = returnData.getJSONObject(i).getString("SG_B_OUT_BILL_NO");
                orderIds.add(sgBOutBillNo);
            }
        }
        return orderIds;
    }


    public static JSONObject getOrderStatusById(Long orderId) {
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        whereKeys.put("ID", orderId);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys,
                filterKeys, null, 1000, 0, new String[]{"ORDER_STATUS", "WMS_CANCEL_STATUS"});

        JSONArray jsonArray = search.getJSONArray("data");
        if (jsonArray.isEmpty() || jsonArray.size() < 1) {
            return new JSONObject();
        } else {
            return jsonArray.getJSONObject(0);
        }
    }

    /**
     * 查询周期购子订单集合
     *
     * @param sourceBillNo 来源单据编号
     * @return ids
     */
    public static List<Long> searchCyclePurchaseSubOrderIds(String sourceBillNo, Integer currentCycleNumber) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SOURCE_BILL_NO", sourceBillNo);
        if (currentCycleNumber != null) {
            whereKeys.put("CURRENT_CYCLE_NUMBER", currentCycleNumber);
        }
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, whereKeys, null, null,
                1000, 0, new String[]{"ID"});
        JSONArray returnIdAry = search.getJSONArray("data");
        return ES4Order.statisticsResult(returnIdAry, "ID");
    }

    /**
     * 根据 商品 买家昵称 平台 查询预计发货时间最大的周期购订单
     */
    public static Long searchCyclePurchaseOrderId(String userNick, Integer platform, Long shopId, String spu) {
        JSONObject orderKey = new JSONObject();
        JSONObject whereKey = new JSONObject();
        // 手机号=当前收货人手机号 状态=待寻源 原商品=当前虚拟商品 取发货时间最新的一条
        whereKey.put("ISACTIVE", "Y");
        whereKey.put("ORDER_STATUS", OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        whereKey.put("USER_NICK", userNick);
        whereKey.put("PLATFORM", platform);
        whereKey.put("CP_C_SHOP_ID", shopId);
        whereKey.put("RESERVE_VARCHAR02", spu);
        orderKey.put("asc", false);
        orderKey.put("name", "ESTIMATE_CON_TIME");
        JSONArray order = new JSONArray();
        order.add(orderKey);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKey, null, order,
                1, 0, new String[]{"ID"});
        JSONArray ary = search.getJSONArray("data");
        if (ary == null || ary.size() < 1) {
            return null;
        }
        return ary.getJSONObject(0).getLong("ID");
    }

    /**
     * 查找京东需要下载发票的单子
     * RESERVE_BIGINT05 备用字段
     *
     * @return JSONObject查询结果
     */
    public static List<Long> findByIsInvoice(Long startTime, Long endTime) {
        JSONObject whereKeys = new JSONObject();
        //下载发票
        whereKeys.put("IS_INVOICE", 1);
        // 是否拉取过
        whereKeys.put("RESERVE_BIGINT05", 0);
        // 京东平台
        JSONArray platFormList = new JSONArray();
        platFormList.add(PlatFormEnum.JINGDONG.getCode());
        platFormList.add(500);
        whereKeys.put("PLATFORM", platFormList);
        // 创建时间比 2022-09-13 大，2022-09-13 系统上线日期
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("CREATIONDATE", startTime + "~" + endTime);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                whereKeys, filterKeys, null,
                500, 0, new String[]{"ID"});
        JSONArray returnIdAry = search.getJSONArray("data");

        log.info(LogUtil.format("findByIsInvoice findByIsInvoice:{}", "findByIsInvoice"),
                Objects.isNull(returnIdAry) ? null : JSONArray.toJSONString(returnIdAry));

        return ES4Order.statisticsResult(returnIdAry, "ID");
    }

    /**
     * 根据拼车单号获取
     *
     * @param carpoolNo
     * @return
     */
    public static List<Long> getIdsByCarpoolNo(String carpoolNo) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("CARPOOL_NO", carpoolNo);
        JSONObject result = ElasticSearchUtil.search(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                whereKeys, null, null, 1000, 0, new String[]{"ID"});
        JSONArray ary = result.getJSONArray("data");
        if (ary == null || ary.size() < 1) {
            return null;
        }
        return statisticsResult(ary, "ID");
    }
}
