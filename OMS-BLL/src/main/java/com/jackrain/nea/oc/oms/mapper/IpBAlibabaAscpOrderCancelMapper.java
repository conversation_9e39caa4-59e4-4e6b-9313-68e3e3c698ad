package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderCancel;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Mapper
@Component
public interface IpBAlibabaAscpOrderCancelMapper extends ExtentionMapper<IpBAlibabaAscpOrderCancel> {


    @Update("<script> "
            + "UPDATE ip_b_alibaba_ascp_order_cancel SET istrans = #{istrans} where popafsrefundapplyid in "
            + "<foreach item='item' index='index' collection='appyids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTMailzfSaRefundIstransList(@Param("istrans") int istrans, @Param("appyids") List<Long> appyids);


    @Select("SELECT * FROM ip_b_alibaba_ascp_order_cancel WHERE biz_order_code=#{bizOrderCode} LIMIT 1 ")
    IpBAlibabaAscpOrderCancel selectIpBAlibabaAscpOrderCancelByBizOrderCode(@Param("bizOrderCode") String bizOrderCode);

    @UpdateProvider(type = IpBTMailzfSaRefundSqlBuilder.class, method = "updateOrderCancelAndTransCount")
    int updateOrderCancelAndTransCount(JSONObject jsonObject);

    @UpdateProvider(type = IpBTMailzfSaRefundSqlBuilder.class, method = "updateOrderCancel")
    int updateOrderCancel(JSONObject jsonObject);

    /**
     * 更新状态及次数
     */
    class IpBTMailzfSaRefundSqlBuilder {
        public String updateOrderCancelAndTransCount(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_alibaba_ascp_order_cancel");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (Map.Entry<String, Object> map : map.entrySet()) {
                        if (!"id".equals(map.getKey())) {
                            SET(map.getKey() + "= #{" + map.getKey() + "}");
                        }
                    }
                    WHERE("id = #{id}");
                }
            }.toString();
        }

        public String updateOrderCancel(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_alibaba_ascp_order_cancel");
                    for (Map.Entry<String, Object> map : map.entrySet()) {
                        if (!"id".equals(map.getKey())) {
                            SET(map.getKey() + "= #{" + map.getKey() + "}");
                        }
                    }
                    WHERE("id = #{id}");
                }
            }.toString();
        }
    }

}