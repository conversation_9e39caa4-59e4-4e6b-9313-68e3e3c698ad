package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Mapper
public interface IpBTimeOrderVipMapper extends ExtentionMapper<IpBTimeOrderVip> {
    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_REMARK_LENGTH = 250;

    /**
     * 通过订单号查询
     **/
    @Select("SELECT * FROM ip_b_time_order_vip WHERE occupied_order_sn=#{occupiedOrderSn}")
    IpBTimeOrderVip selectTimeOrderByOccupiedOrderSn(@Param("occupiedOrderSn") String occupiedOrderSn);

    /**
     * 通过库存占用订单号修改时效订单
     *
     * @param occupiedOrderSn 库存占用订单号
     * @param status          状态
     * @return 返回结果
     */
    @Update("UPDATE ip_b_time_order_vip SET status = #{status} WHERE occupied_order_sn=#{occupiedOrderSn}")
    int updateTimeOrderStatus(@Param("occupiedOrderSn") String occupiedOrderSn,
                              @Param("status") int status);


    /**
     * 更新缺货数量
     *
     * @param occupiedOrderSn  库存占用订单号
     * @param outStockQuantity 缺货数量
     * @param status           状态
     * @return 返回值
     */
    @Update("UPDATE ip_b_time_order_vip SET out_stock_quantity = #{outStockQuantity},status = #{status} WHERE occupied_order_sn=#{occupiedOrderSn}")
    int updateTimeOrderOutStockQuantityAndStatus(@Param("occupiedOrderSn") String occupiedOrderSn,
                                                 @Param("outStockQuantity") BigDecimal outStockQuantity,
                                                 @Param("status") int status);

    /**
     * 通过唯品会订单号查询唯品会时效订单
     *
     * @param orderSn
     * @return
     */
    @Select("SELECT * FROM ip_b_time_order_vip WHERE order_sn=#{orderSn}")
    List<IpBTimeOrderVip> selectTimeOrderByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 通过订单号批量查询
     **/
    @Select("<script> " +
            "SELECT * FROM ip_b_time_order_vip WHERE isactive = 'Y' and occupied_order_sn in "
            + "<foreach item='item' index='index' collection='occupiedOrderSns' open='(' separator=',' close=')'>"
            + " #{item}"
            + " </foreach> "
            + "</script>")
    List<IpBTimeOrderVip> listTimeOrderByOccupiedOrderSns(@Param("occupiedOrderSns") List<String> occupiedOrderSns);

    /**
     * 通过订单号批量更新 转换状态为 转换中(1)
     **/
    @Update("<script> "
            + "UPDATE ip_b_time_order_vip SET " +
            "  modifierid = #{modifierId, jdbcType=BIGINT}, " +
            "  modifiername = #{modifierName, jdbcType=VARCHAR}, " +
            "  modifierename = #{modifierEName, jdbcType=VARCHAR}, " +
            "  modifieddate = #{date, jdbcType=DATE}, " +
            "  next_compensation_date = #{nextCompensationTime}, " +
            "  compensation_time = IFNULL(compensation_time,0) + 1, " +
            "  sysremark = #{remark, jdbcType=VARCHAR} " +
            " where occupied_order_sn = #{occupiedOrderSn}" +
            " and status = #{status}" +
            " and istrans = #{isTrans}"
            + "</script>")
    int updateVipOrder(@Param("occupiedOrderSn") String occupiedOrderSn
            , @Param("modifierId") Long modifierId
            , @Param("modifierName") String modifierName
            , @Param("modifierEName") String modifierEName
            , @Param("date") Date date
            , @Param("remark") String remark
            , @Param("nextCompensationTime") Date nextCompensationTime
            , @Param("status") Integer status
            , @Param("isTrans") Integer isTrans
    );

    @Update("<script> "
            + "  UPDATE ip_b_time_order_vip SET "
            + "  modifierid = #{modifierId, jdbcType=BIGINT}, "
            + "  modifiername = #{modifierName, jdbcType=VARCHAR}, "
            + "  modifierename = #{modifierEName, jdbcType=VARCHAR}, "
            + "  modifieddate = #{date, jdbcType=DATE}, "
            + "  status = #{status}, "
            + "  istrans = #{isTrans}, "
            + "  trans_date = #{date}, "
            + "  trans_count = IFNULL(trans_count,0) + 1, "
            + "  sysremark = #{remark, jdbcType=VARCHAR} "
            + "  where occupied_order_sn = #{occupiedOrderSn}"
            + "</script>")
    int updateVipOrderStatusAndTrans(@Param("occupiedOrderSn") String occupiedOrderSn
            , @Param("modifierId") Long modifierId
            , @Param("modifierName") String modifierName
            , @Param("modifierEName") String modifierEName
            , @Param("date") Date date
            , @Param("remark") String remark
            , @Param("status") Integer status
            , @Param("isTrans") Integer isTrans
    );


    @Update({"<script>" +
            "<foreach collection=\"updateTimeOrderList\" item=\"item\" separator=\";\">" +
            " UPDATE" +
            " ip_b_time_order_vip" +
            " SET status = #{item.status, jdbcType=INTEGER}, " +
            "  modifierid = #{item.modifierid, jdbcType=INTEGER}, " +
            "  modifiername = #{item.modifiername, jdbcType=VARCHAR}, " +
            "  modifierename = #{item.modifierename, jdbcType=VARCHAR}, " +
            "  modifieddate = #{item.modifieddate, jdbcType=DATE}, " +
            "  sysremark = #{item.sysremark, jdbcType=VARCHAR} " +
            "  WHERE " +
            "   occupied_order_sn = #{item.occupiedOrderSn, jdbcType=VARCHAR} " +
            "</foreach>" +
            "</script>"})
    void batchUpdateTimeOrder(@Param("updateTimeOrderList") List<IpBTimeOrderVip> updateTimeOrderList);

    /**
     * 订单SQL创建器
     */
    class TimeOrderSqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param occupiedOrderSn  库存占用订单号
         * @param isTrans          转换状态
         * @param isUpdateTransNum 是否更新转换数量
         * @param remarks          转换备注信息
         * @return 更新SQL语句
         */
        public String buildUpdateTimeOrderTransSQL(@Param("occupiedOrderSn") String occupiedOrderSn
                , @Param("isTrans") int isTrans
                , @Param("isUpdateTransNum") boolean isUpdateTransNum
                , @Param("remarks") String remarks) {
            return new SQL() {
                {
                    UPDATE("ip_b_time_order_vip");
                    SET("istrans=#{isTrans}");
                    SET("modifieddate = now()");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                        SET("trans_date = SYSDATE()");
                    }
                    SET("sysremark=#{remarks}");
                    WHERE("occupied_order_sn=#{occupiedOrderSn}");
                }
            }.toString();
        }

        /**
         * 创建更新订单转换状态SQL
         *
         * @param occupiedOrderSn  库存占用订单号
         * @param isTrans          转换状态
         * @param outStockQuantity 缺货数量
         * @param remarks          转换备注信息
         * @return 更新SQL语句
         */
        public String updateTimeOrderTransAndOutStockQty(@Param("occupiedOrderSn") String occupiedOrderSn
                , @Param("isTrans") int isTrans
                , @Param("outStockQuantity") BigDecimal outStockQuantity
                , @Param("remarks") String remarks) {
            return new SQL() {
                {
                    UPDATE("ip_b_time_order_vip");
                    SET("istrans=#{isTrans}");
                    SET("out_stock_quantity=#{outStockQuantity}");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    SET("trans_date = SYSDATE()");
                    SET("sysremark=#{remarks}");
                    WHERE("occupied_order_sn=#{occupiedOrderSn}");
                }
            }.toString();
        }

        /**
         * 装换异常 更新异常类型
         * @param occupiedOrderSn
         * @param isTrans
         * @param isUpdateTransNum
         * @param remarks
         * @param exceptionType
         * @return
         */
        public String buildUpdateTimeOrderTransAndExceptionTypeSQL(@Param("occupiedOrderSn") String occupiedOrderSn
                , @Param("isTrans") int isTrans
                , @Param("isUpdateTransNum") boolean isUpdateTransNum
                , @Param("remarks") String remarks
                , @Param("exceptionType") String exceptionType) {
            return new SQL() {
                {
                    UPDATE("ip_b_time_order_vip");
                    SET("istrans=#{isTrans}");
                    SET("modifieddate = now()");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                        SET("trans_date = SYSDATE()");
                    }
                    SET("exception_type=#{exceptionType}");
                    SET("sysremark=#{remarks}");
                    WHERE("occupied_order_sn=#{occupiedOrderSn}");
                }
            }.toString();
        }
    }

    /**
     * 更新订单转换状态
     *
     * @param occupiedOrderSn  库存占用订单号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换次数
     * @param remarks          转换备注信息
     * @return 更新结果
     */
    @UpdateProvider(type = IpBTimeOrderVipMapper.TimeOrderSqlBuilder.class
            , method = "buildUpdateTimeOrderTransSQL")
    int updateTimeOrderIsTrans(@Param("occupiedOrderSn") String occupiedOrderSn
            , @Param("isTrans") int isTrans
            , @Param("isUpdateTransNum") boolean isUpdateTransNum
            , @Param("remarks") String remarks);

    /**
     * 更新订单转换状态
     *
     * @param occupiedOrderSn  库存占用订单号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换次数
     * @param remarks          转换备注信息
     * @param exceptionType     异常类型
     * @return 更新结果
     */
    @UpdateProvider(type = IpBTimeOrderVipMapper.TimeOrderSqlBuilder.class
            , method = "buildUpdateTimeOrderTransAndExceptionTypeSQL")
    int updateTimeOrderIsTransAndExceptionType(@Param("occupiedOrderSn") String occupiedOrderSn
            , @Param("isTrans") int isTrans
            , @Param("isUpdateTransNum") boolean isUpdateTransNum
            , @Param("remarks") String remarks
            ,@Param("exceptionType") String exceptionType);

    /**
     * 更新订单转换状态及缺货数量
     *
     * @param occupiedOrderSn  库存占用订单号
     * @param isTrans          转换状态
     * @param outStockQuantity 缺货数量
     * @param remarks          转换备注信息
     * @return 更新结果
     */
    @UpdateProvider(type = IpBTimeOrderVipMapper.TimeOrderSqlBuilder.class
            , method = "updateTimeOrderTransAndOutStockQty")
    int updateTimeOrderTransAndOutStockQty(@Param("occupiedOrderSn") String occupiedOrderSn
            , @Param("isTrans") int isTrans
            , @Param("outStockQuantity") BigDecimal outStockQuantity
            , @Param("remarks") String remarks);

    /**
     * 通过唯品会订单号查询唯品会时效订单
     *
     * @param orderSn
     * @return
     */
    @Select("SELECT * FROM ip_b_time_order_vip WHERE order_sn=#{orderSn}")
    List<IpBTimeOrderVip> selectTimeOrderOccupiedOrderSnListByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 通过唯品会根订单号查询唯品会时效订单
     *
     * @param rootOrderSn
     * @return
     */
    @Select("SELECT * FROM ip_b_time_order_vip WHERE root_order_sn=#{rootOrderSn}")
    List<IpBTimeOrderVip> selectTimeOrderOccupiedOrderSnListByRootOrderSn(@Param("rootOrderSn") String rootOrderSn);


    @Update("<script> " +
            "update ip_b_time_order_vip set reserve_bigint01 = #{varcharValue} where occupied_order_sn in "
            + "<foreach item='item' index='index' collection='occupiedOrderSns' open='(' separator=',' close=')'>"
            + " #{item}"
            + " </foreach> "
            + "</script>")
    int updateInVirtualOccupyFlag(@Param("occupiedOrderSns") List<String> occupiedOrderSns,@Param("varcharValue") Integer varcharValue);

    /**
     * 通过唯品会订单号集合批量查询时效订单
     **/
    @Select("<script> " +
            " SELECT * FROM ip_b_time_order_vip WHERE isactive = 'Y' and order_sn in "
            + "<foreach item='item' index='index' collection='orderSns' open='(' separator=',' close=')'>"
            + " #{item}"
            + " </foreach> "
            + "</script>")
    List<IpBTimeOrderVip> listTimeOrderByOrderSns(@Param("orderSns") List<String> orderSns);
}