package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsReleaseOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.ManyReplaceInfo;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.tag.TaggerManager;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.PrintLogUtils;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 商品相关服务
 *
 * @author: 夏继超
 * @since: 2020/2/14
 * create at : 2020/2/14 11:56
 */
@Slf4j
@Component
public class BatchOperationGoodsService {

    /**
     * 指定业务类型禁止添加赠品(添加赠品排除业务类型)
     */

    @Autowired
    private OcBOrderListQueryService listQueryService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsReplaceComposeService omsReplaceComposeService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private PropertiesConf propertiesConf;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private BatchOperationGoodsService batchOperationGoodsService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;
    @Autowired
    private SplitOutStockOrderService splitOutStockOrderService;

    @Autowired
    private OmsReplaceProductService omsReplaceProductService;

    @Autowired
    private OcbCancelOrderMergeService cancelOrderMergeService;

    @Autowired
    private AgainOccupyStockService againOccupyStockService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;

    @Resource
    private ThreadPoolTaskExecutor commonThreadPoolExecutor;

    @Resource
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;

    @Autowired
    private ExportUtil exportUtil;

    @Autowired
    private ThreadPoolTaskExecutor batchOperationGoodsPollExecutor;
    @Autowired
    private ThreadPoolTaskExecutor batchReplaceGoodsPollExecutor;

    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;

    @NacosValue(value = "${r3.order.zp.naika:'10800'}", autoRefreshed = true)
    private String naika;
    @NacosValue(value = "${r3.order.zp.sale:'10800,10801,10802'}", autoRefreshed = true)
    private String sale;

    @NacosValue(value = "${r3.oc.oms.order.add.gift.exclude:a}", autoRefreshed = true)
    public String addGiftExclude;

    @NacosValue(value = "${r3.order.replace.naika:10800,10801,10802,10803}", autoRefreshed = true)
    private String replaceNaika;

    /**
     * 批量删除商品
     *
     * @param param     入参
     * @param loginUser 登录用户
     * @param usrPem
     * @return
     */
    public ValueHolderV14 batchDeleteGoods(JSONObject param, User loginUser, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (MapUtils.isEmpty(param)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        if (!param.containsKey("itemId")) {
            if (!param.containsKey("changeGoodsSku") && param.getString("changeGoodsSku") == null) {
                vh.setMessage("请选择需要删除的赠品");
                vh.setCode(ResultCode.FAIL);
                return vh;
            }
        }
        JSONArray itemIdJSONArray = param.getJSONArray("itemId");
        String changeGoodsSKu = param.getString("changeGoodsSku");
        String detail = param.getString("detail");
        param.remove("changeGoodsSku");
        // step01 根据传入得参数来确定用不用查询id
        List<Long> longs = new ArrayList();
        if (param.containsKey("ids")) {
            if (!param.getJSONArray("ids").isEmpty()) {
                JSONArray ids = param.getJSONArray("ids");
                for (int i = 0; i < ids.size(); i++) {
                    longs.add(ids.getLong(i));
                }
            }
        } else {
            longs = checkParamForIds(param, loginUser, usrPem);
        }
        if (CollectionUtils.isEmpty(longs)) {
            vh.setMessage("查询不到数据请检查后重试");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
        int success = 0;  // 用来统计成功和失败条数
        ValueHolderV14 holder = new ValueHolderV14();
        List<ValueHolderV14> holderV14s = new ArrayList<>();
        for (Long aLong : longs) {
            long begin = System.currentTimeMillis();
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(aLong);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            String billNo = null;
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocOrder = ocBOrderMapper.selectById(aLong);
                    billNo = ocOrder.getBillNo();
                    long time2 = System.currentTimeMillis();
                    PrintLogUtils.printDeBugLog("orderId={},batchDeleteGoods into tryLock time2():{}", aLong, time2 - begin);
                    ValueHolderV14 v14 = bean.mainStep(ocOrder, loginUser, changeGoodsSKu, itemIdJSONArray, "Y".equals(detail));
                    long time3 = System.currentTimeMillis();
                    PrintLogUtils.printDeBugLog("orderId={},batchDeleteGoods into mainStep time4():{}", aLong, time3 - time2);
                    holder.setCode(v14.getCode());
                    holder.setMessage(v14.getMessage());
                    holderV14s.add(v14);
                    if (ResultCode.SUCCESS == v14.getCode()) {
                        success++;
                        List<OcBOrderItem> gifts = ocBOrderItemMapper.selectOrderItemFullGiftList(ocOrder.getId());
                        // 自动打标：改单,赠品
                        this.modifyOrderStatus(ocOrder.getId(), ocOrder.getIsModifiedOrder()
                                , CollectionUtils.isEmpty(gifts) ? 0 : 1);
                        updateOrderAllSku(ocOrder);
                        try {
                            splitOutStockOrderService.handleOutStockOrderForOrderSplit(ocOrder.getId(), loginUser);
                        } catch (Exception e) {
                            log.error(LogUtil.format("删除赠品更新缺货拆单数据报错: {}", aLong), Throwables.getStackTraceAsString(e));
                        }
                    } else {
                        //调用订单得日志服务
                        omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(), OrderLogTypeEnum.GIFT_DEL.getKey(), v14.getMessage() + " 删除商品失败！原因:非平台商品且成交金额是0才可删除!", null, null, loginUser);
                    }
                } else {
                    throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("删除赠品失败: {}", aLong), Throwables.getStackTraceAsString(e));
                if (StringUtils.isNotBlank(detail) && "Y".equals(detail)) {
                    omsOrderLogService.addUserOrderLog(aLong, billNo, OrderLogTypeEnum.GIFT_DEL.getKey(), StringUtils.isNotBlank(changeGoodsSKu) ? changeGoodsSKu : itemIdJSONArray.toJSONString() + " 删除商品失败！原因:" + e.getMessage(), null, null, loginUser);
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Objects.nonNull(e.getMessage()) ? e.getMessage() : "删除赠品失败!");
                    return vh;
                }
                //调用订单得日志服务
                omsOrderLogService.addUserOrderLog(aLong, billNo, OrderLogTypeEnum.GIFT_DEL.getKey(), changeGoodsSKu + " 删除商品失败！原因:" + e.getMessage(), null, null, loginUser);
            } finally {
                redisLock.unlock();
            }
        }
        if (longs.size() == success) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("删除赠品成功" + success + "条数据，删除赠品失败" + (longs.size() - success) + "条数据");

        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("删除赠品成功" + success + "条数据，删除赠品失败" + (longs.size() - success) + "条数据");

        }
        if ("Y".equals(detail)) {
            vh.setCode(holderV14s.get(0).getCode());
            vh.setMessage(holderV14s.get(0).getMessage());
        }

        vh.setData(holderV14s);
        return vh;
    }

    /**
     * 自动打标：改单,赠品
     *
     * @param id              orderId
     * @param isModifiedOrder 是否改单
     * @param hasGift         是否含有赠品
     */
    private void modifyOrderStatus(long id, Integer isModifiedOrder, Integer hasGift) {
        if (Objects.isNull(isModifiedOrder) || isModifiedOrder != 1) {
            OcBOrder modifiedOrder = new OcBOrder();
            modifiedOrder.setId(id);
            modifiedOrder.setIsModifiedOrder(1);
            if (hasGift != null) {
                modifiedOrder.setIsHasgift(hasGift);
            }
            ocBOrderMapper.updateById(modifiedOrder);
        }
    }

    /**
     * <AUTHOR>
     * @Date 16:55 2021/8/27
     * @Description
     */
    public void updateOrderAllSku(OcBOrder ocOrder) {
        //赋值全部sku字段
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItems(ocOrder.getId());
        //加上sku
        String allSku = "";
        for (OcBOrderItem item : orderItems) {
            if (allSku.length() >= 100) {
                allSku += "...";
                break;
            }
            Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            allSku = allSku + item.getPsCSkuEcode() + "(" + item.getQty().intValue() + "),";
        }
        if (StringUtils.isNotEmpty(allSku)) {
            ocOrder.setAllSku(allSku.substring(0, allSku.length() - 1));
            ocBOrderMapper.updateById(ocOrder);
        }
    }


    /**
     * 删除商品主流程
     * OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode()
     *
     * @param ocBOrder       订单数据
     * @param loginUser      当前登录用户
     * @param changeGoodsSKu 所要删除的 SKU 商品
     * @return 返回的结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 mainStep(OcBOrder ocBOrder, User loginUser, String changeGoodsSKu, JSONArray itemIdJSONArray, boolean isDetail) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入删除赠品主流程: {}", ocBOrder.getId()));
        }
        ValueHolderV14 vh = new ValueHolderV14();

        // step 001  1.当前订单状态为 待审核/缺货 2.当前订单存在该SKU。 setBusinessTypeCode
        ValueHolderV14<List<OcBOrderItem>> v14 = checkStuatsAndSkuReturnItem(ocBOrder, changeGoodsSKu, 0, itemIdJSONArray);
        if (ResultCode.FAIL == v14.getCode()) {
            return v14;
        }

        List<OcBOrderItem> ocOrderItems = v14.getData();

        for (OcBOrderItem item : ocOrderItems) {
            if (!checkBusinessType(ocBOrder, item.getPsCSkuEcode())) {
                return new ValueHolderV14(ResultCode.FAIL, ocBOrder.getBillNo() + "勾选的订单不符合要求，不可以修改商品");
            }
        }


        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        List<OcBOrderItem> data = v14.getData();
        StringBuilder giftIsZeroErrorMessage = new StringBuilder();
        if (isDetail) {
            StringBuilder realAmtIsNotZeroErrorMessage = new StringBuilder();
            StringBuilder manualAddIsZeroErrorMessage = new StringBuilder();
            for (OcBOrderItem datum : data) {
                if (1 != (Objects.nonNull(datum.getIsGift()) ? datum.getIsGift() : 0)) {
                    giftIsZeroErrorMessage.append(datum.getPsCSkuEcode()).append(", ");
                }
                if (datum.getRealAmt().compareTo(BigDecimal.ZERO) > 0) {
                    realAmtIsNotZeroErrorMessage.append(datum.getPsCSkuEcode()).append(", ");
                }
                // 平台商品判断
                // if (!"1".equals(datum.getIsManualAdd())) {
                if (GiftTypeEnum.PLATFORM.getVal().equals(datum.getGiftType())) {
                    manualAddIsZeroErrorMessage.append(datum.getPsCSkuEcode()).append(", ");
                }
            }
            if (StringUtils.isNotBlank(giftIsZeroErrorMessage)) {
                throw new NDSException("条码:[" + StringUtils.removeEnd(giftIsZeroErrorMessage.toString(), ", ") + "]非赠品不允许删除!");
            } else if (StringUtils.isNotBlank(realAmtIsNotZeroErrorMessage)) {
                throw new NDSException("条码:[" + StringUtils.removeEnd(realAmtIsNotZeroErrorMessage.toString(), ", ") + "]金额不为0, 不能删除!");
            } else if (StringUtils.isNotBlank(manualAddIsZeroErrorMessage)) {
                throw new NDSException("条码:[" + StringUtils.removeEnd(manualAddIsZeroErrorMessage.toString(), ", ") + "]是平台赠品, 不能删除!");
            }
        }
        StringBuilder errorMessage = new StringBuilder();
        List<String> giftbagSkuList = new ArrayList<>();
        for (OcBOrderItem item : data) {
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                ocBOrderItems.clear();
            }
            // 可以删除的条件 ： 非平台商品（即手工添加的商品） 且 赠品 且 成交金额是0
            if (isCanDelete(item)) {
                //判断是否为组合商品
                if (item.getProType() == SkuType.NO_SPLIT_COMBINE && StringUtils.isNotBlank(item.getGiftbagSku()) && !giftbagSkuList.contains(item.getGiftbagSku())) {
                    giftbagSkuList.add(item.getGiftbagSku());
                    ocBOrderItems = ocBOrderItemMapper.selectOrderItemByBagSku(ocBOrder.getId(), item.getGiftbagSku());
                    batchOperationGoodsService.deleteOcBOrderItem(ocBOrder, ocBOrderItems, loginUser);
                } else {
                    ocBOrderItems.add(item);
                    batchOperationGoodsService.deleteOcBOrderItem(ocBOrder, ocBOrderItems, loginUser);
                }
            } else {
                if (isDetail) {
                    // 详情页面不能删除 抛错误
                    throw new NDSException("非平台赠品且成交金额是0才可删除!");
                }
                // errorMessage.append("明细:").append(item.getId()).append("虚拟条码:").append(item.getGiftbagSku()).append(", ");
                errorMessage.append("明细:").append(item.getId()).append("条码:").append(item.getPsCSkuEcode()).append(", ");
            }
        }
        if (StringUtils.isNotBlank(errorMessage)) {
            vh.setCode(-1);
            vh.setMessage(ocBOrder.getBillNo() + ":" + StringUtils.removeEnd(errorMessage.toString(), ", ")
                    + "不能删除!");
            return vh;
        }
        vh.setCode(0);
        vh.setMessage("删除赠品成功!");
        return vh;
    }


    /**
     * 判断是否符合新逻辑
     *
     * @param ocBOrder   订单
     * @param productSku 商品信息
     * @return 不符合的品相
     */
    private String checkBusinessTypeByChangeGoods(OcBOrder ocBOrder, ProductSku productSku) {
        String skuEcode = productSku.getSkuEcode();
        Map<String, String> skuDimEcodeMap = new HashMap<>();

        /*组合品需要判断组合品内的明细*/
        if (productSku.getSkuType() == SkuType.COMBINE_PRODUCT) {
            /*查询组合品SKU明细*/
            List<PsCSkugroup> holder = psRpcService.selectGroupProdSkuInfoBySingleSku(skuEcode);
            List<String> itemSkuEcodeList = ListUtils.emptyIfNull(holder).stream()
                    .map(PsCSkugroup::getPsCSkuEcode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemSkuEcodeList)) {
                log.warn(LogUtil.format("组合品获取到子商品sku为空,组合品编码:{}",
                        "checkBusinessTypeByChangeGoods"), skuEcode);
                return skuEcode;
            }

            List<PsCPro> psCProList = psRpcService.queryProByEcode(itemSkuEcodeList);
            Map<String, PsCPro> proMap = ListUtils.emptyIfNull(psCProList).stream()
                    .collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));
            if (MapUtil.isEmpty(proMap)) {
                log.warn(LogUtil.format("组合品编码查询款号信息为空:{},skuEcodeList:{},proMap:{}", "checkBusinessTypeByChangeGoods"),
                        skuEcode, itemSkuEcodeList, proMap);
                return skuEcode;
            }

            List<Long> mDim2IdList = ListUtils.emptyIfNull(psCProList).stream()
                    .map(PsCPro::getMDim2Id).filter(Objects::nonNull).map(Integer::longValue).collect(Collectors.toList());
            List<PsCProdimItem> psProdimItemList = psRpcService.selectPsCProdimItemList(mDim2IdList);
            Map<Long, String> idDimMap = ListUtils.emptyIfNull(psProdimItemList).stream()
                    .collect(Collectors.toMap(PsCProdimItem::getId, PsCProdimItem::getEcode, (a, b) -> a));

            log.info(LogUtil.format("组合品编码:{},skuEcodeList:{},proMapKeys:{},idDimMap:{}", "checkBusinessTypeByChangeGoods"),
                    skuEcode, itemSkuEcodeList, proMap.keySet(), idDimMap);
            for (String itemSkuCode : itemSkuEcodeList) {
                PsCPro psCPro = proMap.get(itemSkuCode);
                if (psCPro == null) {
                    log.warn(LogUtil.format("组合品明细查询不到商品款号信息,组合品编码:{},明细编码:{}",
                            "checkBusinessTypeByChangeGoods"), skuEcode, itemSkuCode);
                    return itemSkuCode;
                }

                Integer mDim2Id = psCPro.getMDim2Id();
                if (mDim2Id == null) {
                    log.warn(LogUtil.format("组合品明细查询不到物料组id,组合品编码:{},明细编码:{},款号编码:{}",
                            "checkBusinessTypeByChangeGoods"), skuEcode, itemSkuCode, psCPro.getEcode());
                    return itemSkuCode;
                }
                String psProdimItemEcode = idDimMap.get(mDim2Id.longValue());
                if (StringUtils.isEmpty(psProdimItemEcode)) {
                    log.warn(LogUtil.format("组合品明细的物料组id查询不到物料信息,组合品编码:{},明细编码:{},款号编码:{},物料组ID:{}",
                            "checkBusinessTypeByChangeGoods"), skuEcode, itemSkuCode, psCPro.getEcode(), mDim2Id);
                    return itemSkuCode;
                }

                log.info(LogUtil.format("组合品明细查询物料编码数据,组合品编码:{},明细编码:{},款号编码:{},物料组ID:{},物料组编码:{}",
                        "checkBusinessTypeByChangeGoods"), skuEcode, itemSkuCode, psCPro.getEcode(), mDim2Id, psProdimItemEcode);

                //不允许选择奶卡物料
                List<String> noReplaces = Arrays.asList(replaceNaika.split(","));
                if (noReplaces.contains(psProdimItemEcode)) {
                    log.warn(LogUtil.format("组合品明细的物料组id查询不到物料信息,组合品编码:{},明细编码:{},款号编码:{},物料组ID:{},奶卡无聊编码:{}",
                            "checkBusinessTypeByChangeGoods"), skuEcode, itemSkuCode, psCPro.getEcode(), mDim2Id, replaceNaika);
                    return itemSkuCode;
                }

                skuDimEcodeMap.put(itemSkuCode, psProdimItemEcode);
            }
        } else {
            /*查询SKU*/
            List<String> skuCode = new ArrayList<>();
            skuCode.add(skuEcode);
            PsSkuResult psSkuResult = psRpcService.selectSkuInfoByeCodes(skuCode);
            log.info(LogUtil.format("skuInfo:{}", "skuInfo"),
                    psSkuResult != null ? JSONObject.toJSONString(psSkuResult) : null);
            if (psSkuResult == null || CollectionUtils.isEmpty(psSkuResult.getProSkus())) {
                log.info(LogUtil.format("条码信息查商品信息失败！", "proCode"));
                return skuEcode;
            }

            /*查询PRO信息*/
            List<ProSku> proSkus = psSkuResult.getProSkus();
            List<String> proCode = new ArrayList<>();
            proCode.add(proSkus.get(0).getPsCProEcode());
            List<PsCPro> cProList = psRpcService.queryProByEcode(proCode);
            log.info(LogUtil.format("proCode:{},cProList:{}", "proCode"),
                    CollectionUtils.isNotEmpty(proCode) ? JSONObject.toJSONString(proCode) : null,
                    CollectionUtils.isNotEmpty(cProList) ? JSONObject.toJSONString(cProList) : null);
            if (CollectionUtils.isEmpty(cProList)) {
                return skuEcode;
            }
            /*从PRO信息中获取物料组ID*/
            Integer mDim2Id = cProList.get(0).getMDim2Id();
            if (mDim2Id == null) {
                return skuEcode;
            }

            //根据物料组id查询属性明细表
            PsCProdimItem psProdimItem = psRpcService.selectPsCProdimItem(mDim2Id.longValue());
            if (psProdimItem == null) {
                return skuEcode;
            }

            skuDimEcodeMap.put(productSku.getEcode(), psProdimItem.getEcode());
        }

        if (MapUtil.isEmpty(skuDimEcodeMap)) {
            return skuEcode;
        }

        List<String> naikaList = new ArrayList<>();
        naikaList.add(OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET.getCode());

        List<String> saleList = this.getAllSaleType();
        String businessTypeCode = ocBOrder.getBusinessTypeCode();
        log.info(LogUtil.format("业务类型:{},商品:{},订单:{},naika:{},sale:{},skuDimMap:{}", "checkBusinessTypeByChangeGoods"),
                businessTypeCode, skuEcode, ocBOrder.getBillNo(), naika, sale, JSON.toJSONString(skuDimEcodeMap));
        for (Map.Entry<String, String> skuDimEntry : skuDimEcodeMap.entrySet()) {
            if (naikaList.contains(businessTypeCode)) {
                String[] split = naika.split(",");
                List<String> naikaPro = Arrays.asList(split);
                if (!naikaPro.contains(skuDimEntry.getValue())) {
                    return skuDimEntry.getKey();
                }
            } else if (saleList.contains(businessTypeCode)) {
                String[] split = sale.split(",");
                List<String> sales = Arrays.asList(split);
                if (sales.contains(skuDimEntry.getValue())) {
                    return skuDimEntry.getKey();
                }
            } else {
                log.warn(LogUtil.format("业务类型非法:{},商品:{},订单:{},naika:{},sale:{}", "checkBusinessTypeByChangeGoods"),
                        businessTypeCode, skuEcode, ocBOrder.getBillNo(), naika, sale, skuDimEcodeMap);
                return "业务类型非法：" + skuEcode;
            }
        }

        return null;
    }

    private Boolean checkBusinessType(OcBOrder ocBOrder, String changeGoodsSKu) {

        List<String> naikaList = new ArrayList<>();
        naikaList.add(OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET.getCode());


        List<String> saleList = this.getAllSaleType();
        String businessTypeCode = ocBOrder.getBusinessTypeCode();

        log.info(LogUtil.format("业务类型:{},商品:{},订单:{},naika:{},sale:{}", "checkBusinessType"),
                businessTypeCode, changeGoodsSKu, ocBOrder.getBillNo(), naika, sale);
        List<String> skuCode = new ArrayList<>();
        skuCode.add(changeGoodsSKu);
        PsSkuResult psSkuResult = psRpcService.selectSkuInfoByeCodes(skuCode);
        log.info(LogUtil.format("skuInfo:{}", "skuInfo"),
                psSkuResult != null ? JSONObject.toJSONString(psSkuResult) : null);


        if (psSkuResult == null || CollectionUtils.isEmpty(psSkuResult.getProSkus())) {
            log.info(LogUtil.format("条码信息查商品信息失败！", "proCode"));
            return Boolean.FALSE;
        }
        List<ProSku> proSkus = psSkuResult.getProSkus();
        List<String> proCode = new ArrayList<>();
        proCode.add(proSkus.get(0).getPsCProEcode());
        List<PsCPro> cProList = psRpcService.queryProByEcode(proCode);

        log.info(LogUtil.format("proCode:{},cProList:{}", "proCode"),
                CollectionUtils.isNotEmpty(proCode) ? JSONObject.toJSONString(proCode) : null,
                CollectionUtils.isNotEmpty(cProList) ? JSONObject.toJSONString(cProList) : null);

        if (CollectionUtils.isEmpty(cProList)) {
            return Boolean.FALSE;
        }

        Integer mDim2Id = cProList.get(0).getMDim2Id();

        if (mDim2Id == null) {
            return Boolean.FALSE;
        }

        //取得物料组id查询属性明细表
        PsCProdimItem psProdimItem = psRpcService.selectPsCProdimItem(mDim2Id.longValue());

        if (psProdimItem == null) {
            return Boolean.FALSE;
        }

        log.info(LogUtil.format("psProdimItem.getEcode():{}", "psProdimItem"),
                psProdimItem.getEcode());

        if (naikaList.contains(businessTypeCode)) {
            String[] split = naika.split(",");
            List<String> naikaPro = Arrays.asList(split);
            if (!naikaPro.contains(psProdimItem.getEcode())) {
                return Boolean.FALSE;
            }
        } else if (saleList.contains(businessTypeCode)) {
            String[] split = sale.split(",");
            List<String> sales = Arrays.asList(split);
            if (sales.contains(psProdimItem.getEcode())) {
                return Boolean.FALSE;
            }
        } else {
            return Boolean.FALSE;
        }


        return Boolean.TRUE;
    }

    private List<String> checkBusinessType(OcBOrder ocBOrder, String currentSku, List<String> skuCodes) {
        List<String> errorSkuCodes = Lists.newArrayList();

        List<String> naikaList = new ArrayList<>();
        naikaList.add(OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode());
        naikaList.add(OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET.getCode());


        List<String> saleList = this.getAllSaleType();
        String businessTypeCode = ocBOrder.getBusinessTypeCode();

        log.info(LogUtil.format("业务类型:{},skuCodes:{},订单:{},naika:{},sale:{}", "checkBusinessType"), businessTypeCode, skuCodes, ocBOrder.getBillNo(), naika, sale);

        skuCodes.add(currentSku);
        PsSkuResult psSkuResult = psRpcService.selectSkuInfoByeCodes(skuCodes);
        log.info(LogUtil.format("skuInfo:{}", "skuInfo"), psSkuResult != null ? JSON.toJSONString(psSkuResult) : null);

        if (psSkuResult == null || CollectionUtils.isEmpty(psSkuResult.getProSkus())) {
            log.info(LogUtil.format("条码信息查商品信息失败！", "proCode"));
            return skuCodes;
        }
        List<ProSku> proSkus = psSkuResult.getProSkus();
        List<String> proEcodes = proSkus.stream().map(ProSku::getPsCProEcode).collect(Collectors.toList());
        List<PsCPro> cProList = psRpcService.queryProByEcode(proEcodes);
        Map<String, PsCPro> proMap = cProList.stream().collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));

        log.info(LogUtil.format("proEcodes:{},cProList:{}", "proCodes"), proEcodes, CollectionUtils.isNotEmpty(cProList) ? JSON.toJSONString(cProList) : null);

        if (CollectionUtils.isEmpty(cProList)) {
            return skuCodes;
        }

        for (String skuCode : skuCodes) {
            PsCPro psCPro = proMap.get(skuCode);
            if (psCPro == null) {
                errorSkuCodes.add(psCPro.getEcode());
                continue;
            }

            Integer mDim2Id = psCPro.getMDim2Id();
            if (mDim2Id == null) {
                errorSkuCodes.add(psCPro.getEcode());
                continue;
            }

            //取得物料组id查询属性明细表
            PsCProdimItem psProdimItem = psRpcService.selectPsCProdimItem(mDim2Id.longValue());
            if (psProdimItem == null) {
                errorSkuCodes.add(psCPro.getEcode());
                continue;
            }

            log.info(LogUtil.format("psProdimItem.getEcode():{}", "psProdimItem"), psProdimItem.getEcode());

            //不允许选择奶卡物料
            List<String> noReplaces = Arrays.asList(replaceNaika.split(","));
            if (noReplaces.contains(psProdimItem.getEcode())) {
                errorSkuCodes.add(psCPro.getEcode());
                continue;
            }

            if (naikaList.contains(businessTypeCode)) {
                String[] split = naika.split(",");
                List<String> naikaPro = Arrays.asList(split);
                if (!naikaPro.contains(psProdimItem.getEcode())) {
                    errorSkuCodes.add(psCPro.getEcode());
                }
            } else if (saleList.contains(businessTypeCode)) {
                String[] split = sale.split(",");
                List<String> sales = Arrays.asList(split);
                if (sales.contains(psProdimItem.getEcode())) {
                    errorSkuCodes.add(psCPro.getEcode());
                }
            } else {
                errorSkuCodes.add(psCPro.getEcode());
            }
        }

        return errorSkuCodes;
    }

    private List<String> getAllSaleType() {
        List<String> saleList = Lists.newArrayList();
        saleList.add(OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.CYCLE_PICK_UP.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.CYCLE_ORDER.getCode());
        saleList.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER.getCode());
        return saleList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteOcBOrderItem(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, User loginUser) {
        long begin = System.currentTimeMillis();

        SgOmsReleaseOutRequest request = new SgOmsReleaseOutRequest();
        List<SgOmsPhyStorageOutItemRequest> itemRequests = Lists.newArrayList();
        request.setRetailBillNo(ocBOrder.getBillNo());
        request.setLoginUser(loginUser);
        request.setIsReleaseOrClean(true);
        //逻辑占用单业务节点--删除赠品
        request.setServiceNode(103L);

        // 需要去释放库存的明细
        List<OcBOrderItem> items = Lists.newArrayList();
        // 要删除的明细ids
        StringBuilder itemIds = new StringBuilder();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            itemIds.append(ocBOrderItem.getId()).append(",");
            if (BigDecimalUtil.isNullReturnZero(ocBOrderItem.getQty())
                    .compareTo(BigDecimalUtil.isNullReturnZero(ocBOrderItem.getQtyLost())) != 0) {
                items.add(ocBOrderItem);
                SgOmsPhyStorageOutItemRequest itemRequest = new SgOmsPhyStorageOutItemRequest();
                itemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                itemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                itemRequest.setQty(ocBOrderItem.getQty());
                itemRequest.setRetailItemId(ocBOrderItem.getId());
                itemRequest.setSkuId(ocBOrderItem.getPsCSkuPtEcode());
                itemRequests.add(itemRequest);
            }
        }
        request.setItemRequestList(itemRequests);
        List<OcBOrderLog> logs = Lists.newArrayList();
        // 删除赠品
        for (OcBOrderItem orderItem : ocBOrderItems) {
            logs.add(omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.GIFT_DEL.getKey(), "条码:" + orderItem.getPsCSkuEcode() + "删除成功", null, null, loginUser));
        }
        long time1 = System.currentTimeMillis();
        if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.UNCONFIRMED.toInteger())) {
            ValueHolderV14 sgValueHolder = sgRpcService.releaseOutStock(request);
            if (!sgValueHolder.isOK()) {
                throw new NDSException(sgValueHolder.getMessage());
            }
            // 释放库存
            if (!items.isEmpty()) {
                for (OcBOrderItem orderItem : items) {
                    logs.add(omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), "条码:" + orderItem.getPsCSkuEcode() + "释放库存成功", null, null, loginUser));
                    //加入占单表 todo 这个地方应该是有问题的
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(logs)) {
            omsOrderLogService.save(logs);
        }
        if (StringUtils.isNotBlank(itemIds)) {
            // 批量删除明细
            ocBOrderItemMapper.deleteByItemIds(StringUtils.removeEnd(itemIds.toString(), ","), ocBOrder.getId());
        }
        long time2 = System.currentTimeMillis();
        List<OcBOrderItem> ocBOrderItems1 = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        if (CollectionUtils.isEmpty(ocBOrderItems1)) {
            order.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
        } else {

            BigDecimal conutQty = ocBOrderItems1.stream().map(OcBOrderItem::getQty).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal lostQtyCount = BigDecimal.ZERO;
//            for (OcBOrderItem ocBOrderItem : ocBOrderItems1) {
//                if (ocBOrderItem.getQtyLost() != null) {
//                    lostQtyCount = lostQtyCount.add(ocBOrderItem.getQtyLost());
//                }
//            }
            List<OcBOrderItem> collect = ocBOrderItems1.stream().filter(p -> Objects.nonNull(p.getProType()) && p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
            List<OcBOrderItem> isGift = ocBOrderItems1.stream().filter(p -> Objects.nonNull(p.getIsGift()) && p.getIsGift() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(isGift)) {
                order.setIsHasgift(1);
            } else {
                order.setIsHasgift(0);
            }
            if (CollectionUtils.isNotEmpty(collect)) {
                order.setIsCombination(1);
            } else {
                order.setIsCombination(0);
            }
            order.setQtyAll(conutQty);

//            if (lostQtyCount.compareTo(BigDecimal.ZERO) == 0) {
//                order.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
//            }
            if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                //order.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
            }
        }
        ocBOrderMapper.updateById(order);
    }

    /**
     * 是否可以删除
     *
     * @return
     */
    private boolean isCanDelete(OcBOrderItem item) {
        boolean isPlatform = GiftTypeEnum.PLATFORM.getVal().equals(item.getGiftType());
        // 成交金额是0
        boolean realAmtIsZero = BigDecimalUtil.isNullReturnZero(item.getRealAmt()).compareTo(BigDecimal.ZERO) == 0;
        // 非平台商品（即手工添加的商品） 且 成交金额是0
        return !isPlatform && realAmtIsZero;
    }


    /**
     * 检查订单状态和 订单SKU 是不是存在
     *
     * @param ocBOrder       订单
     * @param changeGoodsSKu sku
     * @param type           调用类型   0  代表 删除商品   1 代表替换 和新增商品
     * @return
     */
    private ValueHolderV14<List<OcBOrderItem>> checkStuatsAndSku(OcBOrder ocBOrder, String changeGoodsSKu, Integer type, Long itemId, boolean manyReplace) {
//        1.当前订单状态为 待审核/缺货
//        2.当前订单存在该SKU。
        ValueHolderV14 vh = new ValueHolderV14();
        ArrayList list = new ArrayList();
        list.add(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
        list.add(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
        if (!list.contains(ocBOrder.getOrderStatus())) {
            vh.setMessage(ocBOrder.getBillNo() + "只有订单是待审核或者缺货的才能操作");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        if (type == 1) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("查询完成");
            return vh;
        }
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        if (itemId != null) {
            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.queryOrderById(itemId, ocBOrder.getId());
            changeGoodsSKu = ocBOrderItem.getPsCSkuEcode();
            ocBOrderItems.add(ocBOrderItem);
        } else {
            // ocBOrderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", ocBOrder.getId()).eq("ps_c_sku_ecode", changeGoodsSKu).eq("is_gift", 1));
            ocBOrderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id",
                    ocBOrder.getId()).eq("ps_c_sku_ecode", changeGoodsSKu));
        }

        if (manyReplace){
            ocBOrderItems = ocBOrderItems.stream()
                    .filter(item -> item.getProType() != null
                            && SkuType.NORMAL_PRODUCT == item.getProType().intValue()
                            && !InreturningStatus.INRETURN_YES.equals(item.getRefundStatus()))
                    .collect(Collectors.toList());
        }else {
            // 只用pro_type= 4 或 0,且非退款完成的明细才能替换商品
            // 20240903 pro_type= 2 也才能替换商品
            ocBOrderItems = ocBOrderItems.stream()
                    .filter(item -> item.getProType() != null
                            && (SkuType.NO_SPLIT_COMBINE == item.getProType().intValue()
                            || SkuType.COMBINE_PRODUCT == item.getProType().intValue()
                            || SkuType.NORMAL_PRODUCT == item.getProType().intValue())
                            && !InreturningStatus.INRETURN_YES.equals(item.getRefundStatus()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            vh.setMessage(ocBOrder.getBillNo() + "该订单不存在可替换的SKU" + changeGoodsSKu);
            vh.setCode(ResultCode.FAIL);
            return vh;
        } else {
            vh.setMessage("查询成功");
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(ocBOrderItems);
            return vh;
        }
    }


    // 删除赠品 查询明细
    private ValueHolderV14<List<OcBOrderItem>> checkStuatsAndSkuReturnItem(OcBOrder ocBOrder, String changeGoodsSKu, Integer type, JSONArray itemIdJSONArray) {
//        1.当前订单状态为 待审核/缺货
//        2.当前订单存在该SKU。
        ValueHolderV14 vh = new ValueHolderV14();
        ArrayList list = new ArrayList();
        list.add(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
        list.add(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
        if (!list.contains(ocBOrder.getOrderStatus())) {
            vh.setMessage(ocBOrder.getBillNo() + ":订单状态非待审核或者缺货，不允许操作删除赠品！");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        if (type == 1) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("查询完成");
            return vh;
        }
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        if (itemIdJSONArray != null) {
            List<Long> itemIdList = JSON.parseArray(itemIdJSONArray.toJSONString(), Long.class);
            List<OcBOrderItem> ocBOrderItemsList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), itemIdList);
            ocBOrderItems.addAll(ocBOrderItemsList);
        } else {
            ocBOrderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", ocBOrder.getId()).eq("ps_c_sku_ecode", changeGoodsSKu).eq("is_gift", 1));
        }

        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            vh.setMessage(ocBOrder.getBillNo() + ":该订单的SKU不存在");
            vh.setCode(ResultCode.FAIL);
            return vh;
        } else {
            vh.setMessage("查询成功");
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(ocBOrderItems);
            return vh;
        }
    }

    /**
     * 按照查询条件来查询 满足条件的订单id
     *
     * @param param
     * @param loginUser
     * @param usrPem
     * @return
     */
    private List<Long> checkParamForIds(JSONObject param, User loginUser, UserPermission usrPem) {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pageSize = config.getProperty("search.order.size", 500);
        if (param.containsKey("ids")) {
            // 如果包含ids 就不用查询id
            JSONArray ids = param.getJSONArray("ids");
            if (ids.size() > 0) {
                return new ArrayList<>();
            }
            ArrayList idsCollect = new ArrayList();
            for (int i = 0; i < ids.size(); i++) {
                idsCollect.add(ids.getLong(i));
            }
            return idsCollect;
        } else {
            // 如果不包括ids就调用西文接口查询id集合
            // 特殊处理在参数中加入一个字段，用来标志我只查询id
            param.put("SearchForId", 1);
            if (param.containsKey("page")) {
                JSONObject page = param.getJSONObject("page");
                page.put("pageSize", pageSize);
            }
            ValueHolderV14<QueryOrderListResult> queryOrderListResultValueHolderV14 = null;
            try {
                queryOrderListResultValueHolderV14 = listQueryService.queryOrderList(param.toJSONString(), loginUser, usrPem);
                if (ResultCode.SUCCESS == queryOrderListResultValueHolderV14.getCode()) {
                    QueryOrderListResult data = queryOrderListResultValueHolderV14.getData();
                    if (data != null && !CollectionUtils.isEmpty(data.getIds())) {
                        return data.getIds();
                    }
                    return new ArrayList<>();
                }
            } catch (Exception e) {
                log.error(LogUtil.format("查询服务异常: {}"), Throwables.getStackTraceAsString(e));
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    public ValueHolderV14 batchAddGoods(JSONObject param, User user, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }

        // @20200718 判断的逻辑问题：应该是要判断商品和数量都不能为空
        if (!param.containsKey("changeGoodsSku") || param.getString("changeGoodsSku") == null
                || !param.containsKey("qty") || param.getString("qty") == null) {
            vh.setMessage("请选择需要新增的赠品和新增的数量");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        String changeGoodsSKu = (String) param.get("changeGoodsSku");
        Integer qty = param.getInteger("qty");
        List<Long> longs = new ArrayList();
        if (param.containsKey("ids")) {
            if (!param.getJSONArray("ids").isEmpty()) {
                JSONArray ids = param.getJSONArray("ids");
                for (int i = 0; i < ids.size(); i++) {
                    longs.add(ids.getLong(i));
                }
            }
        } else {
            longs = checkParamForIds(param, user, usrPem);
        }
        if (CollectionUtils.isEmpty(longs)) {
            vh.setMessage("查询不到数据请检查后重试");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        JSONArray jsonArray = new JSONArray();
        AtomicInteger success = new AtomicInteger(0);

        List<List<Long>> longIdList = Lists.partition(longs, 50);

        List<Future<BatchOperationGoodsResult>> list = new ArrayList<>();

        for (List<Long> longList : longIdList) {
            list.add(batchOperationGoodsPollExecutor.submit(new BatchOperationGoods(longList, changeGoodsSKu, user, qty)));
        }
        for (Future<BatchOperationGoodsResult> future : list) {
            try {
                BatchOperationGoodsResult batchOperationGoodsResult = future.get();
                if (batchOperationGoodsResult != null) {
                    synchronized (jsonArray) {
                        jsonArray.addAll(batchOperationGoodsResult.getJsonArray());
                    }
                    success.addAndGet(batchOperationGoodsResult.getSuccess());
                }

            } catch (Exception e) {
                log.error(LogUtil.format("批量新增赠品异常: {}"), Throwables.getStackTraceAsString(e));
            }
        }

        if (longs.size() == success.get()) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("新增赠品成功" + success + "条数据，新增赠品失败" + (longs.size() - success.get()) + "条数据");

        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("新增赠品成功" + success + "条数据，新增赠品失败" + (longs.size() - success.get()) + "条数据");

        }
        vh.setData(jsonArray);
        return vh;
    }

    /**
     * 执行批量替换商品
     *
     * @param param
     * @param user
     * @param orderIds
     * @param itemId
     * @param currentSku
     * @return 执行结果列表  data：OM单号，code：是否成功，message：执行结果
     */
    private ValueHolderV14<List<ValueHolderV14<String>>> doBatchChangeGoods(JSONObject param, User user, List<Long> orderIds, Long itemId, String currentSku) {
        /*限制数量*/
        Integer limitQty = param.getInteger("limitQty");
        /*替换数量*/
        Integer changeGoodsQty = param.getInteger("changeGoodsQty");
        /*所要更换的SKU*/
        String skuCode = param.getString("sku_code");

        boolean equalProportion = param.getBooleanValue("equalProportion");

        //多sku替换
        List<ManyReplaceInfo> replaceInfos = Lists.newArrayList();
        JSONArray skus = param.getJSONArray("skus");
        if (skus != null) {
            replaceInfos = JsonUtils.jsonToList(ManyReplaceInfo.class, skus.toJSONString());
        }

        /*用来统计成功和失败条数*/
        AtomicInteger success = new AtomicInteger(0);
        List<ValueHolderV14<String>> v14List = Collections.synchronizedList(new ArrayList<>());

        List<List<Long>> operateIdList = Lists.partition(orderIds, 50);
        List<Future<BatchReplaceGoodsTaskResult>> countList = new ArrayList<>();
        for (List<Long> operateId : operateIdList) {
            countList.add(batchReplaceGoodsPollExecutor.submit(new BatchReplaceGoodsTask(operateId, user, skuCode, limitQty, changeGoodsQty, currentSku, itemId, replaceInfos, equalProportion)));
        }
        for (Future<BatchReplaceGoodsTaskResult> future : countList) {
            try {
                BatchReplaceGoodsTaskResult result = future.get();
                success.addAndGet(result.getSuccess());
                v14List.addAll(result.getV14List());
            } catch (Exception e) {
                log.error(LogUtil.format("批量替换商品执行出错，错误信息:{}", "batchOperationGoodsService.doBatchChangeGoods"), Throwables.getStackTraceAsString(e));
            }
        }
        String msg = "批量替换商品执行完成，成功条数:【" + success + "】，失败条数：【" + (orderIds.size() - success.get()) + "】";
        log.info(LogUtil.format(msg, "batchOperationGoodsService.doBatchChangeGoods"));
        return new ValueHolderV14<List<ValueHolderV14<String>>>(v14List, CollectionUtils.isEmpty(v14List) ? ResultCode.FAIL : ResultCode.SUCCESS, msg);
    }

    @Data
    class BatchOperationGoodsResult {
        private Integer success;
        private JSONArray jsonArray;
    }


    /**
     * 新增商品主流程
     *
     * @param ocBOrder       订单
     * @param loginUser      登录用户
     * @param changeGoodsSKu 新增的商品条码
     * @param qty            数量
     * @return
     */
    public ValueHolderV14 addGoodsMainStep(OcBOrder ocBOrder, User loginUser, String changeGoodsSKu, Integer qty) {
        ValueHolderV14 vh;
        long begin = System.currentTimeMillis();
        // step 001  1.当前订单状态为 待审核/缺货 2.当前订单存在该SKU。

        if (!checkBusinessType(ocBOrder, changeGoodsSKu)) {
            return new ValueHolderV14(ResultCode.FAIL, ocBOrder.getBillNo() + "勾选的订单不符合要求，不可以修改商品");
        }

        ValueHolderV14<List<OcBOrderItem>> v14 = checkStuatsAndSku(ocBOrder, changeGoodsSKu, 1, null, false);
        if (ResultCode.FAIL == v14.getCode()) {
            return v14;
        }
        long currentTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("addGoodsMainStep: {}", ocBOrder.getId()), currentTime - begin);
        }
        // 查看sku是否存在 addMerchandise方法里面会去校验
        // this.checkGoodsPrice(ocBOrder.getCpCShopId(), changeGoodsSKu);
        JSONObject packageParam = packageParam(ocBOrder, changeGoodsSKu, qty);
        long currentTime1 = System.currentTimeMillis();
        // 【增加商】按钮服务。
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("addGoodsMainStep.packageParam: {}", ocBOrder.getId()), packageParam.toJSONString());
        }
        BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
        vh = bean.addMerchandise(ocBOrder, changeGoodsSKu, qty, loginUser);
        long currentTime2 = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("addGoodsMainStep.addMerchandise: {}", ocBOrder.getId()), currentTime2 - currentTime1);
        }
        return vh;
    }


    /**
     * 添加商品
     *
     * @param ocBOrder       订单信息
     * @param changeGoodsSKu 添加的sku
     * @param qty            添加的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 addMerchandise(OcBOrder ocBOrder, String changeGoodsSKu, Integer qty, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        long begin = System.currentTimeMillis();
        try {
            ProductSku productSku = psRpcService.selectProductSku(changeGoodsSKu);
            if (productSku == null) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("所选赠品不存在!");
                return holder;
            }

            //校验是否组合、福袋
            if (productSku.getSkuType() == SkuType.GIFT_PRODUCT
                    || productSku.getSkuType() == SkuType.COMBINE_PRODUCT) {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("目前添加赠品不支持组合商品和福袋商品!");
                return holder;
            }

            // 调用订单得日志服务
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.GIFT_ADD.getKey(),
                    changeGoodsSKu + " 新增赠品成功！", null, null, user);
            List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
            // 为组合商品 新增一个未拆分的组合商品信息
            OcBOrderItem ocBOrderItem = this.packageDetails(ocBOrder, productSku, qty);
            if (productSku.getSkuType() == SkuType.COMBINE_PRODUCT) {
                List<OcBOrderItem> items = new ArrayList<>();
                items.add(ocBOrderItem);
                ocBOrderItems = omsConstituteSplitService.encapsulationParameter(items, ocBOrder, user, 0);
                ocBOrderItem.setProType((long) SkuType.NO_SPLIT_COMBINE);
                ocBOrderItem.setIsManualAdd("1");
                holder = batchOperationGoodsService.handleOrder(ocBOrder, ocBOrderItems, ocBOrderItem, user);
            } else {
                ocBOrderItems.add(ocBOrderItem);
                holder = batchOperationGoodsService.handleOrder(ocBOrder, ocBOrderItems, null, user);
            }
            if (holder.isOK()) {
                holder.setCode(ResultCode.SUCCESS);
                holder.setMessage("赠品添加成功!");
            } else {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(holder.getMessage());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("添加赠品异常: {}"), Throwables.getStackTraceAsString(e));
            holder.setCode(ResultCode.FAIL);
            holder.setMessage(e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return holder;
    }


    /**
     * 赠品处理订单方法
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param ocBOrderItem
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 handleOrder(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, OcBOrderItem ocBOrderItem, User user) {
        ValueHolderV14 handleResult = new ValueHolderV14();
        List<OcBOrderItem> bOrderItemList = new ArrayList<>(ocBOrderItems);
        if (ocBOrderItem != null) {
            bOrderItemList.add(ocBOrderItem);
        }
        List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        Map<String, List<OcBOrderItem>> map = new HashMap<>();
        for (OcBOrderItem item : items) {
            // 不用实付金额判断，用添加赠品类型为中台赠品是否存在
            if ("1".equals(item.getGiftType()) && item.getProType() != SkuType.COMBINE_PRODUCT &&
                    item.getProType() != SkuType.NO_SPLIT_COMBINE) {
                if (!map.containsKey(item.getPsCSkuEcode())) {
                    List<OcBOrderItem> list = new ArrayList<>();
                    list.add(item);
                    map.put(item.getPsCSkuEcode(), list);
                } else {
                    List<OcBOrderItem> items1 = map.get(item.getPsCSkuEcode());
                    items1.add(item);
                    map.put(item.getPsCSkuEcode(), items1);
                }
            }
        }

        for (OcBOrderItem bOrderItem : bOrderItemList) {
            List<OcBOrderItem> currentItems = map.get(bOrderItem.getPsCSkuEcode());
            // 假如不存在系统赠品,或者系统赠品不存在挂靠关系
            if (CollectionUtils.isEmpty(currentItems) || StringUtils.isNotBlank(currentItems.get(0).getGiftRelation())) {
                bOrderItem.setGiftType(GiftTypeEnum.SYSTEM.getVal());
                bOrderItem.setIsManualAdd("1");
                bOrderItem.setIsGift(1);
                bOrderItem.setPsCSkuPtEcode(null);
                bOrderItem.setGiftRelation("");
                ocBOrderItemMapper.insert(bOrderItem);
            } else {
                OcBOrderItem item = currentItems.get(0);
                BigDecimal countQty = currentItems.stream().map(OcBOrderItem::getQty).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                OcBOrderItem orderItem = new OcBOrderItem();
                orderItem.setId(item.getId());
                orderItem.setQty(bOrderItem.getQty().add(countQty));
                orderItem.setOcBOrderId(item.getOcBOrderId());
                omsOrderItemService.updateOcBOrderItem(orderItem, item.getOcBOrderId());
            }
        }

        /**2021/07/21 新增逻辑 添加赠品后,释放之前明细库存,将所有订单明细（包含）重新进行寻源占单*/
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        List<OcBOrderItem> ocBOrderItems1 = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        //释放之前明细库存处理方法
        batchOperationGoodsService.handleOccupy(ocBOrder, null, user);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用释放占用库存buidRequestAndReleaseStock方法入参ocBOrder:{},itemList:{}",
                    ocBOrder.getId()), JSON.toJSONString(ocBOrder), JSON.toJSONString(items));
        }
        long startTime = System.currentTimeMillis();
        ValueHolderV14 vh14 = this.buidRequestAndReleaseStock(ocBOrder, items, user);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("释放占用库存.返回:{}", ocBOrder.getId()), JSON.toJSONString(vh14));
        }
        AssertUtil.assertException(!vh14.isOK(), vh14.getMessage());
        /** 2021/07/21 新增逻辑 添加赠品后,释放之前明细库存,将所有订单明细（包含）重新进行寻源占单*/

        List<OcBOrderItem> collect = ocBOrderItems1.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            order.setIsCombination(1);
        } else {
            order.setIsCombination(0);
        }
        order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        // tagger.007 添加商品保存完明细后，重新打标 @20200710
        TaggerManager.get().doTag(order, ocBOrderItems1);
        omsOrderService.updateOrderInfo(order);
        //todo 处理效期
        for (OcBOrderItem item : ocBOrderItems1) {
            item.setExpiryDateRange(null);
            item.setExpiryDateType(null);
        }
        OcBOrderParam param = new OcBOrderParam();
        param.setOcBOrder(ocBOrder);
        param.setOrderItemList(ocBOrderItems1);
        omsExpiryDateStService.expiryDateStService(param, user, "(添加商品)");
        //加入占单表
        // 不释放卡单
        if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
            handleResult.setCode(ResultCode.SUCCESS);
            handleResult.setMessage("添加赠品处理成功！");
            return handleResult;
        }
        omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
        handleResult.setCode(ResultCode.SUCCESS);
        handleResult.setMessage("添加赠品处理成功！");
        return handleResult;
    }

    /**
     * 构建释放库存参数,并进行释放库存
     *
     * @return
     */
    public ValueHolderV14 buidRequestAndReleaseStock(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems1, User user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "操作成功");

        if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus()) || CollectionUtils.isEmpty(ocBOrderItems1)) {
            //缺货不用释放库存
            return vh;
        }

        //调用指定商品释放库存服务
        SgOmsShareOutRequest request = cancelOrderMergeService.buildSgOmsShareOutRequest(ocBOrder, ocBOrderItems1, user);
        vh = sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItems1);
        return vh;
    }

    /**
     * 2021/07/22
     * 释放占单库存方法封装
     *
     * @param ocBOrder
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleOccupy(OcBOrder ocBOrder, List<OcBOrderItem> exchangeBeforeItems, User user) {
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询明细数据处理前itemList: {}", ocBOrder.getId()), JSON.toJSONString(itemList));
        }
        // 替换之后的明细
        if (CollectionUtils.isNotEmpty(exchangeBeforeItems)) {
            for (OcBOrderItem exchangeBeforeItem : exchangeBeforeItems) {
                exchangeBeforeItem.setQty(BigDecimal.ZERO);
            }
            itemList.addAll(exchangeBeforeItems);
            // 删除相同的item
            this.removeSameItem(itemList);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询明细数据处理后itemList: {}", ocBOrder.getId()), JSON.toJSONString(itemList));
        }
        sgOccupiedInventoryService.exchangeSkuBuildOrderInfoHandle(ocBOrder, itemList, user);
    }

    /**
     * 占用库存并清空库存
     */
    private boolean occupiedInventory(OcBOrderRelation relation, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("占用库存入参: {}", relation.getOrderId()), JSON.toJSONString(relation));
        }
        ValueHolderV14 sgValueHolder = sgRpcService.querySearchStockAndModifyGoodsInfo(relation, loginUser);
        if (!sgValueHolder.isOK()) {
            List<OcBOrderItem> orderItemList = relation.getOrderItemList();
            //更新明细的缺货数量
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                this.addLog(relation.getOrderInfo(), loginUser, "条码:" + ocBOrderItem.getPsCSkuEcode() + "占用库存失败");

            }
            return false;
        } else {
            this.addLog(relation.getOrderInfo(), loginUser, "占用库存成功");
            return true;
        }

    }

    private void addLog(OcBOrder ocBOrder, User user, String content) {
        try {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), content, "", "", user);
        } catch (Exception ex) {
            log.error(LogUtil.format("调用日志服务异常: {}", ocBOrder.getId()), Throwables.getStackTraceAsString(ex));
        }
    }


    /**
     * 封装组合商品 订单明细参数
     *
     * @param ocBOrder
     * @param productSku
     * @return
     */
    public OcBOrderItem packageDetails(OcBOrder ocBOrder, ProductSku productSku, Integer qty) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(item);

        //活动编号. 默认赋值为null
        item.setActiveId(null);

        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);

        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        //商品数字编号
        item.setIsLackstock(0);
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //标准价。【淘宝订单中间表】明细表的“标准价”
        item.setPriceList(productSku.getPricelist());
        //数量
        item.setQty(BigDecimal.valueOf(qty));
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        item.setRealAmt(BigDecimal.ZERO);
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);

        //规格。商品条码. normsdetailnames
        //条码id
        //标准重量。商品条码. weight
        //条码编码。
        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
        initialTaobaoOrderItem(productSku, item, qty);
        item.setQtyGroup(BigDecimal.valueOf(qty)); //组合商品数量

        //库位。不用赋值
//        item.setStoreSite(null);
        List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(items)) {
            item.setTid(items.get(0).getTid());
        } else {
            item.setTid(ocBOrder.getTid());
        }
        item.setIsManualAdd("1");
        //虚拟商品的成交金额
        item.setRealAmt(BigDecimal.ZERO);
        //总的优惠金额
        item.setAmtDiscount(BigDecimal.ZERO);
        // 调整金额 = 单行实际成交金额 - （原价 * 数量）
        //总调整金额

        item.setAdjustAmt(BigDecimal.ZERO);
        //总的平坦金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        item.setPriceActual(BigDecimal.ZERO);
        item.setPriceTag(productSku.getPricelist());
        item.setPrice(BigDecimal.ZERO);
        item.setIsManualAdd("1");
        return item;

    }


    private void initialTaobaoOrderItem(ProductSku productSku, OcBOrderItem item, Integer qty) {
        boolean flag = productSku.getSkuType() == SkuType.COMBINE_PRODUCT;
        item.setPsCProId(productSku.getProdId());
        // ProECode
        item.setPsCProEcode(productSku.getProdCode());
        item.setPsCSkuId(productSku.getId());
        item.setSex(productSku.getSex());
        //2019-08-30吊牌价改为取商品表数据
        item.setPriceTag(productSku.getPricelist()); //吊牌价
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCProMaterieltype(productSku.getMaterialType());
        item.setStandardWeight(productSku.getWeight());
        item.setSkuSpec(productSku.getSkuSpec());
        item.setProType((long) productSku.getSkuType());
        item.setPsCSkuId(productSku.getId());
        item.setPsCProEname(productSku.getName()); //商品名称
        item.setPsCSkuEname(productSku.getSkuName());
        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }
        // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
        String psSkuEcode = productSku.getSkuEcode();
        if (checkIsNeedTransferSkuUpperCase()) {
            psSkuEcode = StringUtils.upperCase(psSkuEcode);
        }
        if (flag) {
            //为福袋或者组合商品
            item.setPsCSkuEcode(psSkuEcode); //虚拟条码
            //由于数据库做了对尺寸code和商品code做了非空限制
            item.setPsCSizeEcode(psSkuEcode);
            item.setPsCProEcode(psSkuEcode);
            item.setQtyGroup(BigDecimal.valueOf(qty)); //组合商品数量
        } else {
            item.setPsCSkuEcode(psSkuEcode);
        }
        String isEnableExpiry = productSku.getIsEnableExpiry();
        if ("Y".equals(isEnableExpiry)) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error(LogUtil.format("checkIsNeedTransferSkuUpperCase: {}"), Throwables.getStackTraceAsString(ex));
            return true;
        }
    }


    private JSONObject packageParam(OcBOrder ocBOrder, String changeGoodsSKu, Integer qty) {
        JSONObject needParam = new JSONObject();
        JSONObject ids = new JSONObject();
        JSONArray arrayMain = new JSONArray();
        arrayMain.add(ocBOrder.getId());
        ids.put("id", arrayMain);
        JSONArray arrayItem = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("PS_C_SKU_ECODE", changeGoodsSKu);
        item.put("QTY", qty);
        arrayItem.add(item);
        needParam.put("OcBorderDto", ids);
        needParam.put("OcBorderItemDto", arrayItem);
        return needParam;
    }

    /**
     * 校验商品价格
     *
     * @param cpCShopId
     * @param changeGoodsSKu
     * @return
     */
    private boolean checkGoodsPrice(Long cpCShopId, String changeGoodsSKu) {
        ProductSku productSku = psRpcService.selectProductSku(changeGoodsSKu);
        if (productSku == null) {
            throw new NDSException("查询不到所要添加的商品");
        }
        return true;
    }

    /**
     * 批量修改商品服务
     *
     * @param param
     * @param user
     * @param usrPem
     * @return
     */
    @Deprecated
    public ValueHolderV14 batchChangeGoods(JSONObject param, User user, UserPermission usrPem) {

        ValueHolderV14 vh = new ValueHolderV14();
        if (param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        //限制数量
        Integer limitQty = param.getInteger("limitQty");
        //替换数量
        Integer changeGoodsQty = param.getInteger("changeGoodsQty");
        String currentSku = param.getString("changeGoodsSku"); // 当前的sku
        String skuCode = param.getString("sku_code"); //所要跟换的SKU
        Long itemId = param.getLong("itemId"); //所要跟换的SKU
        String ids = param.getString("ids");

        boolean equalProportion = param.getBooleanValue("equalProportion");

        List<Long> orderIds;
        if (StringUtils.isEmpty(ids)) {
            vh.setMessage("请选择需要替换商品的订单");
            vh.setCode(ResultCode.FAIL);
            return vh;
        } else {
            orderIds = JSONArray.parseArray(ids, Long.class);
        }
        if (itemId == null && StringUtils.isEmpty(currentSku)) {
            vh.setMessage("未指定需要替换的商品");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }

        BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
        Integer success = 0;  // 用来统计成功和失败条数
        List<ValueHolderV14> v14List = new ArrayList<>();
        for (Long aLong : orderIds) {
            ValueHolderV14 valueHolderV14 = new ValueHolderV14();
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(aLong);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocOrder = ocBOrderMapper.selectById(aLong);

                    ValueHolderV14 v14 = bean.changeGoodsMainStep(ocOrder, user, skuCode, currentSku, itemId, changeGoodsQty, limitQty, Lists.newArrayList(), equalProportion);
                    if (ResultCode.SUCCESS == v14.getCode()) {
                        String sku = (String) v14.getData();
                        success++;
                        this.modifyOrderStatus(ocOrder.getId(), Optional.ofNullable(ocOrder.getIsModifiedOrder()).orElse(0), null);
                        try {
                            splitOutStockOrderService.handleOutStockOrderForOrderSplit(aLong, user);
                        } catch (Exception e) {
                            log.error(LogUtil.format("替换商品,更新缺货拆单数据失败: {}"), Throwables.getStackTraceAsString(e));
                        }
                        OcBOrder newOcOrder = new OcBOrder();
                        newOcOrder.setId(ocOrder.getId());
                        updateOrderAllSku(newOcOrder);
                    } else {
                        //调用订单得日志服务
                        omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                                OrderLogTypeEnum.GOODS_REPLACE.getKey(),
                                "替换" + skuCode + " 商品失败！原因:" + v14.getMessage(), null, null, user);
                    }
                    v14List.add(v14);
                } else {
                    throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
                }
            } catch (Exception e) {
                valueHolderV14.setCode(-1);
                valueHolderV14.setMessage("替换商品失败:" + e.getMessage());
                log.error(LogUtil.format("替换商品,替换商品异常: {}"), Throwables.getStackTraceAsString(e));
                //调用订单得日志服务
                omsOrderLogService.addUserOrderLog(aLong, null,
                        OrderLogTypeEnum.GOODS_REPLACE.getKey(), "替换" +
                                "" + skuCode + " 商品失败！原因：" + e.getMessage(), null, null, user);
                v14List.add(valueHolderV14);
            } finally {
                redisLock.unlock();
            }
        }
        if (orderIds.size() == success) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("替换商品成功" + success + "条数据，替换商品失败" + (orderIds.size() - success) + "条数据");
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("替换商品成功" + success + "条数据，替换商品失败" + (orderIds.size() - success) + "条数据");
        }
        vh.setData(v14List);
        return vh;
    }

    /**
     * 批量修改商品服务(异步)
     *
     * @param param
     * @param user
     * @param usrPem
     * @return
     */
    public ValueHolderV14 batchChangeGoodsAsync(JSONObject param, User user, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }

        String ids = param.getString("ids");
        List<Long> orderIds;
        if (StringUtils.isEmpty(ids)) {
            vh.setMessage("请选择需要替换商品的订单");
            vh.setCode(ResultCode.FAIL);
            return vh;
        } else {
            orderIds = JSONArray.parseArray(ids, Long.class);
        }

        // 所要跟换的SKU
        Long itemId = param.getLong("itemId");
        // 当前的sku
        String currentSku = param.getString("changeGoodsSku");
        if (itemId == null && StringUtils.isEmpty(currentSku)) {
            vh.setMessage("未指定需要替换的商品");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }

        //一换多，相同商品不允许多行，数量限制为1
        JSONArray skus = param.getJSONArray("skus");
        if (skus != null) {
            ValueHolderV14 vh1 = manyReplaceCheck(param, vh, skus);
            if (vh1 != null) {
                return vh1;
            }
        }

        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setBeginTime(new Timestamp(System.currentTimeMillis()));
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("导出");
        asyncTaskBody.setTaskType("批量替换商品");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        commonThreadPoolExecutor.submit(() -> {
            ValueHolderV14<String> v14 = batchChangeGoodsAndWrite2Excel(param, user, orderIds, itemId, currentSku);
            if (ResultCode.SUCCESS == v14.getCode()) {
                //任务完成
                asyncTaskBody.setExportUrl(v14.getData());
            }

            asyncTaskBody.setEndTime(new Timestamp(System.currentTimeMillis()));
            log.info(LogUtil.format("批量替换商品结果为:{}", "batchOperationGoodsService.batchChangeGoodsSync"), JSON.toJSONString(v14));
            asyncTaskManager.afterExecute(user, asyncTaskBody, v14.toJSONObject());
        });

        log.info(LogUtil.format("批量替换商品任务添加成功，条数：{}", "batchOperationGoodsService.batchChangeGoodsSync"),
                orderIds.size());
        return new ValueHolderV14<String>(ResultCode.SUCCESS, "批量替换商品任务添加成功，请在【我的任务】下载结果");
    }

    private ValueHolderV14 manyReplaceCheck(JSONObject param, ValueHolderV14 vh, JSONArray skus) {
        /*限制数量*/
        Integer limitQty = param.getInteger("limitQty");
        /*替换数量*/
        Integer changeGoodsQty = param.getInteger("changeGoodsQty");
        if (!limitQty.equals(1) || !changeGoodsQty.equals(1)) {
            vh.setMessage("多行替换数量必须全部为1");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }

        Set<String> skuCodesTips = Sets.newHashSet();
        List<String> skuCodes = Lists.newArrayList();
        for (Object o : skus) {
            JSONObject jsonObject = (JSONObject) o;
            String skuCode = jsonObject.getString("skuCode");
            if (skuCodes.contains(skuCode)) {
                skuCodesTips.add(skuCode);
            } else {
                skuCodes.add(skuCode);
            }
        }

        if (CollectionUtils.isNotEmpty(skuCodesTips)) {
            vh.setMessage("相同商品不允许重复选择:" + skuCodesTips);
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        return null;
    }

    private ValueHolderV14<String> batchChangeGoodsAndWrite2Excel(JSONObject param, User user, List<Long> orderIds, Long itemId, String currentSku) {
        ValueHolderV14<List<ValueHolderV14<String>>> retData;
        List<ValueHolderV14<String>> retList;
        try {
            retData = doBatchChangeGoods(param, user, orderIds, itemId, currentSku);
            if (!retData.isOK()) {
                return new ValueHolderV14<>(ResultCode.FAIL, retData.getMessage());
            }

            retList = retData.getData();
        } catch (Exception e) {
            log.error(LogUtil.format("执行替换商品时出错，入参：{}，错误信息:{}", "batchOperationGoodsService.batchChangeGoodsAndWrite2Excel"),
                    param.toJSONString(), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, "执行替换商品时出错" + e.getMessage());
        }

        if (CollectionUtils.isEmpty(retList)) {
            log.error(LogUtil.format("替换商品导出结果为空，入参", "batchOperationGoodsService.batchChangeGoodsAndWrite2Excel"),
                    param.toJSONString());
            return new ValueHolderV14<>(ResultCode.FAIL, "替换商品导出结果为空");
        }

        String url;
        try {
            url = write2Excel(retList, user);
        } catch (Exception e) {
            log.error(LogUtil.format("替换商品导出结果时出错，，入参：{}，写入内容：{}，错误信息:{}", "batchOperationGoodsService.batchChangeGoodsAndWrite2Excel"),
                    param.toJSONString(), JSON.toJSONString(retList), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, "替换商品导出结果时出错" + e.getMessage());
        }

        return new ValueHolderV14<>(url, ResultCode.SUCCESS, retData.getMessage());
    }

    private String write2Excel(List<ValueHolderV14<String>> retList, User user) {
        //列名
        String[] columnNames = {"是否成功", "单据编号", "替换结果"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"code", "data", "message"};
        List<String> k = Lists.newArrayList(keys);

        Workbook hssfWorkbook = exportUtil.execute("替换结果", "批量替换商品", c, k, retList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "批量替换商品执行结果", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
    }

    class BatchOperationGoods implements Callable<BatchOperationGoodsResult> {

        private final List<Long> orderIdList;
        private final String changeGoodsSKu;
        private final User user;
        private final Integer qty;

        public BatchOperationGoods(List<Long> orderIdList, String changeGoodsSKu, User user, Integer qty) {
            this.orderIdList = orderIdList;
            this.changeGoodsSKu = changeGoodsSKu;
            this.user = user;
            this.qty = qty;
        }

        @Override
        public BatchOperationGoodsResult call() throws Exception {
            BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
            int success = 0;  // 用来统计成功和失败条数
            JSONArray jsonArray = new JSONArray();
            BatchOperationGoodsResult result = new BatchOperationGoodsResult();
            for (Long aLong : orderIdList) {
                JSONObject jsonObject = new JSONObject();
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(aLong);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                String billNo = "";
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        OcBOrder ocOrder = ocBOrderMapper.selectById(aLong);
                        AssertUtil.notNull(ocOrder, aLong + "不存在! ");

                        //指定业务类型禁止添加赠品
                        if (StringUtils.isNotBlank(addGiftExclude)) {
                            String businessTypeCode = ocOrder.getBusinessTypeCode();
                            List<String> excludeBusinessTypes = Arrays.asList(addGiftExclude.split(","));
                            if (StringUtils.isNotBlank(businessTypeCode) && excludeBusinessTypes.contains(businessTypeCode)) {
                                throw new NDSException("奶卡提货添加赠品走补发订单");
                            }
                        }

                        //toc残次订单不允许添加赠品
                        if (OmsOrderUtil.isToCCcOrder(ocOrder)) {
                            throw new NDSException("残次不支持手动添加赠品");
                        }

                        billNo = ocOrder.getBillNo();
                        ValueHolderV14 v14 = bean.addGoodsMainStep(ocOrder, user, changeGoodsSKu, qty);
                        if (v14 != null) {
                            if (ResultCode.SUCCESS == v14.getCode()) {
                                jsonObject.put("code", ResultCode.SUCCESS);
                                success++;
                                //this.modifyOrderStatus(ocOrder.getId(), ocOrder.getIsModifiedOrder(), 1);
                                OcBOrder modifiedOrder = new OcBOrder();
                                modifiedOrder.setId(ocOrder.getId());
                                if (Objects.isNull(ocOrder.getIsModifiedOrder()) || ocOrder.getIsModifiedOrder() != 1) {
                                    modifiedOrder.setIsModifiedOrder(1);
                                }
                                modifiedOrder.setIsHasgift(1);
                                ocBOrderMapper.updateById(modifiedOrder);
                                OcBOrder newOrder = new OcBOrder();
                                newOrder.setId(ocOrder.getId());
                                updateOrderAllSku(newOrder);
                                ocBOrderItemExtService.deleteByOrderId(ocOrder.getId());
                            } else {
                                jsonObject.put("code", ResultCode.FAIL);
                                //调用订单得日志服务 失败依旧在这调用
                                omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(), OrderLogTypeEnum.GIFT_ADD.getKey(), changeGoodsSKu + " 新增商品失败！原因:" + v14.getMessage(), null, null, user);
                            }
                            jsonObject.put("message", "单据编号" + billNo + ":" + v14.getMessage());
                        }
                    } else {
                        jsonObject.put("code", ResultCode.FAIL);
                        jsonObject.put("message", "订单编号" + aLong + ":" + "当前订单正在操作,请稍后尝试");
                    }
                } catch (Exception e) {
                    jsonObject.put("code", -1);
                    log.error(LogUtil.format("新增赠品服务异常.异常: {}"), Throwables.getStackTraceAsString(e));
                    omsOrderLogService.addUserOrderLog(aLong, billNo, OrderLogTypeEnum.GIFT_ADD.getKey(), changeGoodsSKu + " 新增商品失败！原因:" + e.getMessage(), null, null, user);
                    jsonObject.put("message", billNo + ":" + e.getMessage());

                } finally {
                    redisLock.unlock();
                }
                jsonArray.add(jsonObject);
                result.setSuccess(success);
                result.setJsonArray(jsonArray);
            }
            return result;
        }
    }

    @Data
    class BatchReplaceGoodsTaskResult {
        List<ValueHolderV14<String>> v14List;
        private Integer success;
    }

    class BatchReplaceGoodsTask implements Callable<BatchReplaceGoodsTaskResult> {

        private final List<Long> orderIds;
        private final User user;
        private final String skuCode;
        private final Integer limitQty;
        private final Integer changeGoodsQty;
        private final String currentSku;
        private final Long itemId;
        private final List<ManyReplaceInfo> replaceInfos;

        private final boolean equalProportion;

        public BatchReplaceGoodsTask(List<Long> orderIds, User user, String skuCode, Integer limitQty, Integer changeGoodsQty,
                                     String currentSku, Long itemId, List<ManyReplaceInfo> replaceInfos, boolean equalProportion) {
            this.orderIds = orderIds;
            this.user = user;
            this.skuCode = skuCode;
            this.limitQty = limitQty;
            this.changeGoodsQty = changeGoodsQty;
            this.currentSku = currentSku;
            this.itemId = itemId;
            this.replaceInfos = replaceInfos;
            this.equalProportion = equalProportion;
        }

        @Override
        public BatchReplaceGoodsTaskResult call() throws Exception {
            String logSku = StringUtils.isEmpty(skuCode) ?
                    ListUtils.emptyIfNull(replaceInfos).stream().map(ManyReplaceInfo::getSkuCode).collect(Collectors.joining(","))
                    : skuCode;

            BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
            BatchReplaceGoodsTaskResult result = new BatchReplaceGoodsTaskResult();
            Integer success = 0;
            List<ValueHolderV14<String>> v14List = new ArrayList<>();
            for (Long orderId : orderIds) {
                ValueHolderV14<String> valueHolderV14 = new ValueHolderV14<>();

                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    OcBOrder ocOrder = ocBOrderMapper.selectById(orderId);
                    valueHolderV14.setData(ocOrder.getBillNo());

                    if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
                    }

                    if (OmsOrderUtil.wdtPlatformSend(ocOrder)) {
                        throw new NDSException("旺店通下发订单 不支持替换商品");
                    }

                    List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                    if (itemId != null) {
                        OcBOrderItem ocBOrderItem = ocBOrderItemMapper.queryOrderById(itemId, ocOrder.getId());
                        ocBOrderItems.add(ocBOrderItem);
                    } else {
                        ocBOrderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id",
                                ocOrder.getId()).eq("ps_c_sku_ecode", currentSku));
                    }
                    // 只用pro_type= 4 或 0,且非退款完成的明细才能替换商品
                    // 20240903 pro_type= 2 也才能替换商品
                    ocBOrderItems = ocBOrderItems.stream()
                            .filter(item -> item.getProType() != null
                                    && (SkuType.NO_SPLIT_COMBINE == item.getProType().intValue()
                                    || SkuType.COMBINE_PRODUCT == item.getProType().intValue()
                                    || SkuType.NORMAL_PRODUCT == item.getProType().intValue())
                                    && !InreturningStatus.INRETURN_YES.equals(item.getRefundStatus()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ocBOrderItems)) {
                        //调用订单得日志服务
                        omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                                OrderLogTypeEnum.GOODS_REPLACE.getKey(),
                                "替换" + logSku + " 商品失败！原因:该订单不存在可替换的SKU", null, null, user);
                        continue;
                    }

                    /*组合品想要去除对等换货信息，需要用组合品中所有子商品的明细，到下面去除对等换货信息的时候用*/
                    List<String> groupMarks = ocBOrderItems.stream()
                            .filter(item -> SkuType.NO_SPLIT_COMBINE == item.getProType() && StringUtils.isNotBlank(item.getGroupGoodsMark()))
                            .map(OcBOrderItem::getGroupGoodsMark).distinct().collect(Collectors.toList());
                    Set<Long> itemIdSet = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(groupMarks)) {
                        List<OcBOrderItem> groupItemList = ocBOrderItemMapper.selectOrderItemByGroupMarks(ocOrder.getId(), groupMarks);
                        ocBOrderItems.addAll(groupItemList.stream().filter(item -> !itemIdSet.contains(item.getId())).collect(Collectors.toList()));
                    }


                    if (CollectionUtils.isNotEmpty(replaceInfos)) {
                        //多商品替换，只能是单品
                        ocBOrderItems = ocBOrderItems.stream()
                                .filter(item -> item.getProType() != null
                                        && SkuType.NORMAL_PRODUCT == item.getProType().intValue()
                                        && !InreturningStatus.INRETURN_YES.equals(item.getRefundStatus()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(ocBOrderItems)) {
                            //调用订单得日志服务
                            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                                    OrderLogTypeEnum.GOODS_REPLACE.getKey(),
                                    "替换商品失败！多商品替换，只能是单品", null, null, user);
                            continue;
                        }
                    }
                    /*替换商品核心方法*/
                    ValueHolderV14 v14 = bean.changeGoodsMainStep(ocOrder, user, skuCode, currentSku, itemId, changeGoodsQty, limitQty, replaceInfos, equalProportion);
                    if (ResultCode.SUCCESS == v14.getCode()) {
                        success++;
                        Integer isModifiedOrder = Optional.ofNullable(ocOrder.getIsModifiedOrder()).orElse(0);
                        if (Objects.isNull(isModifiedOrder) || isModifiedOrder != 1) {
                            OcBOrder modifiedOrder = new OcBOrder();
                            modifiedOrder.setId(ocOrder.getId());
                            modifiedOrder.setIsModifiedOrder(1);
                            ocBOrderMapper.updateById(modifiedOrder);
                        }
                        try {
                            splitOutStockOrderService.handleOutStockOrderForOrderSplit(orderId, user);
                        } catch (Exception e) {
                            log.error(LogUtil.format("替换商品,更新缺货拆单数据失败: {}"), Throwables.getStackTraceAsString(e));
                        }
                        OcBOrder newOcOrder = new OcBOrder();
                        newOcOrder.setId(ocOrder.getId());
                        updateOrderAllSku(newOcOrder);
                        // 如果存在对等换货信息 则删除掉
                        // ocBOrderItems去除掉equal_exchange_mark为空的数据
                        List<OcBOrderItem> equalExchangeMarkList = ocBOrderItems.stream().filter(item -> StringUtils.isNotBlank(item.getEqualExchangeMark())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(equalExchangeMarkList)) {
                            ocBOrderEqualExchangeItemMapper.delByEqualExchangeMark(ocOrder.getId(), equalExchangeMarkList.stream().map(OcBOrderItem::getEqualExchangeMark).collect(Collectors.toList()));
                            // 去除订单明细的EQUAL_EXCHANGE_MARK标记
                            ocBOrderItemMapper.clearEqualExchangeMark(ocOrder.getId(), equalExchangeMarkList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
                            List<OcBOrderItem> allOrderItemList = ocBOrderItemMapper.selectOrderItemListAndReturn(ocOrder.getId());
                            if (CollectionUtils.isEmpty(allOrderItemList.stream().filter(item -> StringUtils.isNotBlank(item.getEqualExchangeMark())).collect(Collectors.toList()))) {
                                OcBOrder updateOcBOrder = new OcBOrder();
                                updateOcBOrder.setId(ocOrder.getId());
                                updateOcBOrder.setIsEqualExchange(0);
                                updateOcBOrder.setModifieddate(new Date());
                                ocBOrderMapper.updateById(updateOcBOrder);
                            }
                        }
                        ocBOrderItemExtService.deleteByOrderId(ocOrder.getId());
                    } else {
                        //调用订单得日志服务
                        omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                                OrderLogTypeEnum.GOODS_REPLACE.getKey(),
                                "替换" + logSku + " 商品失败！原因:" + v14.getMessage(), null, null, user);
                    }

                    valueHolderV14.setCode(v14.getCode());
                    valueHolderV14.setMessage(v14.getMessage());
                } catch (Exception e) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("替换商品失败:" + e.getMessage());
                    log.error(LogUtil.format("替换商品,替换商品异常: {}"), Throwables.getStackTraceAsString(e));
                    //调用订单得日志服务
                    omsOrderLogService.addUserOrderLog(orderId, null,
                            OrderLogTypeEnum.GOODS_REPLACE.getKey(), "替换" +
                                    "" + logSku + " 商品失败！原因：" + e.getMessage(), null, null, user);
                } finally {
                    v14List.add(valueHolderV14);
                    redisLock.unlock();
                }
            }
            result.setSuccess(success);
            result.setV14List(v14List);
            return result;
        }
    }

    /**
     * 替换商品主流程
     *
     * @param ocBOrder     订单信息
     * @param loginUser    操作人
     * @param skuCode      目标SKU
     * @param currentSku   原SKU
     * @param itemId       列表页的时候没有传这个参数
     * @param changeSkuQty 目标数量
     * @param limitQty     原SKU数量
     * @param replaceInfos 多商品替换信息
     * @return 替换结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 changeGoodsMainStep(OcBOrder ocBOrder, User loginUser, String skuCode, String currentSku,
                                              Long itemId, Integer changeSkuQty, Integer limitQty,
                                              List<ManyReplaceInfo> replaceInfos, boolean equalProportion) {
        log.info(LogUtil.format("开始执行替换商品主流程,ocBOrder:{},skuEcode:{},currentSku:{},itemId:{}," +
                        "changeSkuQty:{},limitQty:{},replaceInfos:{},equalProportion:{}", "BatchOperationGoodsService.changeGoodsMainStep"),
                JSON.toJSONString(ocBOrder), skuCode, currentSku, itemId, changeSkuQty, limitQty, replaceInfos, equalProportion);

        ValueHolderV14 vh = new ValueHolderV14();

        //多商品替换:只能单个普通单品换多个多个普通单品
        if (CollectionUtils.isNotEmpty(replaceInfos)) {
            vh = changeGoodsOne2Many(ocBOrder, loginUser, currentSku, itemId, limitQty, replaceInfos, equalProportion);
            log.warn(LogUtil.format("单个普通单品换多个多个普通单品-替换商品执行完成，订单ID:{}, 结果：{}",
                    "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
            return vh;
        }

        /*要被替换的目标商品信息*/
        ProductSku productSku = psRpcService.selectProductSku(skuCode);
        if (productSku == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("所选商品不存在!");
            log.warn(LogUtil.format("替换商品执行结束-要被替换的目标商品不存在，订单ID:{},结果：{}",
                    "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
            return vh;
        }

        if (CollectionUtils.isEmpty(replaceInfos) && StringUtils.isNotBlank(currentSku)) {
            ProductSku currentProductSku = psRpcService.selectProductSku(currentSku);
            if (currentProductSku == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("替换成商品不存在!");
                log.warn(LogUtil.format("替换商品执行结束-替换成商品不存在，订单ID:{},结果：{}",
                        "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
                return vh;
            }
            if (currentProductSku.getSkuType() == SkuType.NORMAL_PRODUCT && productSku.getSkuType() == SkuType.COMBINE_PRODUCT) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("单品不支持替换成组合品!");
                log.warn(LogUtil.format("替换商品执行结束-单品不支持替换成组合品，订单ID:{},结果：{}",
                        "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
                return vh;
            }
        }

        // step 001  1.当前订单状态为 待审核/缺货 2.当前订单存在该SKU。
//        if (!checkBusinessType(ocBOrder, skuCode)) {
//            return new ValueHolderV14(ResultCode.FAIL, "勾选的订单不符合要求，不可以修改商品");
//        }
        String errSkuCode = checkBusinessTypeByChangeGoods(ocBOrder, productSku);
        if (StringUtils.isNotEmpty(errSkuCode)) {
            vh = new ValueHolderV14(ResultCode.FAIL, "勾选的订单不符合要求，不可以修改商品:" + errSkuCode);
            log.warn(LogUtil.format("勾选的订单不符合要求，不可以修改商品:{}",
                    "BatchOperationGoodsService.changeGoodsMainStep"), JSONObject.toJSONString(errSkuCode));
            return vh;
        }

        ValueHolderV14<List<OcBOrderItem>> v14 = checkStuatsAndSku(ocBOrder, currentSku, 0, itemId, false);
        if (ResultCode.FAIL == v14.getCode()) {
            log.warn(LogUtil.format("替换商品执行结束-检查订单与SKU信息不通过，订单ID:{}, 结果：{}",
                    "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(v14));
            return v14;
        }

        //查询替换前明细用于释放库存
        List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());

        // 调用更换商品服务
//                1.若待替换商品为组合商品时，调用编辑发货单中【替换组合商品】按钮的服务。
//                2.若为替换商品为普通商品时，调用编辑发货单中【替换商品】按钮的服务。
        List<OcBOrderItem> data = v14.getData();
        String psCSkuEcode = data.get(0).getPsCSkuEcode();

        try {
            if (!equalProportion && limitQty != null) {
                data = data.stream().filter(p -> p.getQty().compareTo(new BigDecimal(limitQty)) == 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(data)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(ocBOrder.getBillNo() + "有所选商品被数量限制!");
                    log.warn(LogUtil.format("替换商品执行结束-商品数量错误，订单ID:{}, 结果：{}",
                            "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
                    return vh;
                }
            }
            if (equalProportion) {
                data = data.stream().filter(o -> {
                    boolean isProportion = new BigDecimal(changeSkuQty)
                            .multiply(o.getQty())
                            .divide(new BigDecimal(limitQty), 10, RoundingMode.HALF_UP)
                            .stripTrailingZeros().scale() == 0;
                    log.warn(LogUtil.format("替换商品执行结束，isProportion:{},算法:[{}*{}/{}],订单ID:{}",
                                    "BatchOperationGoodsService.changeGoodsMainStep"),
                            isProportion, changeSkuQty, o.getQty(), limitQty, ocBOrder.getId());
                    return isProportion;
                }).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(data)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(ocBOrder.getBillNo() + "没有数量符合等比替换的明细!");
                    log.warn(LogUtil.format("替换商品执行结束-没有数量符合等比替换的明细，订单ID:{}, 结果：{}",
                            "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
                    return vh;
                }
            }

            for (OcBOrderItem datum : data) {
                BigDecimal changeQty = datum.getQty();
                if (changeSkuQty != null && changeSkuQty > 0) {
                    changeQty = new BigDecimal(changeSkuQty);
                }

                /*如果是等比替换，计算数量*/
                if (equalProportion) {
                    /*新ITEM的数量 = 前端原SKU数量 * 当前ITEM中数量 / 前端目标SKU数量*/
                    changeQty = new BigDecimal(changeSkuQty)
                            .multiply(datum.getQty())
                            .divide(new BigDecimal(limitQty), 10, RoundingMode.HALF_UP)
                            .stripTrailingZeros();
                    /*如果有小数，说明比例错误，不允许替换*/
                    if (changeQty.scale() > 0) {
                        vh = new ValueHolderV14(ResultCode.FAIL, "替换商品数量非法");
                        log.warn(LogUtil.format("替换商品执行结束，算法:[{}= {}*{}/{}],订单ID:{}, 结果：{}",
                                        "BatchOperationGoodsService.changeGoodsMainStep"),
                                changeQty, changeSkuQty, datum.getQty(), limitQty, ocBOrder.getId(), JSON.toJSONString(vh));
                        return vh;
                    }
                }

                // 未拆的组合4替换为组合2
                if (datum.getProType() == SkuType.NO_SPLIT_COMBINE
                        && productSku.getSkuType() == SkuType.COMBINE_PRODUCT) {
                    if (ObjectUtils.notEqual(changeSkuQty, 1) || ObjectUtils.notEqual(limitQty, 1)) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(ocBOrder.getBillNo() + "未拆的组合替换为组合数量必须为1!");
                        log.warn(LogUtil.format("替换商品执行结束-未拆的组合4替换为组合2数量必须为1，订单ID:{}, 结果：{}",
                                "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
                        return vh;
                    }

                    omsReplaceProductService.replaceNoSplitGroup2NoSplitGroup(ocBOrder, datum, productSku, loginUser, changeQty);
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(ocBOrder.getBillNo() + "替换商品成功");
                    continue;
                }
                // 拆后的组合2替换为正常0
                if (datum.getProType() == SkuType.COMBINE_PRODUCT
                        && productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                    omsReplaceProductService.replaceSplitGroup2Normal(ocBOrder, datum, productSku, loginUser, changeQty);
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(ocBOrder.getBillNo() + "替换商品成功");
                    continue;
                }
                // 202409031906: 下面这几种替换方式都是原来的替换逻辑，没有变更

                // 正常0替换为正常0
                if (datum.getProType() == SkuType.NORMAL_PRODUCT
                        && productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                    omsReplaceProductService.replacePro(ocBOrder, datum, productSku, loginUser, changeQty);
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(ocBOrder.getBillNo() + "替换商品成功");
                }
                // 正常0替换为 福袋商品1/拆后的组合2
                else if (datum.getProType() == SkuType.NORMAL_PRODUCT
                        && (productSku.getSkuType() == SkuType.GIFT_PRODUCT || productSku.getSkuType() == SkuType.COMBINE_PRODUCT)) {
                    vh = omsReplaceProductService.replaceNormal2Group(ocBOrder, datum, productSku, loginUser, changeQty);
                }
                // 未拆的组合4或者福袋 替换为 正常0
                else if (datum.getProType() == SkuType.NO_SPLIT_COMBINE
                        && productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                    vh = omsReplaceProductService.replaceGroup2Normal(ocBOrder, datum, productSku, loginUser, changeQty);
                }
                // 未拆的组合4替换为 福袋商品1/拆后的组合2 「这里的2已经走不到了，因为在上面就结束了」
                else if (datum.getProType() == SkuType.NO_SPLIT_COMBINE
                        && (productSku.getSkuType() == SkuType.GIFT_PRODUCT || productSku.getSkuType() == SkuType.COMBINE_PRODUCT)) {
                    vh = omsReplaceProductService.replaceGroup2Group(ocBOrder, datum, productSku, loginUser, changeQty);
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("替换类型异常！");
                }
            }
            vh.setData(psCSkuEcode);

            //释放之前明细库存处理方法
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用释放占用库存buidRequestAndReleaseStock方法入参ocBOrder:{},itemList:{}",
                        ocBOrder.getId()), JSON.toJSONString(ocBOrder), JSON.toJSONString(items));
            }
            long startTime = System.currentTimeMillis();
            ValueHolderV14 vh14 = this.buidRequestAndReleaseStock(ocBOrder, items, loginUser);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("释放占用库存，返回:{}", ocBOrder.getId()), JSON.toJSONString(vh14));
            }
            AssertUtil.assertException(!vh14.isOK(), vh14.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("订单ID={},调用buidRequestAndReleaseStock释放占用库存耗时={}", ocBOrder.getId(), System.currentTimeMillis() - startTime);
            }
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            ocBOrderMapper.updateById(order);
            //加入占单表
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
            for (OcBOrderItem item : itemList) {
                item.setExpiryDateRange(null);
                item.setExpiryDateType(null);
            }
            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(ocBOrder);
            param.setOrderItemList(itemList);
            omsExpiryDateStService.expiryDateStService(param, loginUser, "(替换商品)");
            // 卡单不释放
            if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
                log.warn(LogUtil.format("替换商品执行完成-卡单不释放？，订单ID:{}, 结果：{}",
                        "BatchOperationGoodsService.changeGoodsMainStep"), ocBOrder.getId(), JSON.toJSONString(vh));
                return vh;
            }

            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
        } catch (Exception e) {
            log.error(LogUtil.format("替换商品异常,订单信息：{},异常信息：{}", "BatchOperationGoodsService.changeGoodsMainStep"),
                    ocBOrder.getBillNo(), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("替换商品异常" + e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        log.info(LogUtil.format("替换商品执行完成，订单ID:{}, 结果：{}", "BatchOperationGoodsService.changeGoodsMainStep"),
                ocBOrder.getId(), JSON.toJSONString(vh));
        return vh;
    }

    private ValueHolderV14 changeGoodsOne2Many(OcBOrder ocBOrder, User loginUser, String currentSku,
                                               Long itemId, Integer limitQty, List<ManyReplaceInfo> replaceInfos, boolean equalProportion) {
        ValueHolderV14 vh = new ValueHolderV14();

        List<String> skuCodes = replaceInfos.stream().map(ManyReplaceInfo::getSkuCode).collect(Collectors.toList());
        List<String> errorCodes = checkBusinessType(ocBOrder, currentSku, skuCodes);
        if (CollectionUtils.isNotEmpty(errorCodes)) {
            return new ValueHolderV14(ResultCode.FAIL, "勾选的订单不符合要求，不可以修改商品:" + errorCodes);
        }

        ValueHolderV14<List<OcBOrderItem>> v14 = checkStuatsAndSku(ocBOrder, currentSku, 0, itemId, true);
        if (ResultCode.FAIL == v14.getCode()) {
            return v14;
        }
        List<OcBOrderItem> data = v14.getData();
        String psCSkuEcode = data.get(0).getPsCSkuEcode();


        //查询替换前明细用于释放库存
        List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());

        Map<String, BigDecimal> skuQtyMap = replaceInfos.stream()
                .filter(o -> Objects.nonNull(o.getChangeGoodsQty()))
                .collect(Collectors.toMap(ManyReplaceInfo::getSkuCode, o -> new BigDecimal(o.getChangeGoodsQty()), (a, b) -> a));
        try {
            Map<String, ProductSku> skuProductMap = new HashMap<>();
            for (String replaceSku : skuQtyMap.keySet()) {
                ProductSku productSku = psRpcService.selectProductSku(replaceSku);
                if (productSku == null) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("所选商品不存在!" + replaceSku);
                    return vh;
                }
                if (productSku.getSkuType() != SkuType.NORMAL_PRODUCT) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("单个商品只能替换正常商品!" + replaceSku);
                    return vh;
                }

                skuProductMap.put(replaceSku, productSku);
            }

            if (!equalProportion && limitQty != null) {
                data = data.stream().filter(p -> p.getQty().compareTo(new BigDecimal(limitQty)) == 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(data)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(ocBOrder.getBillNo() + "有所选商品被数量限制!");
                    return vh;
                }
            }

            for (OcBOrderItem datum : data) {
                if (datum.getProType() != SkuType.NORMAL_PRODUCT) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(ocBOrder.getBillNo() + "只有单品才可以替换多个单品!");
                    return vh;
                }

                // 单个正常替换为多个正常
                if (datum.getProType() == SkuType.NORMAL_PRODUCT) {
                    /*核心替换逻辑*/
                    omsReplaceProductService.replaceNormal2ManyNormal(ocBOrder, datum, skuProductMap, loginUser, skuQtyMap, equalProportion);
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(ocBOrder.getBillNo() + "替换商品成功");
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(ocBOrder.getBillNo() + "只有单品才可以替换多个单品!");
                    return vh;
                }
            }
            vh.setData(psCSkuEcode);

            //释放之前明细库存处理方法
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用释放占用库存buidRequestAndReleaseStock方法入参ocBOrder:{},itemList:{}",
                        ocBOrder.getId()), JSON.toJSONString(ocBOrder), JSON.toJSONString(items));
            }
            long startTime = System.currentTimeMillis();
            ValueHolderV14 vh14 = this.buidRequestAndReleaseStock(ocBOrder, items, loginUser);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("释放占用库存，返回:{}", ocBOrder.getId()), JSON.toJSONString(vh14));
            }
            AssertUtil.assertException(!vh14.isOK(), vh14.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("订单ID={},调用buidRequestAndReleaseStock释放占用库存耗时={}", ocBOrder.getId(), System.currentTimeMillis() - startTime);
            }
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            ocBOrderMapper.updateById(order);
            //加入占单表
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
            for (OcBOrderItem item : itemList) {
                item.setExpiryDateRange(null);
                item.setExpiryDateType(null);
            }
            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(ocBOrder);
            param.setOrderItemList(itemList);
            omsExpiryDateStService.expiryDateStService(param, loginUser, "(替换商品)");
            // 卡单不释放
            if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
                return vh;
            }

            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);

        } catch (Exception e) {
            log.error(LogUtil.format("替换商品异常,订单信息：{},异常信息：{}", "BatchOperationGoodsService.changeGoodsMainStep"),
                    ocBOrder.getBillNo(), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("替换商品异常" + e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return vh;
    }


    /**
     * 替换商品
     *
     * @param ocBOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void replacePro(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user) {
        // 复制替换之前的商品,重新占单的时候使用,释放相关占用
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, ocBOrderItem);
        item.setPsCProId(productSku.getProdId());
        // ProECode
        item.setPsCProEcode(productSku.getProdCode());
        item.setPsCSkuId(productSku.getId());
        item.setSex(productSku.getSex());
        //2019-08-30吊牌价改为取商品表数据
        item.setPriceTag(productSku.getPricelist());
        item.setPriceList(productSku.getPricelist());
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCProMaterieltype(productSku.getMaterialType());
        item.setStandardWeight(productSku.getWeight());
        item.setSkuSpec(productSku.getSkuSpec());
        item.setPsCSkuPtEcode(productSku.getProdCode());
        item.setPsCSkuEcode(productSku.getSkuEcode());
        //商品名称
        item.setPsCProEname(productSku.getName());

        // 增加品类信息 20220923
        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }

        omsOrderItemService.updateOcBOrderItem(item, ocBOrder.getId());
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + ocBOrderItem.getPsCSkuEcode()
                        + "替换为" + productSku.getSkuEcode() + "成功:",
                null, null, user);
        batchOperationGoodsService.handleOccupy(ocBOrder, Lists.newArrayList(ocBOrderItem), user);
    }


    /**
     * 批量添加商品標記
     *
     * @param obj
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchAddLabel(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (obj.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请传入参数");
            return vh;
        }
        JSONArray ids = obj.getJSONArray("ids");
        List<Long> idsList = new ArrayList<>();
        JSONArray errorMessage = new JSONArray();
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().in("id", ids));
        for (int i = 0; i < ocBOrderList.size(); i++) {
            OcBOrder ocBOrder = ocBOrderList.get(i);
            // 如果 订单状态是取消或者作废“提示当前状态异常，不允许订单编辑”
            if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                    OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                JSONObject object = new JSONObject();
                object.put("billNo", ocBOrder.getBillNo());
                object.put("Message", "当前状态异常，不允许订单编辑");
                errorMessage.add(object);
                omsOrderLogService.addUserOrderLog(ids.getLong(i), null, OrderLogTypeEnum.ORDER_LABEL.getKey(),
                        "打标记失败：当前状态异常，不允许订单标记", null, null, user);
            } else {
                idsList.add(ocBOrder.getId());
                omsOrderLogService.addUserOrderLog(ids.getLong(i), null, OrderLogTypeEnum.ORDER_LABEL.getKey(),
                        "打标记成功", null, null, user);
            }
        }
        if (!CollectionUtils.isEmpty(idsList)) {
            String cpCLabelEname = obj.getString("CP_C_LABEL_ENAME");
            String cpCLabelContent = obj.getString("CP_C_LABEL_CONTENT");
            Long cpCLabelId = obj.getLong("CP_C_LABEL_ID");
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifiername(user.getName());
            ocBOrder.setModifierename(user.getEname());
            ocBOrder.setCpCLabelContent(cpCLabelContent);
            ocBOrder.setCpCLabelEname(cpCLabelEname);
            ocBOrder.setCpCLabelId(cpCLabelId);
            ocBOrderMapper.update(ocBOrder, new QueryWrapper<OcBOrder>().in("id", idsList));
        }

        if (ids.size() == idsList.size()) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("订单标记成功" + ids.size() + "条数据");
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单标记成功" + idsList.size() + "条数据，失败" + (ids.size() - idsList.size()) + "条数据");
            vh.setData(errorMessage);
        }
        return vh;

    }

    /**
     * 手工打标：会员加急发货
     *
     * @param obj  传入的参数，JSON对象：{"ids":[1,2,3]}
     * @param user 当前操作用户
     * @return 响应结果
     */
    public ValueHolderV14 orderDeliveryUrgent(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        JSONArray ids = obj.getJSONArray("ids");
        if (obj.isEmpty() || CollectionUtils.isEmpty(ids)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请传入参数");
            return vh;
        }

        List<Long> idsList = new ArrayList<>();
        JSONArray errorMessage = new JSONArray();
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().in("id", ids));
        for (int i = 0; i < ocBOrderList.size(); i++) {
            OcBOrder ocBOrder = ocBOrderList.get(i);
            // 如果 订单状态是取消或者作废或者是待审核或者缺货“提示当前状态异常，不允许订单编辑”
            if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                    OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                JSONObject object = new JSONObject();
                object.put("billNo", ocBOrder.getBillNo());
                object.put("Message", "当前状态异常，不允许订单编辑");
                errorMessage.add(object);
                omsOrderLogService.addUserOrderLog(ids.getLong(i), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_LABEL.getKey(), "打标记失败：当前状态异常，不允许订单标记", null, null, user);
            } else {
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                        OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                    idsList.add(ocBOrder.getId());
                    omsOrderLogService.addUserOrderLog(ids.getLong(i), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_LABEL.getKey(), "打标记成功", null, null, user);
                }

            }
        }
        if (!CollectionUtils.isEmpty(idsList)) {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifiername(user.getName());
            ocBOrder.setModifierename(user.getEname());
            ocBOrder.setIsDeliveryUrgent(1); // 1表示紧急发货
            int number = ocBOrderMapper.update(ocBOrder, new QueryWrapper<OcBOrder>().in("id", idsList));
            if (number == 0) {
                idsList = new ArrayList<>();
            }
        }

        if (ids.size() == idsList.size()) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("订单标记成功" + ids.size() + "条数据");
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单标记成功" + idsList.size() + "条数据，失败" + (ids.size() - idsList.size()) + "条数据，只允许待审核和缺货的订单");
            vh.setData(null);
        }
        return vh;
    }

    /**
     * 删除重复的( ItemId _ SkuId _Qty is Zero )明细
     *
     * @param afterItems
     * @return
     */
    private List<OcBOrderItem> removeSameItem(List<OcBOrderItem> afterItems) {
        Map<String, OcBOrderItem> map = Maps.newHashMap();
        StringBuilder sb = new StringBuilder();
        // 按照数量倒数排序,用去下方删除 itemId 相同,且数量为0的
        afterItems.sort((o1, o2) -> (o2.getQty().compareTo(o1.getQty())));
        Iterator<OcBOrderItem> iterator = afterItems.iterator();
        while (iterator.hasNext()) {
            OcBOrderItem item = iterator.next();
            sb.append(item.getId()).append("_").append(item.getPsCSkuId());
            String key = sb.toString();
            sb.setLength(0);
            if (map.containsKey(key) && BigDecimalUtil.isZero(item.getQty())) {
                iterator.remove();
                continue;
            }
            map.put(key, item);
        }
        return afterItems;
    }

    /**
     * @param item
     * @return
     */
    private String groupKey(OcBOrderItem item) {
        return item.getId() + "_" + item.getPsCSkuEcode();
    }

    /**
     * <AUTHOR>
     * @Date 22:54 2021/8/31
     * @Description 取消加急
     */
    public ValueHolderV14 orderUnDeliveryUrgent(JSONObject obj, User user) {

        ValueHolderV14 vh = new ValueHolderV14();

        JSONArray ids = obj.getJSONArray("ids");
        if (obj.isEmpty() || CollectionUtils.isEmpty(ids)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请传入参数");
            return vh;
        }
        List<Long> idsList = new ArrayList<>();
        JSONArray errorMessage = new JSONArray();
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().in("id", ids));
        for (int i = 0; i < ocBOrderList.size(); i++) {
            OcBOrder ocBOrder = ocBOrderList.get(i);
            // 如果 订单状态是取消或者作废或者是待审核或者缺货“提示当前状态异常，不允许订单编辑”
            if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                    OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                JSONObject object = new JSONObject();
                object.put("billNo", ocBOrder.getBillNo());
                object.put("Message", "当前状态异常，不允许取消加急");
                errorMessage.add(object);
                omsOrderLogService.addUserOrderLog(ids.getLong(i), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_LABEL.getKey(), "取消加急失败：当前状态异常，不允许取消加急", null, null, user);
            } else {
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                        OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus()) || "1".equals(ocBOrder.getIsDeliveryUrgent())) {
                    idsList.add(ocBOrder.getId());
                    omsOrderLogService.addUserOrderLog(ids.getLong(i), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_LABEL.getKey(), "取消加急成功", null, null, user);
                }

            }
        }
        if (!CollectionUtils.isEmpty(idsList)) {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifiername(user.getName());
            ocBOrder.setModifierename(user.getEname());
            ocBOrder.setIsDeliveryUrgent(0); // 1表示紧急发货
            int number = ocBOrderMapper.update(ocBOrder, new QueryWrapper<OcBOrder>().in("id", idsList));
            if (number == 0) {
                idsList = new ArrayList<>();
            }
        }

        if (ids.size() == idsList.size()) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("订单取消加急成功" + ids.size() + "条数据");
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单取消加急成功" + idsList.size() + "条数据，失败" + (ids.size() - idsList.size()) + "条数据，只允许待审核和缺货的订单");
            vh.setData(null);
        }
        return vh;
    }
}



