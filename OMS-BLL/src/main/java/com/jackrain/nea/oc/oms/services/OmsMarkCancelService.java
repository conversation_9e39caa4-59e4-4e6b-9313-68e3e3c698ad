package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsReleaseOutRequest;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.enums.OrderStatusEnum;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/9/10 10:11 上午
 * @Version 1.0
 * <p>
 * 标记取消服务重构 (原标记退款服务)
 */
@Slf4j
@Component
public class OmsMarkCancelService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private BllRedisLockOrderUtil bllRedisLockOrderUtil;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OmsMarkCancelService omsMarkCancelService;
    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;

    @Deprecated
    public ValueHolderV14 markNaiKaCancel(OcBOrder order, String tid, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        try {
            Integer status = order.getOrderStatus();
            //要取消明细
            List<OcBOrderItem> orderItemsList = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku2(order.getId(), tid);

            // 全部明细
            List<OcBOrderItem> allOrderItemsList = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku(order.getId());

            if (CollectionUtils.isEmpty(orderItemsList)) {
                holder.setCode(0);
                holder.setMessage("标记退款完成成功");
                return holder;
            }
            // 反审核
            if (OmsOrderStatus.CHECKED.toInteger().equals(status) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)) {
                // 此处应该不会再反审核了 因为前面刚进行了反审核
                omsReturnUtil.toExamineOrderLock(order, user);
            }
            //校验订单状态，待分配、occupy_status占单中不允许标记退款完成
            AssertUtil.assertException(OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus()),
                    String.format("订单【%s】占单中，不允许标记退款完成", order.getBillNo()));

            List<Long> ocBOrderItemIds = orderItemsList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            this.checkOrder(order, allOrderItemsList, ocBOrderItemIds);
            Map<Long, OcBOrderItem> orderItemMap = allOrderItemsList.stream().collect(Collectors.toMap(OcBOrderItem::getId, p -> p));
            List<OcBOrderItem> items = new ArrayList<>();
            for (Long itemId : ocBOrderItemIds) {
                OcBOrderItem ocBOrderItem = orderItemMap.get(itemId);
                if (ocBOrderItem != null && (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE
                        || ocBOrderItem.getProType() == SkuType.NORMAL_PRODUCT)) {
                    items.add(ocBOrderItem);
                    allOrderItemsList.remove(ocBOrderItem);
                }
            }
            if (CollectionUtils.isEmpty(items)) {
                throw new NDSException("只有正常商品和组合福袋商品并且未退款完成的明细才可以标记退款完成");
            }
            omsMarkCancelService.handleCancelItem(order, items, Boolean.FALSE, allOrderItemsList, user, OrderHoldReasonEnum.REFUND_HOLD);
            holder.setCode(0);
            holder.setMessage("标记退款完成成功");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 标记退款完成异常,orderId:" + order.getId() + ",异常信息:" + e.getMessage(), e);
            throw new NDSException(e.getMessage());
        }
        return holder;
    }

    public ValueHolderV14 markNaiKaPartCancel(OcBOrder order, String tid, User user, List<OcBOrderItem> orderItemsList) {
        ValueHolderV14 holder = new ValueHolderV14();
        try {
            Integer status = order.getOrderStatus();

            // 全部明细
            List<OcBOrderItem> allOrderItemsList = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku(order.getId());

            if (CollectionUtils.isEmpty(orderItemsList)) {
                holder.setCode(0);
                holder.setMessage("标记退款完成成功");
                return holder;
            }
            // 反审核
            if (OmsOrderStatus.CHECKED.toInteger().equals(status) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)) {
                // 此处应该不会再反审核了 因为前面刚进行了反审核
                omsReturnUtil.toExamineOrderLock(order, user);
            }
            //校验订单状态，待分配、occupy_status占单中不允许标记退款完成
            AssertUtil.assertException(OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus()),
                    String.format("订单【%s】占单中，不允许标记退款完成", order.getBillNo()));

            List<Long> ocBOrderItemIds = orderItemsList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            this.checkOrder(order, allOrderItemsList, ocBOrderItemIds);
            Map<Long, OcBOrderItem> orderItemMap = allOrderItemsList.stream().collect(Collectors.toMap(OcBOrderItem::getId, p -> p));
            List<OcBOrderItem> items = new ArrayList<>();
            for (Long itemId : ocBOrderItemIds) {
                OcBOrderItem ocBOrderItem = orderItemMap.get(itemId);
                if (ocBOrderItem != null && (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE
                        || ocBOrderItem.getProType() == SkuType.NORMAL_PRODUCT)) {
                    items.add(ocBOrderItem);
                    allOrderItemsList.remove(ocBOrderItem);
                }
            }
            if (CollectionUtils.isEmpty(items)) {
                throw new NDSException("只有正常商品和组合福袋商品并且未退款完成的明细才可以标记退款完成");
            }
            omsMarkCancelService.handleCancelItem(order, items, Boolean.FALSE, allOrderItemsList, user, OrderHoldReasonEnum.REFUND_HOLD);
            holder.setCode(0);
            holder.setMessage("标记退款完成成功");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 标记退款完成异常,orderId:" + order.getId() + ",异常信息:" + e.getMessage(), e);
            throw new NDSException(e.getMessage());
        }
        return holder;
    }

    /**
     * @param orderId 订取消的明细id单主表id
     * @param itemIds 需要标记取消的明细id (只接收pro_type = 0或者 4的数据)
     * @return
     */
    public ValueHolderV14 markCancel(Long orderId, List<Long> itemIds, User user, OrderHoldReasonEnum reasonEnum,
                                     boolean posRefund) {
        ValueHolderV14 holder = new ValueHolderV14();
        if (orderId == null || CollectionUtils.isEmpty(itemIds)) {
            holder.setCode(-1);
            holder.setMessage("传入数据异常!");
            return holder;
        }
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 进入标记退款完成服务 订单id :{}", orderId);
        }
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(bllRedisLockOrderUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder order = ocBOrderMapper.selectById(orderId);

                //校验订单状态，待分配、occupy_status占单中不允许标记退款完成
                AssertUtil.assertException(OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus()),
                        String.format("订单【%s】占单中，不允许标记退款完成",order.getBillNo()));

                //全部明细
                List<OcBOrderItem> orderItemsList = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku(orderId);
                this.checkOrder(order, orderItemsList,itemIds);
                Map<Long, OcBOrderItem> orderItemMap = orderItemsList.stream().collect(Collectors.toMap(OcBOrderItem::getId, p -> p));
                List<OcBOrderItem> items = new ArrayList<>();
                for (Long itemId : itemIds) {
                    OcBOrderItem ocBOrderItem = orderItemMap.get(itemId);
                    if (ocBOrderItem != null && (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE
                            || ocBOrderItem.getProType() == SkuType.NORMAL_PRODUCT)) {
                        items.add(ocBOrderItem);
                        orderItemsList.remove(ocBOrderItem);
                    }
                }
                if (CollectionUtils.isEmpty(items)) {
                    throw new NDSException("只有正常商品和组合福袋商品并且未退款完成的明细才可以标记退款完成");
                }
                omsMarkCancelService.handleCancelItem(order, items, posRefund, orderItemsList, user, reasonEnum);
                holder.setCode(0);
                holder.setMessage("标记退款完成成功");
            } else {
                throw new NDSException("当前订单正在被操作, 标记退款完成失败");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 标记退款完成异常,orderId:"+orderId+",异常信息:"+e.getMessage(), e);
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return holder;
    }


    /**
     * 检验订单的合法性
     *
     * @param ocBOrder
     */
    private void checkOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList,List<Long> itemIds) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus))) {
            throw new NDSException("当前订单状态不允许标记退款完成");
        }
        if (CollectionUtils.isEmpty(itemList)) {
            throw new NDSException("传入的明细数据有误或者已取消");
        }
        if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            List<OcBOrderItem> markItemList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), itemIds);
            Set<String> tidSet = markItemList.stream().map(OcBOrderItem::getTid).collect(Collectors.toSet());
            itemList.remove(markItemList);
            for(OcBOrderItem item:itemList ){
                if(tidSet.contains(item.getTid())){
                    throw new NDSException("JITX订单仅支持整单取消，不支持单明细取消！");
                }
            }
        }


    }

    /**
     * 处理取消的明细
     *
     * @param order          订单主表信息
     * @param itemList       传过来的数据信息
     * @param orderItemsList 当前订单下的剩余的明细信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleCancelItem(OcBOrder order, List<OcBOrderItem> itemList,Boolean posRefund,
                                 List<OcBOrderItem> orderItemsList, User user, OrderHoldReasonEnum reasonEnum) {
        boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());
        //是否有关联的赠品信息
        List<OcBOrderItem> giftRelations = itemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(giftRelations)) {
            List<String> giftRelation = giftRelations.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
            //找到关联的赠品信息
            for (String gift : giftRelation) {
                for (int i = 0; i < orderItemsList.size(); i++) {
                    OcBOrderItem ocBOrderItem = orderItemsList.get(i);
                    if (ocBOrderItem.getGiftRelation() != null && gift.equals(ocBOrderItem.getGiftRelation())) {
                        itemList.add(ocBOrderItem);
                        orderItemsList.remove(ocBOrderItem);
                        i--;
                    }
                }
            }
        }
        //如果有组合商品  找出组合商品的下挂商品
        List<OcBOrderItem> items = itemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(items)) {
            //组合商品的id
            List<String> goodsMarks = items.stream().map(OcBOrderItem::getGroupGoodsMark).collect(Collectors.toList());
            for (String goodsMark : goodsMarks) {
                for (int i = 0; i < orderItemsList.size(); i++) {
                    OcBOrderItem ocBOrderItem = orderItemsList.get(i);
                    if (StringUtils.isNotEmpty(ocBOrderItem.getGroupGoodsMark()) && goodsMark.equals(ocBOrderItem.getGroupGoodsMark())) {
                        itemList.add(ocBOrderItem);
                        orderItemsList.remove(ocBOrderItem);
                        i--;
                    }
                }
            }
        }
        //判断要取消的明细 是否有关联的赠品信息
        List<OcBOrderItem> orderItems = orderItemsList.stream()
                .filter(p -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(p.getIsGift()))
                .collect(Collectors.toList());
        boolean flag = false;
        if ((CollectionUtils.isNotEmpty(orderItems) && orderItems.size() == orderItemsList.size())
                || CollectionUtils.isEmpty(orderItemsList)) {
            itemList.addAll(orderItems);
            //全部取消 将订单直接取消
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(order.getId());
            ocBOrder.setAdjustAmt(BigDecimal.ZERO);
            // 商品总额”：明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
            ocBOrder.setProductAmt(BigDecimal.ZERO);
            // 商品数量”：明细“商品数量”-“已退数量”合计
            ocBOrder.setQtyAll(BigDecimal.ZERO);
            // 优惠金额”：明细“优惠金额”合计
            ocBOrder.setProductDiscountAmt(BigDecimal.ZERO);

            //订单优惠金额”：明细“整单平摊金额”合计
            ocBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
            // 配送费用：更新为0
            ocBOrder.setShipAmt(BigDecimal.ZERO);
            // 服务费
            ocBOrder.setServiceAmt(BigDecimal.ZERO);
            // 已付金额 :订单总额：商品总额+配送费用+调整金额+服务费-订单优惠金额-商品优惠金额
            ocBOrder.setOrderAmt(BigDecimal.ZERO);
            // 已收金额
            ocBOrder.setReceivedAmt(BigDecimal.ZERO);
            ocBOrder.setSuffixInfo("REFUND-VOID");
            ocBOrder.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
            ocBOrderMapper.updateById(ocBOrder);
            flag = true;
        }else{
            // 残次不允许部分取消
            AssertUtil.assertException(isCc, "残次销售不允许部分取消！");
        }
        List<Long> itemIds = itemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
        //将当前明细的金额置0  取消状态变成已取消 死锁报错标记-2
        ocBOrderItemMapper.updateOrderItemAmt(order.getId(), itemIds);
        SgOmsReleaseOutRequest request = builReleaseParam(order,itemList,user);

        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(order.getId());
        //后面记录日志用到
        ocBOrder.setBillNo(order.getBillNo());

        //toc残次判断使用
        ocBOrder.setSaleProductAttr(order.getSaleProductAttr());
        ocBOrder.setBusinessTypeCode(order.getBusinessTypeCode());

        BaseModelUtil.makeBaseModifyField(ocBOrder,user);
        if (flag) {
            //云POS退款、默认、缺货、待分配不释放库存
            if (Boolean.TRUE.equals(posRefund) ||
                    OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(order.getOrderStatus()) ||
                    OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus()) ||
                    OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())) {
                return;
            }
            ValueHolderV14 voidSgOutOrderResult = sgRpcService.invoildOutOrder(isCc, ocBOrder, user);
            if (log.isDebugEnabled()) {
                log.debug("orderID的订单ID为{}开始调用取消发货逻辑单服务出参Result={}", ocBOrder.getId(),
                        voidSgOutOrderResult.toJSONObject());
            }
            AssertUtil.assertException(!voidSgOutOrderResult.isOK(), voidSgOutOrderResult.getMessage());
            //整单作废
            ocBOrder.setIsInterecept(0);
            ocBOrderHoldService.holdOrUnHoldOrder(order, reasonEnum);
            omsOrderService.updateOrderSystemVoid(ocBOrder, user);
        } else {
            List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefundAndNoSplit(order.getId());
            //判断是否有赠品  如果没有  则将赠的标识去掉
            Integer isHasgift = order.getIsHasgift();
            if (isHasgift != null && isHasgift == 1) {
                List<OcBOrderItem> giftList = orderItemList.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(giftList)) {
                    ocBOrder.setIsHasgift(0);
                }
            }
            List<OcBOrderItem> bOrderItems = orderItemList.stream().filter(p -> p.getProType() == SkuType.NORMAL_PRODUCT
                    || p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            omsOrderRecountAmountService.doRecountAmount(ocBOrder, bOrderItems);
            BigDecimal qtyAll = orderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            ocBOrder.setQtyAll(qtyAll);
            omsOrderService.updateOrderInfo(ocBOrder);
        }

        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("释放库存前单据信息：{}"), JSON.toJSONString(order));
        }
        //云POS退款、默认、缺货、待分配不释放库存
        if(Boolean.TRUE.equals(posRefund) ||
                OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())){
            return;
        }

        if (!flag) {
            //释放商品库存
            if (OmsOrderUtil.isToCCcOrder(order)) {
                //toc残次释放库存
                ValueHolderV14 valueHolderV14 = sgRpcService.voidFreezeOutOrder(order, user);
                AssertUtil.assertException(!valueHolderV14.isOK(), valueHolderV14.getMessage());

                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus()) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())) {
                    //修改订单状态为待寻源
                    ocBOrderMapper.updateWarehouse(order.getId());
                    //加入中间表
                    if (order.getIsDetention() != null && order.getIsDetention() == 1) {
                        Date detentionReleaseDate = order.getDetentionReleaseDate();
                        if (detentionReleaseDate != null) {
                            omsOccupyTaskService.addOcBOccupyTask(order, detentionReleaseDate);
                        }
                    } else {
                        omsOccupyTaskService.addOcBOccupyTask(order, null);
                    }
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(),
                            "残次订单退款重新寻源成功", null, null, user);
                } else {
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(),
                            "订单非待审核/缺货状态，不允许重新寻源", null, null, user);
                }
            } else {
                ValueHolderV14<String> releaseResult = sgRpcService.releaseOutStock(request);
                AssertUtil.assertException(!releaseResult.isOK(), releaseResult.getMessage());
            }
        }


    }

    public SgOmsReleaseOutRequest builReleaseParam(OcBOrder order,List<OcBOrderItem> itemList,User user){
        SgOmsReleaseOutRequest request = new SgOmsReleaseOutRequest();
        List<SgOmsPhyStorageOutItemRequest> itemRequests = Lists.newArrayList();
        request.setRetailBillNo(order.getBillNo());
        request.setLoginUser(user);
        request.setIsReleaseOrClean(true);
        //逻辑占用单业务节点--标记退款完成
        request.setServiceNode(106L);
        for (OcBOrderItem ocBOrderItem : itemList) {
            if (SkuType.NO_SPLIT_COMBINE != ocBOrderItem.getProType()) {
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ORDER_CANCLE.getKey(), "条码:" + ocBOrderItem.getPsCSkuEcode() + "标记退款成功", null, null, user);
                SgOmsPhyStorageOutItemRequest itemRequest = new SgOmsPhyStorageOutItemRequest();
                itemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                itemRequest.setQty(ocBOrderItem.getQty());
                itemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                itemRequest.setRetailItemId(ocBOrderItem.getId());
                itemRequest.setSkuId(ocBOrderItem.getPsCSkuPtEcode());
                itemRequests.add(itemRequest);
            }
        }
        request.setItemRequestList(itemRequests);
        return request;
    }

    public ValueHolderV14 cancelNaiKaPartOrder(String tid, List<String> oids) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage("success");
        User user = SystemUserResource.getMiniProgram();
        // 根据平台id查询零售发货单明细表 获取零售发货单id+明细表id
        List<Long> ocBOrderIds = ocBOrderItemMapper.selectOcBOrderIdByTid(tid);
        if (CollectionUtils.isEmpty(ocBOrderIds)) {
            holder.setCode(-1);
            holder.setMessage("传入数据异常!");
            return holder;
        }
        // 根据tid 以及oid 查询数据
        if (CollectionUtils.isEmpty(oids)) {
            holder.setCode(-1);
            holder.setMessage("传入数据异常!");
            return holder;
        }
        List<OcBOrderItem> cancelOrderItemList = new ArrayList<>();
        for (String oid : oids) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectByOoid(tid, oid);
            if (CollectionUtils.isEmpty(ocBOrderItemList)) {
                holder.setCode(-1);
                holder.setMessage("传入的子单号不存在!" + oid);
                return holder;
            }
            cancelOrderItemList.addAll(ocBOrderItemList);
        }
        // 执行订单部分取消
        // 1、先筛选出来有哪些零售发货单 然后判断要不要进行反审核
        List<Long> ocBOrderIdList = cancelOrderItemList.stream().map(OcBOrderItem::getOcBOrderId).distinct().collect(Collectors.toList());
        for (Long ocBOrderId : ocBOrderIdList) {
            OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderId);
            // 过滤掉作废的订单
            if (ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.CANCELLED.getVal())
                    || ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.SYS_VOID.getVal())) {
                continue;
            }
            Integer status = ocBOrder.getOrderStatus();
            //要取消明细
            List<OcBOrderItem> orderItemsList = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku2(ocBOrderId, tid);
            if (CollectionUtils.isEmpty(orderItemsList)) {
                continue;
            }
            if (OmsOrderStatus.CHECKED.toInteger().equals(status) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)) {
                try {
                    // 反审核
                    if (omsReturnUtil.toExamineOrderLock(ocBOrder, user)) {
                        OcBOrder updateOcBOrder = new OcBOrder();
                        updateOcBOrder.setId(ocBOrderId);
                        updateOcBOrder.setOrderStatus(ocBOrder.getOrderStatus());
                        ocBOrderMapper.updateById(updateOcBOrder);
                    } else {
                        holder.setCode(-1);
                        holder.setMessage("订单取消失败!");
                        return holder;
                    }
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 订单取消异常,orderId:" + ocBOrderId + ",异常信息:" + e.getMessage(), e);
                    throw new NDSException(e.getMessage());
                }
            }
        }

        // 2、执行完成反审核之后 再执行部分明细的取消操作
        for (Long ocBOrderId : ocBOrderIdList) {
            OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderId);
            // 过滤掉作废的订单
            if (ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.CANCELLED.getVal())
                    || ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.SYS_VOID.getVal())) {
                continue;
            }
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("error");
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(bllRedisLockOrderUtil.getLockOrderTimeOut2(), TimeUnit.MILLISECONDS)) {
                    // 根据零售发货单id与子单号 获取订单明细list
                    List<OcBOrderItem> cancelItemList = ocBOrderItemMapper.selectItemList(ocBOrderId, oids);
                    holder = omsMarkCancelService.markNaiKaPartCancel(ocBOrder, tid, user, cancelItemList);
                }
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 标记退款完成异常,orderId:" + ocBOrderId + ",异常信息:" + e.getMessage(), e);
                throw new NDSException(e.getMessage());
            } finally {
                redisLock.unlock();
            }
            if (holder.isOK()) {
                OcBOrder order = ocBOrderMapper.selectById(ocBOrderId);
                omsReturnUtil.handleRefundComplete(order);
            }
        }
        return holder;
    }

    /**
     * 根据平台单号取消奶卡订单(目前奶卡系统过来的订单都是单明细(单sku)的)
     *
     * @param tid
     * @return
     */
    @Deprecated
    public ValueHolderV14 cancelNaiKaOrder(String tid) {
        User user = SystemUserResource.getMiniProgram();
        // 根据平台id查询零售发货单明细表 获取零售发货单id+明细表id
        List<Long> ocBOrderIds = ocBOrderItemMapper.selectOcBOrderIdByTid(tid);
        if (CollectionUtils.isEmpty(ocBOrderIds)) {
            ValueHolderV14 holder = new ValueHolderV14();
            holder.setCode(-1);
            holder.setMessage("传入数据异常!");
            return holder;
        }

        // 操作反审核
        for (Long ocBOrderId : ocBOrderIds) {
            OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderId);
            // 过滤掉作废的订单
            if (ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.CANCELLED.getVal())
                    || ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.SYS_VOID.getVal())) {
                continue;
            }
            Integer status = ocBOrder.getOrderStatus();
            //要取消明细
            List<OcBOrderItem> orderItemsList = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku2(ocBOrderId, tid);
            if (CollectionUtils.isEmpty(orderItemsList)) {
                continue;
            }
            if (OmsOrderStatus.CHECKED.toInteger().equals(status) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)) {
                try {
                    // 反审核
                    if (omsReturnUtil.toExamineOrderLock(ocBOrder, user)) {
                        OcBOrder updateOcBOrder = new OcBOrder();
                        updateOcBOrder.setId(ocBOrderId);
                        updateOcBOrder.setOrderStatus(ocBOrder.getOrderStatus());
                        ocBOrderMapper.updateById(updateOcBOrder);
                    } else {
                        ValueHolderV14 holder = new ValueHolderV14();
                        holder.setCode(-1);
                        holder.setMessage("订单取消失败!");
                        return holder;
                    }
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 订单取消异常,orderId:" + ocBOrderId + ",异常信息:" + e.getMessage(), e);
                    throw new NDSException(e.getMessage());
                }
            }
        }

        for (Long ocBOrderId : ocBOrderIds) {
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("error");
            OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderId);
            // 过滤掉作废的订单
            if (ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.CANCELLED.getVal())
                    || ObjectUtil.equals(ocBOrder.getOrderStatus(), OrderStatusEnum.SYS_VOID.getVal())) {
                continue;
            }
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(bllRedisLockOrderUtil.getLockOrderTimeOut2(), TimeUnit.MILLISECONDS)) {
                    holderV14 = omsMarkCancelService.markNaiKaCancel(ocBOrder, tid, user);
                }
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 标记退款完成异常,orderId:"+ocBOrderId+",异常信息:"+e.getMessage(), e);
                throw new NDSException(e.getMessage());
            } finally {
                redisLock.unlock();
            }
            if (holderV14.isOK()) {
                OcBOrder order = ocBOrderMapper.selectById(ocBOrderId);
                omsReturnUtil.handleRefundComplete(order);
            }
        }
        ValueHolderV14 v14 = new ValueHolderV14();
        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage("success");
        return v14;
    }


    /**
     * 标记退款完成按钮
     *
     * @param obj
     * @param user
     * @return
     */
    public ValueHolderV14 markRefund(JSONObject obj, User user) {
        Long id = obj.getLong("id");
        JSONArray itemIdArr = obj.getJSONArray("itemIds");
        if (id == null || CollectionUtils.isEmpty(itemIdArr)) {
            ValueHolderV14 holder = new ValueHolderV14();
            holder.setCode(-1);
            holder.setMessage("传入数据异常!");
            return holder;
        }
        List<Long> itemIds = JSONArray.parseArray(itemIdArr.toString(), Long.class);
        ValueHolderV14 holderV14 = omsMarkCancelService.markCancel(id, itemIds, user,
                OrderHoldReasonEnum.REFUND_HOLD, Boolean.FALSE);
        if (holderV14.isOK()) {
            OcBOrder order = ocBOrderMapper.selectById(id);
            omsReturnUtil.handleRefundComplete(order);
        }
        return holderV14;
    }


    /**
     * 处理取消的明细
     *
     * @param order          订单主表信息
     * @param itemList       传过来的数据信息
     * @param orderItemsList 当前订单下的剩余的明细信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleSapCancelItem(OcBOrder order, List<OcBOrderItem> itemList,List<OcBOrderItem> orderItemsList) {

        User sapUser = SystemUserResource.getSapUser();

        boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());

        //是否有关联的赠品信息
        List<OcBOrderItem> giftRelations = itemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(giftRelations)) {
            List<String> giftRelation = giftRelations.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
            //找到关联的赠品信息
            for (String gift : giftRelation) {
                for (int i = 0; i < orderItemsList.size(); i++) {
                    OcBOrderItem ocBOrderItem = orderItemsList.get(i);
                    if (ocBOrderItem.getGiftRelation() != null && gift.equals(ocBOrderItem.getGiftRelation())) {
                        itemList.add(ocBOrderItem);
                        orderItemsList.remove(ocBOrderItem);
                        i--;
                    }
                }
            }
        }
        //如果有组合商品  找出组合商品的下挂商品
        List<OcBOrderItem> items = itemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(items)) {
            //组合商品的id
            List<String> goodsMarks = items.stream().map(OcBOrderItem::getGroupGoodsMark).collect(Collectors.toList());
            for (String goodsMark : goodsMarks) {
                for (int i = 0; i < orderItemsList.size(); i++) {
                    OcBOrderItem ocBOrderItem = orderItemsList.get(i);
                    if (StringUtils.isNotEmpty(ocBOrderItem.getGroupGoodsMark()) && goodsMark.equals(ocBOrderItem.getGroupGoodsMark())) {
                        itemList.add(ocBOrderItem);
                        orderItemsList.remove(ocBOrderItem);
                        i--;
                    }
                }
            }
        }
        //判断要取消的明细 是否有关联的赠品信息
        List<OcBOrderItem> orderItems = orderItemsList.stream()
                .filter(p -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(p.getIsGift()))
                .collect(Collectors.toList());
        boolean flag = false;

        //2B的单子取消都由上游发起；主品取消赠品是否要发，也由上游系统决定
        if ((CollectionUtils.isNotEmpty(orderItems) && orderItems.size() == orderItemsList.size() && !OmsBusinessTypeUtil.isToBOrder(order))
                || CollectionUtils.isEmpty(orderItemsList)) {
            itemList.addAll(orderItems);
            //全部取消 将订单直接取消
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(order.getId());
            ocBOrder.setAdjustAmt(BigDecimal.ZERO);
            // 商品总额”：明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
            ocBOrder.setProductAmt(BigDecimal.ZERO);
            // 商品数量”：明细“商品数量”-“已退数量”合计
            ocBOrder.setQtyAll(BigDecimal.ZERO);
            // 优惠金额”：明细“优惠金额”合计
            ocBOrder.setProductDiscountAmt(BigDecimal.ZERO);

            //订单优惠金额”：明细“整单平摊金额”合计
            ocBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
            // 配送费用：更新为0
            ocBOrder.setShipAmt(BigDecimal.ZERO);
            // 服务费
            ocBOrder.setServiceAmt(BigDecimal.ZERO);
            // 已付金额 :订单总额：商品总额+配送费用+调整金额+服务费-订单优惠金额-商品优惠金额
            ocBOrder.setOrderAmt(BigDecimal.ZERO);
            // 已收金额
            ocBOrder.setReceivedAmt(BigDecimal.ZERO);
            ocBOrder.setSuffixInfo("REFUND-VOID");
            ocBOrder.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
            ocBOrderMapper.updateById(ocBOrder);
            flag = true;
        }else{
            // 残次不允许部分取消
            AssertUtil.assertException(isCc, "残次销售不允许部分取消！");
        }
        List<Long> itemIds = itemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
        //将当前明细的金额置0  取消状态变成已取消
        ocBOrderItemMapper.updateOrderItemAmt(order.getId(), itemIds);

        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(order.getId());
        //后面记录日志用到
        ocBOrder.setBillNo(order.getBillNo());
        Integer orderStatus = order.getOrderStatus();
        if (flag) {
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
                ValueHolderV14 voidSgOutOrderResult = sgRpcService.invoildOutOrder(isCc, ocBOrder, sapUser);
                if (log.isDebugEnabled()) {
                    log.debug("orderID的订单ID为{}开始调用取消发货逻辑单服务出参Result={}", ocBOrder.getId(),
                            voidSgOutOrderResult.toJSONObject());
                }
                AssertUtil.assertException(!voidSgOutOrderResult.isOK(), voidSgOutOrderResult.getMessage());
            }
            //整单作废
            ocBOrder.setIsInterecept(0);
            omsOrderService.updateOrderSystemCancelled(ocBOrder, sapUser);
        } else {
            List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefundAndNoSplit(order.getId());
            //判断是否有赠品  如果没有  则将赠的标识去掉
            Integer isHasgift = order.getIsHasgift();
            if (isHasgift != null && isHasgift == 1) {
                List<OcBOrderItem> giftList = orderItemList.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(giftList)) {
                    ocBOrder.setIsHasgift(0);
                }
            }
            List<OcBOrderItem> bOrderItems = orderItemList.stream().filter(p -> p.getProType() == SkuType.NORMAL_PRODUCT
                    || p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            omsOrderRecountAmountService.doRecountAmount(ocBOrder, bOrderItems);
            BigDecimal qtyAll = orderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            ocBOrder.setQtyAll(qtyAll);
            omsOrderService.updateOrderInfo(ocBOrder);
        }

        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("释放库存前单据信息：{}"), JSON.toJSONString(order));
        }
        //云POS退款、默认、缺货、待分配不释放库存
        if(OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())){
            return;
        }

        if (!flag)  {
            SgOmsReleaseOutRequest request = this.builReleaseParam(order,itemList,sapUser);
            //释放商品库存
            ValueHolderV14<String> releaseResult = sgRpcService.releaseOutStock(request);
            AssertUtil.assertException(!releaseResult.isOK(),releaseResult.getMessage());
        }
    }
}
