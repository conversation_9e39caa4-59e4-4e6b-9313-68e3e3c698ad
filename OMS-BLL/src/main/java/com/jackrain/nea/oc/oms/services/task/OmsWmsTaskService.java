package com.jackrain.nea.oc.oms.services.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToWmsTaskMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBWarehouseSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBToWmsTask;
import com.jackrain.nea.oc.oms.model.table.task.OcBWarehouseSplitTask;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.st.service.OrderPushDelayStrategyService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @author: 易邵峰
 * @since: 2020-02-17
 * create at : 2020-02-17 15:05
 */
@Component
public class OmsWmsTaskService {

    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Autowired
    private OcBWarehouseSplitTaskMapper ocBWarehouseSplitTaskMapper;

    @Autowired
    private OcBToWmsTaskMapper ocBToWmsTaskMapper;

    @Autowired
    private OrderPushDelayStrategyService orderPushDelayStrategyService;

    /***
     * 分库查询待拆单的订单ID
     * @param nodeName
     * @param limit
     * @param taskTableName
     * @param status
     * @return
     */
    public List<Long> selectOcBWarehouseSplitTask(String nodeName, int limit, String taskTableName, int status) {
        return ocBWarehouseSplitTaskMapper.selectOcBWarehouseSplitTask(nodeName, limit, taskTableName, status);
    }

    /**
     * 保存待走物流拆单的订单
     *
     * @param orderId
     * @param user
     */
    public void saveOrUpdateOcBWarehouseSplitTask(Long orderId, User user) {
        OcBWarehouseSplitTask ocBWarehouseSplitTask =
                ocBWarehouseSplitTaskMapper.selectOcBWarehouseSplitTaskByOrderId(orderId);
        if (ocBWarehouseSplitTask == null) {
            ocBWarehouseSplitTask = new OcBWarehouseSplitTask();
            ocBWarehouseSplitTask.setId(buildSequenceUtil.buildOcBWarehouseSplitTaskId());
            ocBWarehouseSplitTask.setOrderId(orderId);
            ocBWarehouseSplitTask.setStatus(0);
            makeCreateField(ocBWarehouseSplitTask, user);
            ocBWarehouseSplitTask.setOwnerename(user.getEname());
            ocBWarehouseSplitTask.setModifierename(user.getEname());
            ocBWarehouseSplitTaskMapper.insert(ocBWarehouseSplitTask);
        } else {
            QueryWrapper<OcBWarehouseSplitTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(OcBWarehouseSplitTask::getOrderId, ocBWarehouseSplitTask.getOrderId())
                    .eq(OcBWarehouseSplitTask::getId, ocBWarehouseSplitTask.getId());
            OcBWarehouseSplitTask updateTask = new OcBWarehouseSplitTask();
            makeModiferField(updateTask, user);
            updateTask.setModifierename(user.getEname());
            updateTask.setStatus(0);
            ocBWarehouseSplitTaskMapper.update(updateTask, queryWrapper);
        }
    }

    /**
     * 批量更新仓库待拆单任务状态
     * @param orderIdList
     * @param status
     */
    public void batchUpdateOcBWarehouseSplitTask(List<Long> orderIdList, Integer status){
        this.ocBWarehouseSplitTaskMapper.batchUpdateOcBWarehouseSplitTaskByOrderIds(orderIdList, status);
    }

    /**
     * 保存待传WMS订单
     *
     * @param ocBOrder
     * @param user
     */
    public void saveOrUpdateOcBToWmsTask(OcBOrder ocBOrder, User user) {
        OcBToWmsTask ocBToWmsTask =
                ocBToWmsTaskMapper.selectOcBToWmsTaskByOrderId(ocBOrder.getId());
        Date orderPushDelayDate = orderPushDelayStrategyService.getOrderPushDelayDate(ocBOrder);
        if (ocBToWmsTask == null) {
            ocBToWmsTask = new OcBToWmsTask();
            ocBToWmsTask.setId(buildSequenceUtil.buildOcBToWmsTaskId());
            ocBToWmsTask.setOrderId(ocBOrder.getId());
            ocBToWmsTask.setStatus(0);
            ocBToWmsTask.setPushDelayDate(Optional.ofNullable(orderPushDelayDate).orElse(new Date()));
            makeCreateField(ocBToWmsTask, user);
            ocBToWmsTask.setOwnerename(user.getEname());
            ocBToWmsTask.setModifierename(user.getEname());
            ocBToWmsTaskMapper.insert(ocBToWmsTask);
        } else {
            QueryWrapper<OcBToWmsTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(OcBToWmsTask::getOrderId, ocBOrder.getId())
                    .eq(OcBToWmsTask::getId, ocBToWmsTask.getId());
            OcBToWmsTask updateTask = new OcBToWmsTask();
            makeModiferField(updateTask, user);
            updateTask.setModifierename(user.getEname());
            updateTask.setStatus(0);
            updateTask.setPushDelayDate(Optional.ofNullable(orderPushDelayDate).orElse(new Date()));
            ocBToWmsTaskMapper.update(updateTask, queryWrapper);
        }
    }

    /**
     * 批量更新传WMS任务状态
     * @param orderIdList
     * @param status
     */
    public void batchUpdateOcBToWmsTask(List<Long> orderIdList, Integer status){
        this.ocBToWmsTaskMapper.batchUpdateOcBToWmsTask(orderIdList, status);
    }

    /**
     * 更新传WMS状态
     * @param orderId
     * @param status
     */
    public void updateOcBToWmsTaskStatus(Long orderId, Integer status){
        this.ocBToWmsTaskMapper.updateOcBToWmsTaskStatusByOrderId(status, orderId);
    }

    public void updateOcBToWmsTaskStatusAndDelayTimeByOrderId(Long orderId, Integer status, Date delayTime) {
        this.ocBToWmsTaskMapper.updateOcBToWmsTaskStatusAndDelayTimeByOrderId(status, orderId, delayTime);
    }



    /**
     * @param nodeName
     * @param limit
     * @param taskTableName
     * @param status
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @Description 查询推送wms订单(推单延时过滤 只有oc_b_to_wms_task可以使用)
     * @Date 13:28 2020/9/17
     **/
    public List<Long> selectWmsTaskWithPushDelay(int limit, String taskTableName, int status) {
        return ocBWarehouseSplitTaskMapper.selectWmsTaskWithPushDelay(limit, taskTableName, status);
    }

    /**
     * @param nodeName
     * @param limit
     * @param taskTableName
     * @param status
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @Description 查询推送wms订单推单延时时间为空的数据（只有oc_b_to_wms_task可以使用）
     * @Date 13:29 2020/9/17
     **/
    public List<Long> selectByNodeSqlWithPushDelayNull(int limit, String taskTableName, int status) {
        return ocBWarehouseSplitTaskMapper.selectByNodeSqlWithPushDelayNull(limit, taskTableName, status);
    }


    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        model.setAdClientId((long) user.getClientId());
        model.setAdOrgId((long) user.getOrgId());
        model.setOwnerid(Long.valueOf(user.getId()));
        model.setCreationdate(date);
        model.setOwnername(user.getName());
        model.setModifierid(Long.valueOf(user.getId()));
        model.setModifiername(user.getName());
        model.setModifieddate(date);
        model.setIsactive("Y");
    }

    private void makeModiferField(BaseModel model, User user) {
        Date date = new Date();
        model.setModifierid(Long.valueOf(user.getId()));
        model.setModifiername(user.getName());
        model.setModifieddate(date);
        model.setIsactive("Y");
    }
}
