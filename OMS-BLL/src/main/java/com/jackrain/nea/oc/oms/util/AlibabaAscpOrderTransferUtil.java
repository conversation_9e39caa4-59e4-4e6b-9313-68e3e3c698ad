package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/9/5 下午3:00
 * @description 猫超订单中间表转换成全渠道订
 **/

@Component
@Slf4j
public class AlibabaAscpOrderTransferUtil {

    @Autowired
    private RegionNewService regionService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private CpRpcService cpRpcService;


    public AlibabaAscpOrderTransferUtil() {
    }

    /**
     * 订单唯一码，通过明细tid、oid、sku+头表订单补充信息字段生成
     *
     * @param alibabaAscpOrderRelation 淘宝订单关联表对象
     * @return 订单唯一码
     */
    private String buildUniqueKey(IpAlibabaAscpOrderRelation alibabaAscpOrderRelation) {

        StringBuilder sb = new StringBuilder();
        if (alibabaAscpOrderRelation.getAlibabaAscpOrder() != null) {
            sb.append(alibabaAscpOrderRelation.getOrderNo());
        }

        if (alibabaAscpOrderRelation.getAlibabaAscpOrderItemExList() != null) {
            for (IpBAlibabaAscpOrderItemExt orderItemEx : alibabaAscpOrderRelation.getAlibabaAscpOrderItemExList()) {
                sb.append(orderItemEx.getSubOrderCode());
                sb.append(orderItemEx.getBarcode());
            }
        }
        String sbLength = sb.toString();
        if (sbLength.length() >= 190) {
            sbLength = sbLength.substring(0, 190);
        }
        return sbLength;
    }

    /**
     * 解析是否有组合商品
     *
     * @param alibabaAscpOrderRelation 猫超中间表关联对象
     * @return 是否有组合商品。True=包含
     */
    private boolean hasCombineProduct(IpAlibabaAscpOrderRelation alibabaAscpOrderRelation) {
        for (IpBAlibabaAscpOrderItemExt orderItem : alibabaAscpOrderRelation.getAlibabaAscpOrderItemExList()) {
            if (orderItem.getProdSku() != null &&
                    (orderItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                            || orderItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 转换成零售发货单对象
     *
     * @param alibabaAscpOrderRelation 猫超订单中间表关联对象
     * @return 零售发货单对象
     */
    private OcBOrder buildOcBOrderFromIpAlibabaAscprder(IpAlibabaAscpOrderRelation alibabaAscpOrderRelation) {
        IpBAlibabaAscpOrder alibabaAscpOrder = alibabaAscpOrderRelation.getAlibabaAscpOrder();
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setTid(alibabaAscpOrderRelation.getOrderNo());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);
        BaseModelUtil.initialBaseModelSystemField(order);

        //调整金额
        //所有SKU（最多五个超过，显示数量）。转单赋值空
        order.setAllSku(null);
        //应收金额. =订单金额
        order.setReceivedAmt(null);
        //审核时间
        order.setAuditTime(null);
        //自动审核状态
        order.setAutoAuditStatus(0);
        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        //代销结算金额. 默认为0
        order.setConsignAmt(BigDecimal.ZERO);
        //代销运费. 默认为0
        order.setConsignShipAmt(BigDecimal.ZERO);
        //供应商id
        String supplierId = alibabaAscpOrder.getSupplierId();

        //通过供应商id查找下单店铺
        List<CpShop> cpShopList = cpRpcService.queryShopBySupplierId(supplierId);

        if (CollectionUtils.isNotEmpty(cpShopList)) {
            CpShop shopInfo = cpShopList.get(0);
            order.setCpCShopId(shopInfo.getId());
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreEname(shopInfo.getCpCStoreEname());
            //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreId(shopInfo.getCpCStoreId());
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
            order.setCpCShopEcode(shopInfo.getEcode());
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
        } else {
            throw new NDSException("猫超平台供应商id=" + alibabaAscpOrder.getSupplierId() + "无指定店铺");
        }

        String storeCode = alibabaAscpOrder.getStoreCode();
        List<CpCStore> cpStoreList = cpRpcService.queryStoreByTmallStoreCode(storeCode);

        if (CollectionUtils.isNotEmpty(cpStoreList)) {
            CpCStore cpCStore = cpStoreList.get(0);
            order.setCpCPhyWarehouseId(cpCStore.getCpCPhyWarehouseId());
            order.setCpCPhyWarehouseEcode(cpCStore.getCpCPhyWarehouseEcode());
            order.setCpCPhyWarehouseEname(cpCStore.getCpCPhyWarehouseEname());
            /*order.setCpCStoreId(cpCStore.getId());
            order.setCpCStoreEcode(cpCStore.getEcode());
            order.setCpCStoreEname(cpCStore.getEname());*/
        } else {
            throw new NDSException("猫超仓库编码" + storeCode + "找不到逻辑仓");
        }

        //配货时间. 配货阶段进行赋值
        order.setDistributionTime(null);
        //交易结束时间
        order.setEndTime(alibabaAscpOrder.getEndTime());
        //物流编码. 分配物流后进行赋值
        order.setExpresscode(null);
        //内部备注。后续手动填写
        order.setInsideRemark(null);
        //开票内容
        order.setInvoiceContent(null);
        //商品计算重量。是否需要进行称重。根据系统配置来进行赋值。可能没用。产品也不知道
        order.setIsCalcweight(0);
        //是否组合订单
        boolean hasCombine = this.hasCombineProduct(alibabaAscpOrderRelation);
        order.setIsCombination(hasCombine ? 1 : 0);
        //是否生成开票通知。现在赋值为N。占用订单后再进行赋值
        order.setIsGeninvoiceNotice(0);
        // 是否已给物流。占单后再进行赋值
        // order.setIsGiveLogistic(0);
        // 是否有赠品.0.否。计算完赠品策略赋值
        order.setIsHasgift(0);
        //包含预售商品
        //是否退款中
        order.setIsInreturning(0);
        //是否已经挂起
        order.setIsInterecept(0);
        order.setIsInvented(0);
        //京仓订单
        order.setIsJcorder(0);
        //实缺标记
//        order.setIsLackstock(0);
        //是否合并订单 默认0不合并
        order.setIsMerge(0);
        //是否拆分订单
        order.setIsSplit(0);
        //是否生成调拨零售
        // order.setIsTodrp(0);
        //应收平台金额（京东）
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本.需要计算成本。默认为0
        order.setLogisticsCost(BigDecimal.ZERO);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeOrderId(null);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeSourceCode(alibabaAscpOrderRelation.getOrderNo());
        //订单占单状态
        order.setOccupyStatus(0);
        //操作费.默认为0
        // order.setOperateAmt(BigDecimal.ZERO);
        //下单时间
        order.setOrderDate(DateUtil.stringToDate(alibabaAscpOrder.getOrderCreateTime()));
        //订单来源
        order.setOrderSource(alibabaAscpOrder.getOrderSource());
        //订单状态. 默认状态为50
        order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
        order.setIsHistory("N");
        //占单状态
        order.setOccupyStatus(0);
        //订单标签
        order.setOrderTag(null);
        //订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getVal());
        //原始订单号。默认空
        order.setOrigOrderId(null);
        //原始退货单号
        order.setOrigReturnOrderId(null);
        //出库状态. WMS后调用,已出库未出库,现在没有用
        order.setOutStatus(null);
        //支付方式（淘宝，天猫没有货到付款类型。PRD中写到COD=货到付款的判断可用忽视）
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        order.setPayTime(Objects.isNull(alibabaAscpOrder.getPayTime()) ? new Date() : alibabaAscpOrder.getPayTime());
        //平台
        order.setPlatform(PlatFormEnum.ALIBABAASCP.getCode());
        // 双11的预售状态。现在暂时赋值0
        order.setDouble11PresaleStatus(0);
        //商品数量
        List<IpBAlibabaAscpOrderItemExt> alibabaAscpOrderItemExList = alibabaAscpOrderRelation.getAlibabaAscpOrderItemExList();
        long quality = alibabaAscpOrderItemExList.stream().mapToLong(IpBAlibabaAscpOrderItemExt::getQuantity).sum();
        order.setQtyAll(new BigDecimal(quality));
        order.setSkuKindQty(new BigDecimal(alibabaAscpOrderRelation.getAlibabaAscpOrderItemExList().size()));
        // 买家收货详细地址
        order.setReceiverAddress(alibabaAscpOrder.getReceiverAddress());
        order.setReceiverAddress(OrderAddressConvertUtil.convert(order));
        //买家所在省
        String provinceName = alibabaAscpOrder.getReceiverProvince();
        //买家所在市
        String cityName = alibabaAscpOrder.getReceiverCity();
        //买家所在区
        String areaName = alibabaAscpOrder.getReceiverArea();

        order.setCpCRegionProvinceEname(provinceName);
        order.setCpCRegionCityEname(cityName);
        order.setCpCRegionAreaEname(areaName);

        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
            order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
            order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
        }

        order.setReceiverMobile(alibabaAscpOrder.getReceiverMobile());
        order.setReceiverName(alibabaAscpOrder.getReceiverName());
        order.setReceiverPhone(alibabaAscpOrder.getReceiverPhone());
        order.setReceiverZip(alibabaAscpOrder.getReceiverZipcode());
        //退款审核状态（AG使用）
        order.setRefundConfirmStatus(null);
        //退货状态
        order.setReturnStatus(null);
        //销售员编号
        order.setSalesmanId(null);
        //销售员名称
        order.setSalesmanName(null);
        //扫描出库时间.WMS回传值
        order.setScanTime(null);

        //平台预售活动。暂时用不到。有可能是平台传输过来
        // order.setSendTime(null);
        //配送费用。如果为空，则赋值0.
        order.setShipAmt(alibabaAscpOrder.getPostFee() == null ? BigDecimal.ZERO : alibabaAscpOrder.getPostFee());
        //平台单号信息
        order.setSourceCode(alibabaAscpOrder.getBizOrderCode());
        //拆分原单单号
        order.setSplitOrderId(null);
        //订单补充信息
        order.setSuffixInfo(null);
        //系统备注
        order.setSysremark(null);
        //初始平台单号（确定唯一）
        order.setTid(alibabaAscpOrder.getBizOrderCode());
        //订单唯一码，通过明细tid、oid、sku+头表订单补充信息字段生成
        //order.setUniqueKey(this.buildUniqueKey(alibabaAscpOrderRelation));
        //下单用户
        order.setUserId(null);
        //版本信息
        order.setVersion(0L);
        //商品重量
        order.setWeight(BigDecimal.ZERO);
        //wms撤回状态调用WMS撤回是否成功。1=成功；2=失败
        order.setWmsCancelStatus(0);
        //仓储状态（拣货中，已打印，已装箱）
        //order.setWmsStatus(null);
        //是否插入核销流水
        // order.setIsWriteoff(0);
        //出库状态
        order.setOutStatus(1);
        // 平台状态
        order.setPlatformStatus(String.valueOf(OmsOrderStatus.TO_BE_ASSIGNED));
        if (log.isDebugEnabled()) {
            log.debug("Finish BuildOcOrder OrderNo=" + alibabaAscpOrderRelation.getOrderNo());
        }
        return order;
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            return true;
        }
    }

    /**
     * 订单明细解析时若为福袋或者组合商品是明细为中间表数据
     */
    private OcBOrderItem buildOrderItemFromOrderItem(OcBOrder ocBOrder, IpBAlibabaAscpOrderItemExt orderItemEx) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        BaseModelUtil.initialBaseModelSystemField(item);

        //活动编号. 默认赋值为null
        item.setActiveId(null);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //国标码。SKU 69码。从条码档案中有一个69码字段
        if (orderItemEx.getProdSku() != null) {
            item.setBarcode(orderItemEx.getProdSku().getBarcode69());
        } else {
            item.setBarcode(null);
        }
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);
        item.setIsPresalesku(0);
        //平台条码
        item.setSkuNumiid(orderItemEx.getBarcode() + "");
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        item.setOoid(orderItemEx.getSubOrderCode());
        //退货状态
        item.setRefundStatus(0);
        item.setPsCBrandId(orderItemEx.getProdSku().getPsCBrandId());
        item.setPriceList(orderItemEx.getProdSku().getPricelist());
        //数量
        item.setQty(BigDecimal.valueOf(orderItemEx.getQuantity()));
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //规格。商品条码. normsdetailnames
        //条码id
        //标准重量。商品条码. weight
        //条码编码。
        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
        initialAlibabaAscpItem(orderItemEx, item);

        //库位。不用赋值
//        item.setStoreSite(null);
        item.setTid(ocBOrder.getTid());

        return item;
    }

    /**
     * 初始化initialAlibabaAscpItem内容
     *
     * @param orderItemEx 猫超中间表数据
     * @param item        需要赋值的taobaoorderItem
     */

    private void initialAlibabaAscpItem(IpBAlibabaAscpOrderItemExt orderItemEx, OcBOrderItem item) {
        if (orderItemEx.getProdSku() != null) {
            item.setPsCProId(orderItemEx.getProdSku().getProdId());
            // ProECode
            item.setPsCProEcode(orderItemEx.getProdSku().getProdCode());
            item.setPsCSkuId(orderItemEx.getProdSku().getId());
            item.setSex(orderItemEx.getProdSku().getSex());
            //2019-08-30吊牌价改为取商品表数据
            item.setPriceTag(orderItemEx.getProdSku().getPricelist()); //吊牌价
            item.setPsCClrEcode(orderItemEx.getProdSku().getColorCode());
            item.setPsCClrEname(orderItemEx.getProdSku().getColorName());
            item.setPsCClrId(orderItemEx.getProdSku().getColorId());
            item.setPsCSizeEcode(orderItemEx.getProdSku().getSizeCode());
            item.setPsCSizeEname(orderItemEx.getProdSku().getSizeName());
            item.setPsCSizeId(orderItemEx.getProdSku().getSizeId());
            item.setPsCProMaterieltype(orderItemEx.getProdSku().getMaterialType());
            item.setStandardWeight(orderItemEx.getProdSku().getWeight());
            item.setSkuSpec(orderItemEx.getProdSku().getSkuSpec());
            item.setProType(NumberUtils.toLong(orderItemEx.getProdSku().getSkuType() + ""));
            item.setPsCSkuPtEcode(orderItemEx.getProdSku().getSkuEcode());
            //平台商品名称
            item.setPtProName(orderItemEx.getScItemName());
            //平台商品ID
            item.setSkuNumiid(orderItemEx.getScItemId());
            //商品名称
            item.setPsCProEname(orderItemEx.getProdSku().getName());
            item.setPsCSkuEname(orderItemEx.getProdSku().getSkuName());

            // 增加品类信息 20220923
            item.setMDim4Id(orderItemEx.getProdSku().getMDim4Id());
            item.setMDim6Id(orderItemEx.getProdSku().getMDim6Id());
            if ("Y".equals(orderItemEx.getProdSku().getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }

            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = orderItemEx.getProdSku().getSkuEcode();

            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            if (orderItemEx.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                    || orderItemEx.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                item.setPsCSkuEcode(psSkuEcode); //虚拟条码
                //由于数据库做了对尺寸code和商品code做了非空限制
                item.setQtyGroup(BigDecimal.valueOf(orderItemEx.getQuantity())); //组合商品数量
                item.setProType(NumberUtils.toLong(SkuType.NO_SPLIT_COMBINE + ""));

            } else {
                item.setPsCSkuEcode(psSkuEcode);
                item.setPsCProEname(orderItemEx.getProdSku().getName()); //商品名称
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    public OcBOrderRelation AlibabaAscpOrderToOrder(IpAlibabaAscpOrderRelation alibabaAscpOrderRelation) {
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        OcBOrder orderInfo = this.buildOcBOrderFromIpAlibabaAscprder(alibabaAscpOrderRelation);
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        for (IpBAlibabaAscpOrderItemExt orderItemEx : alibabaAscpOrderRelation.getAlibabaAscpOrderItemExList()) {
            OcBOrderItem item = this.buildOrderItemFromOrderItem(orderInfo, orderItemEx);
            orderItemList.add(item);
        }
        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);
        return orderRelation;
    }
}