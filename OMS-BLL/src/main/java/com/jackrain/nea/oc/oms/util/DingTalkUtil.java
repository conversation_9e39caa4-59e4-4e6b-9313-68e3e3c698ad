package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.exceptions.ApiException;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.nums.EnvEnum;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/6/11
 **/
@Slf4j
@Repository
public class DingTalkUtil {

    @Autowired
    private PropertiesConf propertiesConf;

    //发送群消息
    private static final String SEND_GROUP_OF_NEWS_URL = "https://oapi.dingtalk.com/chat/send";

    //获取toekn
    private static final String GET_ACCESS_TOKEN_URL = "https://oapi.dingtalk.com/gettoken";

    //获取userid
    private static final String GET_USER_ID = "https://oapi.dingtalk.com/gettoken";

    //钉钉机器人配置
    private final String DEFAULT_APP_URL = "https://oapi.dingtalk.com/robot/send?access_token=fc4dc8227a3a71f1407c30b05c3cd6719c8822c5429077637207fb3f07f331fd";

    //天猫周期购告警
    private static final String TMALL_CYCLE_BUY_URL = "https://oapi.dingtalk.com/robot/send?access_token=fbbadf27b9fb9637325482606317cbdd98c680ad4ed6630fa006526ebb0fe865";

    //天猫周期购告警
    private static final String LOGISTIC_URL = "https://oapi.dingtalk.com/robot/send?access_token=94e3d19df676d7b995033d5e30bdc94dcc33d0af904a6b8e6bbfc32ee60ba8bb";

    //TOB部分发货
    private static final String OUT_PART_URL = "https://oapi.dingtalk.com/robot/send?access_token=03ab92ec5a84a1d8087affd3dabd12244d4de6ce9e3638ea1a2f3fafe2995ece";

    //订单非寻源中收到寻源消息
    private static final String FIND_SOURCE_EXCEPTION = "https://oapi.dingtalk.com/robot/send?access_token=1cfa86eb739421a0bf6ca75595aebcb45393a858a893d68536d6c1e1790e7590";

    //发票
    private static final String INVOICE_URL = "https://oapi.dingtalk.com/robot/send?access_token=a07578aaf3ba55cf98e78cb392d9da10219bd9a0b8778180981c9884bfd29c39";

    //告警
    private static final String NOTICE_URL = "https://oapi.dingtalk.com/robot/send?access_token=eaaa09824ae7ee1f07362210f0f7d60407b4b6b3a2417580ac6867d7d47ddfed";


    public String sendMessage(String message) {
        HashMap<String, Object> returnMap = new HashMap();
        String token = getToken();
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        String cid = propertiesConf.getProperty("ding.talk.cid");
        try {
//            DingTalkClient client = new DefaultDingTalkClient(SEND_GROUP_OF_NEWS_URL);
//            OapiChatSendRequest request = new OapiChatSendRequest();
//            request.setChatid(cid);
//            OapiChatSendRequest.Msg msg = new OapiChatSendRequest.Msg();
//            msg.setMsgtype("text");
//            OapiChatSendRequest.Text text = new OapiChatSendRequest.Text();
//            text.setContent(message);
//            msg.setText(text);
//
//            request.setMsg(msg);
//            OapiChatSendResponse response = client.execute(request, token);
//            String result = response.getMessage();
            returnMap.put("isOK", "0");
            return JSON.toJSONString(returnMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String sendRobotMsg(String msg, String mobiles) {
        try {
            if (StringUtils.isEmpty(mobiles)) {
                return null;
            }
            msg = msg.replaceAll("null", "").
                    replaceAll("\\$品牌\\$", "").
                    replaceAll("\\$订单号\\$", "").
                    replaceAll("\\$入库单号\\$", "").
                    replaceAll("\\$到货仓\\$", "").
                    replaceAll("\\$商品总数量\\$", "").
                    replaceAll("\\$预计发货时间\\$", "").
                    replaceAll("\\$预计到货时间\\$", "").
                    replaceAll("\\$箱号\\$", "").
                    replaceAll("\\$物流公司\\$", "").
                    replaceAll("\\$发货实体仓\\$ ", "");
            String appKey = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:ding_talk_url");
            if (StringUtils.isBlank(appKey)) {
                log.debug("get DingTalk appKey error msg:{}", msg);
                return null;
            }
//            DingTalkClient client = new DefaultDingTalkClient(appKey);
//            OapiRobotSendRequest request = new OapiRobotSendRequest();
//            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
//            //***********
//            String[] split = mobiles.split(",");
//            List<String> list = Lists.newArrayList();
//            Collections.addAll(list, split);
//            at.setAtMobiles(list);
//            request.setAt(at);
//
//            request.setMsgtype("markdown");
//            OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
//            markdown.setTitle("价格监控通知");
//            StringBuffer mobilesBuffer = new StringBuffer();
//            for (String s : list) {
//                mobilesBuffer.append("@").append(s);
//            }
//            markdown.setText("预警:" + mobilesBuffer + "  " + msg);
//            request.setMarkdown(markdown);
//            OapiRobotSendResponse execute = client.execute(request);
//            HashMap<String, Object> returnMap = new HashMap();
//            if ("ok".equals(execute.getErrmsg())) {
//                returnMap.put("isOK", "0");
//            } else {
//                log.debug("send DingTalk  error msg:{}, result:{} ", msg, JSON.toJSONString(execute));
//                return null;
//            }
            //return JSON.toJSONString(returnMap);
        } catch (Exception e) {
            log.error(LogUtil.format("推送钉钉消息失败:{}"), Throwables.getStackTraceAsString(e));
        }
        return null;
    }


    public String getToken() {
        try {
            String appKey = propertiesConf.getProperty("ding.talk.appKey");
            String appSecret = propertiesConf.getProperty("ding.talk.appSecret");
//            DingTalkClient client = new DefaultDingTalkClient(GET_ACCESS_TOKEN_URL);
//            OapiGettokenRequest req = new OapiGettokenRequest();
//            req.setHttpMethod("GET");
//            req.setAppkey(appKey);
//            req.setAppsecret(appSecret);
//            OapiGettokenResponse rsp = client.execute(req);
//            String accessToken = rsp.getAccessToken();
            //return accessToken;
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 天猫周期购钉钉告警
     *
     * @param content
     */
    public static void dingTmallCycle(Long orderId, String content) {
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject notic = new JSONObject();
            notic.put("content", "【天猫周期购告警】【" + EnvEnum.getEnv() + "】【" + orderId + "】" + content);
            jsonObject.put("text", notic);
            jsonObject.put("msgtype", "text");
            HttpRestUtil.httpPostUrl(TMALL_CYCLE_BUY_URL, jsonObject);
        } catch (Exception e) {
            log.error("dingNoticeTmallCycle error content:{}", content, e);
        }
    }

    /**
     * 快递费用查询告警
     *
     * @param content
     */
    public static void dingLogisticFee(Long orderId, String content) {
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject notic = new JSONObject();
            notic.put("content", "【快递费用查询告警】【" + EnvEnum.getEnv() + "】【" + orderId + "】" + content);
            jsonObject.put("text", notic);
            jsonObject.put("msgtype", "text");
            HttpRestUtil.httpPostUrl(LOGISTIC_URL, jsonObject);
        } catch (Exception e) {
            log.error("dingLogisticFee error content:{}", content, e);
        }
    }

    /**
     * TOB部分发货
     *
     * @param content
     */
    public static void dingTobOutPart(Long orderId, String content) {
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject notic = new JSONObject();
            notic.put("content", "【TOB部分发货】【" + EnvEnum.getEnv() + "】【" + orderId + "】" + content);
            jsonObject.put("text", notic);
            jsonObject.put("msgtype", "text");
            HttpRestUtil.httpPostUrl(OUT_PART_URL, jsonObject);
        } catch (Exception e) {
            log.error("dingTobOutPart error content:{}", content, e);
        }
    }

    /**
     * 重复收到寻源后消息
     *
     * @param content
     */
    public static void dingFindSourceException(String content) {
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject notic = new JSONObject();
            notic.put("content", "【" + EnvEnum.getEnv() + "】" + content);
            jsonObject.put("text", notic);
            jsonObject.put("msgtype", "text");
            HttpRestUtil.httpPostUrl(FIND_SOURCE_EXCEPTION, jsonObject);
        } catch (Exception e) {
            log.error("dingFindSourceException error content:{}", content, e);
        }
    }

    /**
     * 发票告警
     *
     * @param content
     */
    public static void dingInvoice(String content) {
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject notic = new JSONObject();
            notic.put("content", "【发票告警】【" + EnvEnum.getEnv() + "】" + content);
            jsonObject.put("text", notic);
            jsonObject.put("msgtype", "text");
            HttpRestUtil.httpPostUrl(INVOICE_URL, jsonObject);
        } catch (Exception e) {
            log.error("dingInvoice error content:{}", content, e);
        }
    }

    /**
     * 通用告警
     *
     * @param content
     */
    public static void notice(String content) {
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject notic = new JSONObject();
            notic.put("content", "【告警】【" + EnvEnum.getEnv() + "】" + content);
            jsonObject.put("text", notic);
            jsonObject.put("msgtype", "text");
            HttpRestUtil.httpPostUrl(NOTICE_URL, jsonObject);
        } catch (Exception e) {
            log.error("notice error content:{}", content, e);
        }
    }
}
