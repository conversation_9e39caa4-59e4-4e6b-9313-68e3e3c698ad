package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.model.SgBPhyInStorageItemExt;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderHoldItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.tag.TaggerManager;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: 江家雷
 * @since: 2020/8/8
 * create at : 2020/8/8 15:27
 */
@Component
@Slf4j
public class OmsOrderAutoSplitUtil {

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Autowired
    private OcBOrderHoldItemService holdItemService;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    public OcBOrderRelation saveOrder(List<OrderItemGroup> itemGroups, OcBOrderRelation orderRelation, int index,
                                      int status, List<OcBOrderRelation> jdOrderRelationList, String isPlatformSplit) {
        // 新订单明细
        List<OcBOrderItem> newOcBOrderItemList = new ArrayList<>();
        // 新单
        OcBOrderRelation newOrderRelation = new OcBOrderRelation();

        Long orderId = ModelUtil.getSequence(SystemTableNames.OC_ORDER_TABLE_NAME);
        for (OrderItemGroup itemGroup : itemGroups) {
            newOcBOrderItemList.addAll(itemGroup.getItems());
        }
        newOrderRelation.setOrderInfo(orderRelation.getOrderInfo());
        newOrderRelation.setOrderItemList(newOcBOrderItemList);
        String suffixInfo = (orderRelation.getOrderInfo().getId() + "-AutoSP" + index);
        try {
            orderRelation.getOrderInfo().setSplitReason(SplitReason.SPLIT_OUTOFSTOCK);
            //自定义拆单赋值
            omsOrderSplitReasonUtil.matchReason(orderRelation.getOrderInfo());
            OcBOrder ocBOrder = this.bulidOcbOrder(orderRelation.getOrderInfo(), orderId, suffixInfo, newOcBOrderItemList);
            newOrderRelation.setOrderInfo(ocBOrder);
            List<OcBOrderItem> noMalorderItemList = this.bulidOrderItemList(orderId, newOcBOrderItemList);
            newOrderRelation.setOrderItemList(noMalorderItemList);
            ocBOrder.setOrderStatus(status);

            // tagger.001: @20200710 查单生成新单，需要打标：在单生成后保存前
            TaggerManager.get().doTag(ocBOrder, noMalorderItemList);

            OcBOrderRelation jdOcBOrderRelation = new OcBOrderRelation();

            if ("Y".equals(isPlatformSplit)
                    && PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                    && !"手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
                jdOcBOrderRelation.setOrderInfo(ocBOrder);

                jdOcBOrderRelation.setOrderItemList(newOcBOrderItemList);
                jdOrderRelationList.add(jdOcBOrderRelation);
            } else {
                //保存订单对象
                omsOrderService.saveOrderInfo(newOrderRelation.getOrderInfo());
                for (OcBOrderItem ocBOrderItem : newOrderRelation.getOrderItemList()) {
                    omsOrderItemService.saveOcBOrderItem(ocBOrderItem, orderId);
                }
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_SPLIT.getKey(), "自动拆单新生成订单", "", "", SystemUserResource.getRootUser());
                newOrderRelation.setOrderItemList(noMalorderItemList);
            }
        } catch (Exception ex) {
            throw ex;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsOrderAutoSplitUtil.saveOrder 新单信息-{}", "新单信息"), JSONObject.toJSONString(newOrderRelation));
        }
        return newOrderRelation;
    }


    /**
     * 构造新订单
     *
     * @param ocBorderDto      原始订单对象
     * @param orderNewId       新订单Id
     * @param suffixInfo       订单补充信息
     * @param newOrderItemList 订单明细信息
     * @return 拆分后的订单对象
     */
    private OcBOrder bulidOcbOrder(OcBOrder ocBorderDto, Long orderNewId, String suffixInfo, List<OcBOrderItem> newOrderItemList) {

        OcBOrder ocBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(ocBorderDto, ocBOrder);
        //设置ID
        ocBOrder.setId(orderNewId);
        //订单编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //是否拆分原单
        ocBOrder.setIsSplit(1);
        // 拆单次数
        ocBOrder.setQtySplit(Optional.ofNullable(ocBorderDto.getQtySplit()).orElse(0L) + 1);
        //拆分原订单号
        ocBOrder.setSplitOrderId(ocBorderDto.getId());
        //平台单号
        ocBOrder.setSourceCode(ocBorderDto.getSourceCode());
        //oaid
        ocBOrder.setOaid(ocBorderDto.getOaid());
        //设置创建人
        ocBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        ocBOrder.setCreationdate(new Date());
        //订单补充信息
        ocBOrder.setSuffixInfo(suffixInfo);
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        //配送费用复制
        ocBOrder.setShipAmt(Optional.ofNullable(ocBorderDto.getShipAmt()).orElse(BigDecimal.ZERO));
        //服务费复制
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBorderDto.getServiceAmt()).orElse(BigDecimal.ZERO));
        //系统备注
        ocBOrder.setSysremark("");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("生成新订单主表信息为:{}", ocBOrder.getId()), JSONObject.toJSONString(ocBOrder));

        }
        // 根据明细的退款状态 判断主表的退款标记
        boolean isHashReturn = false;
        if (CollectionUtils.isNotEmpty(newOrderItemList)) {
            for (OcBOrderItem item : newOrderItemList) {
                // 如果存在退款状态，并且状态不为：关闭，则需要打标
                if (StringUtils.isNotBlank(item.getPtReturnStatus())
                        && OcOrderRefundStatusEnum.CLOSED.getVal() != item.getRefundStatus()) {
                    isHashReturn = true;
                    break;
                }
            }
        }
        // 如果不退则取消标记
        if (!isHashReturn) {
            //是否已经拦截
            ocBOrder.setIsInterecept(0);
            //是否已经退款中
            ocBOrder.setIsInreturning(0);
        }
        return ocBOrder;
    }

    /**
     * 构造新明细
     *
     * @param orderId       新单Id
     * @param orderItemList 原单明细List
     * @return
     */
    private List<OcBOrderItem> bulidOrderItemList(Long orderId, List<OcBOrderItem> orderItemList) {

        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem ocbItemDto : orderItemList) {
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(ocbItemDto, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单Id
            orderItem.setOcBOrderId(orderId);
            //修改人
            orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
            //修改时间
            orderItem.setModifieddate(new Date());
            //缺货数量置为0
            orderItem.setQtyLost(BigDecimal.ZERO);
            ocBOrderItemList.add(orderItem);
        }
        return ocBOrderItemList;
    }

    /**
     * @param commonOrderRelation 拆分后的新订单信息
     * @param newWareHouseId      重新分仓后的新仓库
     */
    public OcBOrderRelation distributeWarehouse(OcBOrderRelation commonOrderRelation, Long newWareHouseId) {
        //根据实体仓id查询实体仓name和code
        CpCPhyWarehouse firstCpCPhyWarehouse = cpRpcService.queryByWarehouseId(newWareHouseId);
        if (firstCpCPhyWarehouse == null) {
            throw new NDSException("实体仓Id" + newWareHouseId + "对应的实体仓信息不存在,或调用CP接口异常");
        }
        //将分配到的实体仓赋值到订单信息中//将分配到的实体仓赋值到订单信息中
        commonOrderRelation.getOrderInfo().setCpCPhyWarehouseEname(firstCpCPhyWarehouse.getEname());
        commonOrderRelation.getOrderInfo().setCpCPhyWarehouseId(newWareHouseId);
        commonOrderRelation.getOrderInfo().setCpCPhyWarehouseEcode(firstCpCPhyWarehouse.getEcode());
        //如果实体仓是o2o仓库，对订单进行打标
        if (StringUtils.equals(firstCpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
            commonOrderRelation.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            commonOrderRelation.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
        }
        omsOrderService.updateOrderInfo(commonOrderRelation.getOrderInfo());

        OcBOrder commonOcBOrder = omsOrderService.selectOrderInfo(commonOrderRelation.getOrderInfo().getId());
        commonOrderRelation.setOrderInfo(commonOcBOrder);
        return commonOrderRelation;
    }

    /**
     * 分物流
     *
     * @param ocBOrder
     * @return
     */
    public void distributeLogistics(OcBOrder ocBOrder) {
        //然后对于新订单执行分配物流服务
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        orderRelation.setOrderInfo(ocBOrder);
        CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderRelation);

        if (cpCLogistics != null) {
            String message =
                    "订单OrderId" + ocBOrder.getId() + "拆分新订单调用分物流服务,订单分配到物流公司.返回物流公司Id[" + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "]";
            ocBOrder.setCpCLogisticsId(cpCLogistics.getId());
            ocBOrder.setCpCLogisticsEname(cpCLogistics.getEname());
            ocBOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
            //插入日志
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null,
                    SystemUserResource.getRootUser());
        } else {
            String errorMessage =
                    "订单OrderId" + ocBOrder.getId() + "拆分新订单调用分物流服务未匹配到有效物流公司";
            //为了清除原有的物流公司
            ocBOrder.setSysremark(errorMessage);
            ocBOrder.setCpCLogisticsId(0L);
            ocBOrder.setCpCLogisticsEname("");
            ocBOrder.setCpCLogisticsEcode("");
            //插入日志
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null,
                    SystemUserResource.getRootUser());
        }
        orderRelation.setOrderInfo(ocBOrder);
        omsOrderService.updateOrderInfo(ocBOrder);
    }

    /**
     * 重新整理主表金额,同时打组合标
     *
     * @param orderRelation
     * @param oriOrderId
     * @return
     */
    public OcBOrderRelation reorganizeAmount(OcBOrderRelation orderRelation, Long oriOrderId) {
        OcBOrder ocBOrder = orderRelation.getOrderInfo();
        List<OcBOrderItem> itemList = orderRelation.getOrderItemList();
        //重新计算商品数量
        BigDecimal normalTotal = BigDecimal.ZERO;
        //重新计算商品总额
        BigDecimal priceListTotal = BigDecimal.ZERO;
        //指定商品优惠金额
        BigDecimal amtDiscountTotal = BigDecimal.ZERO;
        //平摊金额之和
        BigDecimal orderSplitAmtTotal = BigDecimal.ZERO;
        //代销结算金额
        BigDecimal consignmentTotal = BigDecimal.ZERO;
        //应收金额求和
        BigDecimal receivableTotal = BigDecimal.ZERO;
        //调整金额之和
        BigDecimal adjustTotal = BigDecimal.ZERO;
        // SKU条数（排除组合商品的条数）
        int orderItemNum = 0;
        int isCombineOrGift = 0;
        for (OcBOrderItem item : itemList) {
            if (item.getProType() != null && item.getProType().intValue() == 4) {
                continue;
            }
            // 商品数量总和
            normalTotal = normalTotal.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            orderItemNum++;
            if (item.getIsGift() != null && item.getIsGift().intValue() == 1) {
                continue;
            }
            if (item.getProType() != null && (item.getProType().intValue() == 1 || item.getProType().intValue() == 2)) {
                isCombineOrGift = 1;
            }
            //商品金额
            priceListTotal =
                    priceListTotal.add(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO).multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            //优惠金额计算
            amtDiscountTotal = amtDiscountTotal.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            //平摊金额求和
            orderSplitAmtTotal =
                    orderSplitAmtTotal.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            //代销结算金额求和
            consignmentTotal =
                    consignmentTotal.add(Optional.ofNullable(item.getDistributionPrice()).orElse(BigDecimal.ZERO));
            //应收金额求和
            receivableTotal =
                    receivableTotal.add(Optional.ofNullable(item.getPriceList()).orElse(BigDecimal.ZERO).multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            //调整金额之和
            adjustTotal = adjustTotal.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
        }
        BigDecimal orderAmtTotal = priceListTotal
                .add(Optional.ofNullable(ocBOrder.getShipAmt()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO))
                .add(adjustTotal)
                .subtract(amtDiscountTotal)
                .subtract(orderSplitAmtTotal);
        //	【商品数量】：取明细中所有数量的合计；
        ocBOrder.setQtyAll(normalTotal);
        ocBOrder.setSkuKindQty(new BigDecimal(orderItemNum));
        // 商品金额
        ocBOrder.setProductAmt(priceListTotal);
        // 商品优惠金额
        ocBOrder.setProductDiscountAmt(amtDiscountTotal);
        // 【订单优惠金额】：整单优惠金额
        ocBOrder.setOrderDiscountAmt(orderSplitAmtTotal);
        //	【订单总额】：商品总额+物流费用+调整金额+服务费-商品优惠金额-订单优惠金额
        ocBOrder.setConsignAmt(consignmentTotal);
        // 代销结算价*数量
        ocBOrder.setAmtReceive(receivableTotal);
        // 商品标准价*数量
        ocBOrder.setOrderAmt(orderAmtTotal);
        //	【已支付金额】（已收金额）：等于计算后的订单总额
        ocBOrder.setReceivedAmt(orderAmtTotal);
        //	【调整金额之和】：取明细中所有调整金额的合计；
        ocBOrder.setAdjustAmt(adjustTotal);
        // 是否组合商品
        ocBOrder.setIsCombination(isCombineOrGift);
        // 更新HOLD单标和退款中的标
        List<OcBOrderItem> refundItems = itemList.stream()
                .filter(item -> Objects.equals(OcBorderListEnums.OrderRefundStatus.WAIT_SELLER_AGREE.getVal(),
                        item.getRefundStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(refundItems)) {
            // 插入HOLD单明细
            holdItemService.businessHold(ocBOrder.getId(), OrderHoldReasonEnum.REFUND_HOLD);
            // 发货前退款，打退款中的标
            ocBOrder.setIsInreturning(1);
        } else {
            ocBOrder.setIsInreturning(0);
            ocBOrder.setIsInterecept(0);
        }
        // 查询原单HOLD单记录，并复制HOLD单记录
        List<Integer> list = new ArrayList<>();
        list.add(OrderHoldReasonEnum.LIVECAST_HOLD.getKey());
        list.add(OrderHoldReasonEnum.BUYER_HOLD.getKey());
        List<OcBOrderHoldItem> holdItems =
                holdItemService.selectOrderHoldItemsByHoldReasons(oriOrderId, list);
        if (CollectionUtils.isNotEmpty(holdItems)) {
            List<OcBOrderHoldItem> items = holdItems.stream()
                    .filter(item -> Optional.ofNullable(item.getHoldStatus()).orElse(OcBOrderHoldConst.NO)
                            .equalsIgnoreCase(OcBOrderHoldConst.YES)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                ocBOrder.setIsInterecept(1);
            }
            holdItems.forEach(item -> item.setOcBOrderId(ocBOrder.getId()));
            holdItemService.saveOrderHoldItemList(holdItems);
        }
        omsOrderService.updateOrderInfo(ocBOrder);
        return orderRelation;
    }

    /**
     * 拿到原单对应的组合商品明细 存储到明细表
     *
     * @param orderId          原单ID
     * @param ocBOrderRelation 新单信息
     */
    public void saveCombinedOrGiftBagItem(Long orderId, OcBOrderRelation ocBOrderRelation) {
        List<String> combinedOrGiftBag = ocBOrderRelation.getOrderItemList().stream()
                .filter(it -> StringUtils.isNotEmpty(it.getGiftbagSku()))
                .map(OcBOrderItem::getGiftbagSku)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(combinedOrGiftBag)) {
            List<OcBOrderItem> list = omsOrderItemService.getOrderItemOcBOrderId(orderId);
            List<OcBOrderItem> resultList = list.stream()
                    .filter(ocBOrderItem -> ocBOrderItem.getProType() == 4 && combinedOrGiftBag.contains(ocBOrderItem.getGiftbagSku()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (OcBOrderItem item : resultList) {
                    OcBOrderItem newItem = new OcBOrderItem();
                    BeanUtils.copyProperties(item, newItem);
                    newItem.setId(ModelUtil.getSequence("oc_b_order_item"));
                    newItem.setOcBOrderId(ocBOrderRelation.getOrderInfo().getId());
                    newItem.setModifieddate(new Date());
                    try {
                        omsOrderItemService.saveOcBOrderItem(newItem, ocBOrderRelation.getOrderInfo().getId());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取组合商品或福袋的明细+挂靠赠品
     *
     * @param orderItemList
     * @return
     */
    public List<OrderItemGroup> getCombinedOrGiftBag(List<OcBOrderItem> orderItemList) {
        List<OrderItemGroup> orderItemGroupList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderItemList)) {
            return orderItemGroupList;
        }
        Map<String, List<OcBOrderItem>> giftMap = orderItemList.stream()
                .filter(item -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())
                        && StringUtils.isNotBlank(item.getGiftRelation()))
                .collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
        Map<String, List<OcBOrderItem>> combineMap = orderItemList.stream()
                .filter(item -> (Long.valueOf(SkuType.GIFT_PRODUCT).equals(item.getProType())
                        || Long.valueOf(SkuType.COMBINE_PRODUCT).equals(item.getProType()))
                        && !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift()))
                .collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        if (combineMap != null && !combineMap.isEmpty()) {
            for (String groupGoodsMark : combineMap.keySet()) {
                if (CollectionUtils.isNotEmpty(combineMap.get(groupGoodsMark))) {
                    List<OcBOrderItem> temp = new ArrayList<>();
                    temp.addAll(combineMap.get(groupGoodsMark));
                    String giftRelation = Optional.ofNullable(temp.get(0).getGiftRelation()).orElse("");
                    if (StringUtils.isNotBlank(giftRelation) && giftMap != null && CollectionUtils.isNotEmpty(giftMap.get(giftRelation))) {
                        temp.addAll(giftMap.get(giftRelation));
                    }
                    OrderItemGroup orderItemGroup = new OrderItemGroup();
                    orderItemGroup.setItems(temp);
                    orderItemGroup.setCode(groupGoodsMark);
                    orderItemGroupList.add(orderItemGroup);
                }
            }
        }
        return orderItemGroupList;
    }

    /***
     * 获取 普通商品及其挂靠赠品，组合商品及其挂靠赠品
     * @param orderItemList
     * @return
     */
    public List<OrderItemGroup> getGoodGroupList(List<OcBOrderItem> orderItemList) {
        List<OrderItemGroup> allList = new ArrayList<>();
        List<OrderItemGroup> goodsList = this.getGoodsList(orderItemList);
        List<OrderItemGroup> giftBagList = this.getCombinedOrGiftBag(orderItemList);
        if (CollectionUtils.isNotEmpty(goodsList)) {
            allList.addAll(goodsList);
        }
        if (CollectionUtils.isNotEmpty(giftBagList)) {
            allList.addAll(giftBagList);
        }
        return allList;
    }

    // 获取普通赠品
    public List<OcBOrderItem> getGiftList(List<OcBOrderItem> orderItemList) {
        if (CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        List<OcBOrderItem> results = new ArrayList<>();
        List<String> giftRelationList = orderItemList.stream()
                .filter(it -> !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                        && StringUtils.isNotEmpty(it.getGiftRelation()))
                .map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
        for (OcBOrderItem item : orderItemList) {
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())) {
                if (StringUtils.isEmpty(item.getGiftRelation())) {
                    results.add(item);
                } else if (CollectionUtils.isEmpty(giftRelationList) || !giftRelationList.contains(item.getGiftRelation())) {
                    results.add(item);
                }
            }
        }
        return results;
    }

    /**
     * 获取普通商品及其下挂商品，并做好分组
     *
     * @param orderItemList
     * @return
     */
    public List<OrderItemGroup> getGoodsList(List<OcBOrderItem> orderItemList) {
        List<OrderItemGroup> orderItemGroupList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        Map<String, List<OcBOrderItem>> giftMap = orderItemList.stream()
                .filter(item -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())
                        && StringUtils.isNotBlank(item.getGiftRelation()))
                .collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
        for (OcBOrderItem item : orderItemList) {
            if ((!Long.valueOf(SkuType.NORMAL_PRODUCT).equals(item.getProType())
                    && !Long.valueOf(SkuType.PRE_SALE_PRODUCT).equals(item.getProType()))
                    || OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())) {
                continue;
            }
            List<OcBOrderItem> temp = new ArrayList<>();
            temp.add(item);
            String giftRelation = Optional.ofNullable(item.getGiftRelation()).orElse("");
            if (StringUtils.isNotBlank(giftRelation)
                    && giftMap != null && CollectionUtils.isNotEmpty(giftMap.get(giftRelation))) {
                temp.addAll(giftMap.get(giftRelation));
            }
            OrderItemGroup orderItemGroup = new OrderItemGroup();
            orderItemGroup.setItems(temp);
            orderItemGroup.setCode(item.getPsCSkuEcode() + giftRelation);

            orderItemGroupList.add(orderItemGroup);
        }
        return orderItemGroupList;
    }

    // 构建有库存的组合商品+挂靠赠品
    public void buildCombinedOrderItemGroupMap(List<OmsOrderAutoSplitUtil.OrderItemGroup> combinedOrGiftBagList,
                                               //List<OcBOrderItem> orderItemList,
                                               Map<String, List<SgBPhyInStorageItemExt>> skuCountMap,
                                               Long defaultPhyWarehouseId,
                                               Map<Long, List<OcBOrderItem>> shortageSkuMap,
                                               Map<Long, List<OrderItemGroup>> combinedMap,
                                               Map<OrderItemGroup, Set<Long>> combinedGroupMap) {
        if (CollectionUtils.isEmpty(combinedOrGiftBagList)) {
            return;
        }
        for (OrderItemGroup orderItemGroup : combinedOrGiftBagList) {
            List<OcBOrderItem> tmpList = orderItemGroup.getItems();
            Map<OrderItemGroup, Set<Long>> tempCombinedGroupMap = getSatisfyStockCompare(tmpList, skuCountMap);
            Map<Long, List<OrderItemGroup>> tempCombinedMap = getWareSatisfyStockCompare(tempCombinedGroupMap);
            if (tempCombinedGroupMap == null || tempCombinedGroupMap.isEmpty()) {
                shortageSkuMap.get(defaultPhyWarehouseId).addAll(tmpList);
            } else {
                combinedGroupMap = mergeOrderItemGroupMapGroup(combinedGroupMap, tempCombinedGroupMap);
                combinedMap = mergeMapGroup(combinedMap, tempCombinedMap);
            }
        }
        /*Iterator<String> iterator = goodsOrGiftMap.keySet().iterator();
        while (iterator.hasNext()) {
            String key = (String) iterator.next();
            List<OcBOrderItem> tmpList = goodsOrGiftMap.get(key);
            Map<OrderItemGroup, Set<Long>> tempCombinedGroupMap = getSatisfyStockCompare(tmpList, skuCountMap);
            Map<Long, List<OrderItemGroup>> tempCombinedMap = getWareSatisfyStockCompare(tempCombinedGroupMap);
            if (tempCombinedGroupMap == null || tempCombinedGroupMap.isEmpty()) {
                shortageSkuMap.get(defaultPhyWarehouseId).addAll(tmpList);
            } else {
                combinedGroupMap = mergeOrderItemGroupMapGroup(combinedGroupMap, tempCombinedGroupMap);
                combinedMap = mergeMapGroup(combinedMap, tempCombinedMap);
            }
        }*/
    }

    // 构建普通商品组
    public void buildOrderItemGroupMap(Map<String, List<SgBPhyInStorageItemExt>> skuCountMap,
                                       Long defaultPhyWarehouseId,
                                       Map<Long, List<OcBOrderItem>> shortageSkuMap,
                                       Map<Long, List<OrderItemGroup>> goodsMap,
                                       Map<OrderItemGroup, Set<Long>> goodsGroupMap,
                                       List<OcBOrderItem> goodsList) {
        if (CollectionUtils.isNotEmpty(goodsList)) {
            for (OcBOrderItem temp : goodsList) {
                List<OcBOrderItem> tempList = new ArrayList<>();
                tempList.add(temp);
                Map<OrderItemGroup, Set<Long>> tempGoodsGroupMap = getSatisfyStockCompare(tempList, skuCountMap);
                Map<Long, List<OrderItemGroup>> tempGoodsMap = getWareSatisfyStockCompare(tempGoodsGroupMap);
                if (tempGoodsGroupMap.isEmpty()) {
                    shortageSkuMap.get(defaultPhyWarehouseId).addAll(tempList);
                } else {
                    goodsGroupMap = mergeOrderItemGroupMapGroup(goodsGroupMap, tempGoodsGroupMap);
                    goodsMap = mergeMapGroup(goodsMap, tempGoodsMap);
                }
            }
        }
    }

    // 得到商品组作为KEY的MAP
    public Map<OrderItemGroup, Set<Long>> getSatisfyStockCompare(List<OcBOrderItem> tmpCodeList, Map<String,
            List<SgBPhyInStorageItemExt>> skuCountMap) {
        Map<OrderItemGroup, Set<Long>> satisfyStockMap = new HashMap<>();
        Random random = new Random();
        String code =
                tmpCodeList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.joining()) + random.nextInt(1000);
        //商品列表中的sku
        List<String> goodsSkuList =
                tmpCodeList.stream().filter(ocBOrderItem -> ocBOrderItem.getProType() != 4).map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
        List<String> storageList = new ArrayList<>(skuCountMap.keySet());
        if (!storageList.containsAll(goodsSkuList)) {
            return satisfyStockMap;
        }
        Map<String, Set<Long>> resultMap = new HashMap<>();
        for (OcBOrderItem item : tmpCodeList) {
            if (item.getProType() == 4) {
                continue;
            }
            List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExt = skuCountMap.get(item.getPsCSkuEcode());
            Set<Long> set = new HashSet<>();
            for (SgBPhyInStorageItemExt temp : sgBPhyInStorageItemExt) {
                if (item.getQty().compareTo(temp.getTotal_qty_available()) <= 0) {
                    set.add(temp.getAdvise_phy_warehouse_id());
                    resultMap.put(item.getPsCSkuEcode(), set);
                }
            }
        }
        storageList = new ArrayList<>(resultMap.keySet());
        // 库存sku不包含商品sku
        if (!storageList.containsAll(goodsSkuList)) {
            return satisfyStockMap;
        }
        Set<Long> resultSet = new HashSet<>();
        for (String key : resultMap.keySet()) {
            if (resultSet.isEmpty()) {
                resultSet.addAll(resultMap.get(key));
            } else {
                resultSet = Sets.intersection(resultSet, resultMap.get(key));
            }
        }
        if (!resultSet.isEmpty()) {
            OrderItemGroup group = new OrderItemGroup();
            group.setCode(code);
            group.setItems(tmpCodeList);
            satisfyStockMap.put(group, resultSet);
        }
        return satisfyStockMap;
    }

    // 转换为仓库作为KEY的MAP
    public Map<Long, List<OrderItemGroup>> getWareSatisfyStockCompare(Map<OrderItemGroup, Set<Long>> groupMap) {
        Map<Long, List<OrderItemGroup>> satisfyStockMap = new HashMap<>();
        if (!groupMap.isEmpty()) {
            Iterator<OrderItemGroup> it = groupMap.keySet().iterator();
            while (it.hasNext()) {
                OrderItemGroup item = it.next();
                Set<Long> wareSet = groupMap.get(item);
                for (Long wareId : wareSet) {
                    if (satisfyStockMap.get(wareId) == null) {
                        List<OrderItemGroup> list = new ArrayList<>();
                        list.add(item);
                        satisfyStockMap.put(wareId, list);
                    } else {
                        if (!satisfyStockMap.get(wareId).contains(item)) {
                            satisfyStockMap.get(wareId).add(item);
                        }
                    }
                }
            }
        }
        return satisfyStockMap;
    }


    /***
     * 对所有有库存的商品进行 按照仓库ID进行拆分
     * @param startMap 仓库，商品对应的集合
     * @param wareHouseResultMap 分组后的结果
     */
    public void doSplitOrder(Map<OrderItemGroup, Set<Long>> startMap,
                             Map<Long, List<OrderItemGroup>> wareHouseResultMap) {
        while (!startMap.isEmpty()) {
            Iterator<OrderItemGroup> itemGroupIterator = startMap.keySet().iterator();
            Map<Long, List<OrderItemGroup>> sortedMap = new HashMap<>();
            while (itemGroupIterator.hasNext()) {
                OrderItemGroup key = itemGroupIterator.next();
                Set<Long> list = startMap.get(key);
                for (Long wareId : list) {
                    if (sortedMap.get(wareId) == null) {
                        List<OrderItemGroup> tempList = new ArrayList<>();
                        tempList.add(key);
                        sortedMap.put(wareId, tempList);
                    } else {
                        sortedMap.get(wareId).add(key);
                    }
                }
            }
            sortedMap = sortMethodByValue(sortedMap);
            Iterator<Long> resultIt = sortedMap.keySet().iterator();
            Long key = resultIt.next();
            for (OrderItemGroup orderItemGroup : sortedMap.get(key)) {
                startMap.remove(orderItemGroup);
            }
            wareHouseResultMap.put(key, sortedMap.get(key));
        }
    }

    /**
     * 缺货商品都为赠品，从正常商品中随机拿出一个商品与赠品一起放到缺货列表
     *
     * @param defaultPhyWarehouseId 缺货仓ID
     * @param shortageSkuMap        缺货商品对应的MAP
     * @param startMap              全部有库存的商品组（除了赠品）
     */
    public void goodsBindDefaultPhyWarehouse(Long defaultPhyWarehouseId, Map<Long, List<OcBOrderItem>> shortageSkuMap
            , Map<OrderItemGroup, Set<Long>> startMap) {
        if (!shortageSkuMap.isEmpty()) {
            List<OcBOrderItem> oldItems = shortageSkuMap.get(defaultPhyWarehouseId);
            if (CollectionUtils.isNotEmpty(oldItems)) {
                List<OcBOrderItem> items =
                        oldItems.stream().filter(e -> e.getIsGift() == null || e.getIsGift() != 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(items)) {
                    // 缺货商品都为赠品 需要从正常商品中随机拿出一个商品与赠品一起放到缺货列表
                    Iterator<OrderItemGroup> iterator = startMap.keySet().iterator();
                    OrderItemGroup orderItemGroup = iterator.next();
                    oldItems.addAll(orderItemGroup.getItems());
                    iterator.remove();
                }
            }
        }
    }

    /***
     * 将缺货的赠品挂到缺货仓
     * @param defaultPhyWarehouseId 缺货仓ID
     * @param shortageSkuMap 缺货商品列表
     * @param giftGroupMap 赠品信息
     */
    public void giftBindDefautPhyWarehouse(Long defaultPhyWarehouseId, Map<Long, List<OcBOrderItem>> shortageSkuMap,
                                           Map<OrderItemGroup, Set<Long>> giftGroupMap) {
        if (!giftGroupMap.isEmpty()) {
            Iterator it = giftGroupMap.keySet().iterator();
            while (it.hasNext()) {
                OrderItemGroup itemGroup = (OrderItemGroup) it.next();
                shortageSkuMap.get(defaultPhyWarehouseId).addAll(itemGroup.getItems());
                it.remove();
            }
        }
    }

    /**
     * 赠品不能单独发货，将赠品与商品绑定到一起
     *
     * @param giftGroupMap
     * @param startMap
     */
    public void bindGiftAndGoods(Map<OrderItemGroup, Set<Long>> giftGroupMap, Map<OrderItemGroup, Set<Long>> startMap) {
        if (!giftGroupMap.isEmpty()) {
            Iterator giftIt = giftGroupMap.keySet().iterator();
            while (giftIt.hasNext()) {
                OrderItemGroup key = (OrderItemGroup) giftIt.next();
                Set<Long> giftWareSet = giftGroupMap.get(key);
                for (OrderItemGroup itemKey : startMap.keySet()) {
                    Set<Long> wareSet = startMap.get(itemKey);
                    // 取交集
                    Set<Long> resultSet = new HashSet();
                    resultSet.addAll(giftWareSet);
                    resultSet.retainAll(wareSet);
                    if (CollectionUtils.isNotEmpty(resultSet)) {
                        itemKey.getItems().addAll(key.getItems());
                        startMap.put(itemKey, resultSet);
                        giftIt.remove();
                        break;
                    }
                }
            }
        }
    }

    private Map<Long, List<OrderItemGroup>> mergeMapGroup(Map<Long, List<OrderItemGroup>> resultMap, Map<Long,
            List<OrderItemGroup>> paramMap) {
        if (paramMap == null || paramMap.isEmpty()) {
            return resultMap;
        }
        for (Long key : paramMap.keySet()) {
            if (resultMap.get(key) == null) {
                resultMap.put(key, paramMap.get(key));
            } else {
                if (paramMap.get(key) != null) {
                    resultMap.get(key).addAll(paramMap.get(key));
                }
            }
        }
        return resultMap;
    }

    private Map<OrderItemGroup, Set<Long>> mergeOrderItemGroupMapGroup(Map<OrderItemGroup, Set<Long>> resultMap,
                                                                       Map<OrderItemGroup, Set<Long>> paramMap) {
        if (paramMap == null || paramMap.isEmpty()) {
            return resultMap;
        }
        for (OrderItemGroup key : paramMap.keySet()) {
            if (resultMap.get(key) == null) {
                resultMap.put(key, paramMap.get(key));
            } else {
                if (paramMap.get(key) != null) {
                    resultMap.get(key).addAll(paramMap.get(key));
                }
            }
        }
        return resultMap;
    }

    // 根据value的size大小进行从大到小排序
    public Map<Long, List<OrderItemGroup>> sortMethodByValue(Map<Long, List<OrderItemGroup>> wareHouseAndSkuSizeMap) {
        //map根据Value的排序方法[降序]====开始
        List<Map.Entry<Long, List<OrderItemGroup>>> list = new LinkedList<>(wareHouseAndSkuSizeMap.entrySet());
        Collections.sort(list, (o1, o2) -> o2.getValue().size() - o1.getValue().size());
        Map<Long, List<OrderItemGroup>> sortResult = new LinkedHashMap();
        for (Map.Entry<Long, List<OrderItemGroup>> entry : list) {
            sortResult.put(entry.getKey(), entry.getValue());
        }
        return sortResult;
    }

    public static class OrderItemGroup {

        private List<OcBOrderItem> items;

        private String code;

        public List<OcBOrderItem> getItems() {
            return items;
        }

        public void setItems(List<OcBOrderItem> items) {
            this.items = items;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj instanceof OrderItemGroup) {
                OrderItemGroup itemGroup = (OrderItemGroup) obj;
                return code.equals(itemGroup.getCode());
            }
            return false;
        }

        @Override
        public int hashCode() {
            return code.hashCode();
        }
    }
}