package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.dms.LabelingRequirementsQueryResult;
import com.jackrain.nea.oc.oms.api.st.StCHoldOrderQueryCmd;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.result.OcBPreOrderModelResult;
import com.jackrain.nea.oc.oms.util.PrintLogUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.api.AuditMarkQueryCmd;
import com.jackrain.nea.st.api.CompensateQueryCmd;
import com.jackrain.nea.st.api.ExpressAllocationCheckCmd;
import com.jackrain.nea.st.api.ExpressQueryServiceCmd;
import com.jackrain.nea.st.api.JdDistributionLogisticsCmd;
import com.jackrain.nea.st.api.OrderUrgentStrategyCmd;
import com.jackrain.nea.st.api.OtherFeesQueryServiceCmd;
import com.jackrain.nea.st.api.SellOwngoodsQueryCmd;
import com.jackrain.nea.st.api.SendPlanRuleQueryServiceCmd;
import com.jackrain.nea.st.api.ShopStrategyDefaultStoreQueryCmd;
import com.jackrain.nea.st.api.ShopStrategyQueryServiceCmd;
import com.jackrain.nea.st.api.SplitOrderReasonCmd;
import com.jackrain.nea.st.api.StAddedServiceStrategyQueryCmd;
import com.jackrain.nea.st.api.StAddedServiceTypeDocQueryCmd;
import com.jackrain.nea.st.api.StBansAreaStrategyQueryCmd;
import com.jackrain.nea.st.api.StBnQueryCmd;
import com.jackrain.nea.st.api.StCAllocationCostQueryCmd;
import com.jackrain.nea.st.api.StCAllocationStorageCostQueryCmd;
import com.jackrain.nea.st.api.StCCustomLabelQueryCmd;
import com.jackrain.nea.st.api.StCDepositPreSaleSinkQueryCmd;
import com.jackrain.nea.st.api.StCDetentionPolicyQueryCmd;
import com.jackrain.nea.st.api.StCExpressPriceStrategyQueryCmd;
import com.jackrain.nea.st.api.StCFullcarCostQueryCmd;
import com.jackrain.nea.st.api.StCKeywordsInterceptStrategyQueryCmd;
import com.jackrain.nea.st.api.StCMatchAbnormalStrategyMatchCmd;
import com.jackrain.nea.st.api.StCMsgStrategyQueryCmd;
import com.jackrain.nea.st.api.StCOrderLabelQueryCmd;
import com.jackrain.nea.st.api.StCOrderPushDelayStrategyQueryCmd;
import com.jackrain.nea.st.api.StCPreOccupyProvincePriorityQueryCmd;
import com.jackrain.nea.st.api.StCPreOccupyWarehousePriorityQueryCmd;
import com.jackrain.nea.st.api.StCPreorderModelStrategyCmd;
import com.jackrain.nea.st.api.StCProLogisticStrategyQueryCmd;
import com.jackrain.nea.st.api.StCShopLogisticStrategyQueryCmd;
import com.jackrain.nea.st.api.StCSplitReasonQueryCmd;
import com.jackrain.nea.st.api.StCUnfullcarCostItemQueryCmd;
import com.jackrain.nea.st.api.StCUnfullcarCostQueryCmd;
import com.jackrain.nea.st.api.StCVipcomJitxWarehouseCmd;
import com.jackrain.nea.st.api.StCWarehouseLogisticStrategyQueryCmd;
import com.jackrain.nea.st.api.StCWarehouseQueryCmd;
import com.jackrain.nea.st.api.StrategyCenterCmd;
import com.jackrain.nea.st.api.SyncStockStrategyQueryServiceCmd;
import com.jackrain.nea.st.api.VipcomCooperationNoCmd;
import com.jackrain.nea.st.api.VipcomMailFindSenderCmd;
import com.jackrain.nea.st.api.WarehouseLogisticsQueryCmd;
import com.jackrain.nea.st.api.cycle.StCCyclePurchaseStrategyCmd;
import com.jackrain.nea.st.model.common.StCConstants;
import com.jackrain.nea.st.model.request.CompensateQueryRequest;
import com.jackrain.nea.st.model.request.ExpressAreaRequest;
import com.jackrain.nea.st.model.request.SellOwngoodsQueryRequest;
import com.jackrain.nea.st.model.request.StCAllocationCostQueryRequest;
import com.jackrain.nea.st.model.request.StCBansAreaStrategyRequest;
import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.st.model.request.StCExpressPriceStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StCFullcarCostQueryRequest;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.st.model.request.StCProLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StCShopLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.st.model.request.StCUnfullcarCostQueryRequest;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyItemQueryRequest;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.request.StDetentionPolicyRequest;
import com.jackrain.nea.st.model.request.WarehouseLogisticsRankRequest;
import com.jackrain.nea.st.model.request.cycle.StCCyclePurchaseStrategyQueryRequest;
import com.jackrain.nea.st.model.result.MailInfoResult;
import com.jackrain.nea.st.model.result.MergeOrderStrategyResult;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyDocResult;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCAllocationCostQueryResult;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.result.StCBansAreaStrategyResult;
import com.jackrain.nea.st.model.result.StCExpressPriceStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCFullcarCostQueryResult;
import com.jackrain.nea.st.model.result.StCLiveCastStrategyAllResult;
import com.jackrain.nea.st.model.result.StCPreorderModelStrategyResult;
import com.jackrain.nea.st.model.result.StCPriceResult;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.result.StCUnfullcarCostQueryResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyQueryResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyResult;
import com.jackrain.nea.st.model.result.StCWarehouseQueryResult;
import com.jackrain.nea.st.model.result.SyncStockStrategyQueryResult;
import com.jackrain.nea.st.model.result.cycle.StCCyclePurchaseStrategyResult;
import com.jackrain.nea.st.model.table.StAddedServiceTypeDocDO;
import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.st.model.table.StCAutoInvoiceDO;
import com.jackrain.nea.st.model.table.StCAutocheckAuditMarkDO;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import com.jackrain.nea.st.model.table.StCBnWarehouseLogisticsConfigDO;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.model.table.StCExpressAllocationItemDO;
import com.jackrain.nea.st.model.table.StCExpressDO;
import com.jackrain.nea.st.model.table.StCExpressPackageDO;
import com.jackrain.nea.st.model.table.StCExpressPlanAreaItemDO;
import com.jackrain.nea.st.model.table.StCExpressProItemDO;
import com.jackrain.nea.st.model.table.StCExpressWarehouseItemDO;
import com.jackrain.nea.st.model.table.StCKeywordsInterceptStrategy;
import com.jackrain.nea.st.model.table.StCMatchAbnormalStrategy;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.st.model.table.StCOperationcostDO;
import com.jackrain.nea.st.model.table.StCOrderPriceItemDO;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import com.jackrain.nea.st.model.table.StCPostfeeDO;
import com.jackrain.nea.st.model.table.StCPostfeeItemDO;
import com.jackrain.nea.st.model.table.StCPostfeeWarehouseDO;
import com.jackrain.nea.st.model.table.StCPreOccupyProvincePriority;
import com.jackrain.nea.st.model.table.StCPreOccupyWarehousePriority;
import com.jackrain.nea.st.model.table.StCPreorderFieldStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderItemStrategyDO;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.st.model.table.StCScalpingDO;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.model.table.StCSendPlanItemDO;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRankDO;
import com.jackrain.nea.st.model.table.StCSendRuleAddressVipDo;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyItemDO;
import com.jackrain.nea.st.model.table.StCVipcomCooperationNo;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.st.model.table.StcMsgDO;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import com.jackrain.nea.st.request.StCUnfullcarCostArrivalDaysRequest;
import com.jackrain.nea.st.result.StCUnfullcarCostArrivalDaysResponse;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.st.service.StRedisKey;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 易邵峰
 * @since: 2019-03-22
 * create at : 2019-03-22 11:54
 */
@Component
@Slf4j
public class StRpcService {

    // redis超时时间：5分钟
    static final long REDIS_TIMEOUT = 1_000L * 60 * 10;
    @Reference(group = "st", version = "1.0")
    private ShopStrategyQueryServiceCmd shopStrategyQueryServiceCmd;
    @Reference(group = "st", version = "1.0")
    private StrategyCenterCmd strategyCenterCmd;
    @Reference(group = "st", version = "1.0")
    private SellOwngoodsQueryCmd sellOwngoodsQueryCmd;
    @Reference(group = "oms-fi", version = "1.0")
    private StCHoldOrderQueryCmd stCHoldOrderQueryCmd;
    @Reference(group = "st", version = "1.0")
    private AuditMarkQueryCmd auditMarkQueryCmd;
    @Reference(group = "st", version = "1.0")
    private StCOrderPushDelayStrategyQueryCmd stCOrderPushDelayStrategyQueryCmd;
    @Reference(group = "st", version = "1.0")
    private SplitOrderReasonCmd splitOrderReasonCmd;
    @Reference(group = "st", version = "1.0")
    private ExpressQueryServiceCmd expressQueryServiceCmd;
    @Reference(group = "st", version = "1.0")
    private StCWarehouseQueryCmd stCWarehouseQueryCmd;
    @Reference(group = "st", version = "1.0")
    private OtherFeesQueryServiceCmd otherFeesQueryServiceCmd;

    @Reference(group = "st", version = "1.0")
    private WarehouseLogisticsQueryCmd warehouseLogisticsQueryCmd;

    @Reference(group = "st", version = "1.0")
    private SendPlanRuleQueryServiceCmd sendPlanRuleQueryServiceCmd;

    @Reference(group = "st", version = "1.0")
    private ExpressAllocationCheckCmd expressAllocationCheckCmd;

    @Reference(group = "st", version = "1.0")
    private SyncStockStrategyQueryServiceCmd syncStockStrategyQueryServiceCmd;

    @Reference(group = "st", version = "1.0")
    private OrderUrgentStrategyCmd orderUrgentStrategyCmd;

    @Reference(group = "st", version = "1.0")
    private StCVipcomJitxWarehouseCmd jitxWarehouseCmd;

    @Reference(group = "st", version = "1.0")
    private VipcomMailFindSenderCmd vipMail;

    @Reference(group = "st", version = "1.0")
    private StCMsgStrategyQueryCmd stCMsgStrategyQueryCmd;

    @Reference(group = "st", version = "1.0")
    private ShopStrategyDefaultStoreQueryCmd shopStrategyDefaultStoreQueryCmd;

    @Reference(group = "st", version = "1.0")
    private VipcomCooperationNoCmd vipcomCooperationNoCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCDepositPreSaleSinkQueryCmd stCDepositPreSaleSinkQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCOrderLabelQueryCmd stCOrderLabelQueryCmd;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;

    @Reference(group = "st", version = "1.0")
    private CompensateQueryCmd compensateQueryCmd;


    @Reference(group = "st", version = "1.0")
    private StCShopLogisticStrategyQueryCmd shopExpressQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCWarehouseLogisticStrategyQueryCmd warehouseExpressQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StBansAreaStrategyQueryCmd bansExpressQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCExpressPriceStrategyQueryCmd stCExpressPriceStrategyQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCProLogisticStrategyQueryCmd stCProLogisticStrategyQueryCmd;

    @Reference(group = "st", version = "1.0")
    JdDistributionLogisticsCmd jdDistributionLogisticsCmd;

    @Reference(group = "st", version = "1.0")
    private StCAllocationStorageCostQueryCmd stCAllocationStorageCostQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StAddedServiceStrategyQueryCmd addedServiceStrategyQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCUnfullcarCostItemQueryCmd stCUnfullcarCostItemQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StAddedServiceTypeDocQueryCmd addedServiceTypeDocQueryCmd;

    public ValueHolderV14<BigDecimal> getCompensate(CompensateQueryRequest compensateQueryRequest) {
        return compensateQueryCmd.selectByAcPayable(compensateQueryRequest);
    }

    @Reference(group = "st", version = "1.0")
    private StCSplitReasonQueryCmd stCSplitReasonQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCDetentionPolicyQueryCmd stCDetentionPolicyQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCWarehouseLogisticStrategyQueryCmd warehouseLogisticStrategyQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCUnfullcarCostQueryCmd unfullcarCostQueryCmd;

    @Reference(group = "st", version = "1.0")
    private StCFullcarCostQueryCmd fullcarCostQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCCyclePurchaseStrategyCmd cyclePurchaseStrategyCmd;

    @Reference(group = "st", version = "1.0")
    private StCMatchAbnormalStrategyMatchCmd stCMatchAbnormalStrategyMatchCmd;

    @Reference(group = "st", version = "1.0")
    private StCPreorderModelStrategyCmd modelStrategyCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCAllocationCostQueryCmd cAllocationCostQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StBnQueryCmd bnQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCPreOccupyWarehousePriorityQueryCmd warehousePriorityQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCPreOccupyProvincePriorityQueryCmd provincePriorityQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCCustomLabelQueryCmd customLabelQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private StCKeywordsInterceptStrategyQueryCmd stCKeywordsInterceptStrategyQueryCmd;


    public ValueHolderV14<List<LabelingRequirementsQueryResult>> queryAddedServiceTypeDoc() {
        List<LabelingRequirementsQueryResult> queryResultList = new ArrayList<>();
        ValueHolderV14<List<LabelingRequirementsQueryResult>> resultValueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                "SUCCESS");
        try {
            List<StAddedServiceTypeDocDO> list = (List<StAddedServiceTypeDocDO>) addedServiceTypeDocQueryCmd.execute(new QuerySessionImpl(SystemUserResource.getRootUser())).getData();
            if (CollectionUtils.isNotEmpty(list)) {
                for (StAddedServiceTypeDocDO stAddedServiceTypeDocDO : list) {
                    LabelingRequirementsQueryResult queryResult = new LabelingRequirementsQueryResult();
                    queryResult.setAddedTypeCode(stAddedServiceTypeDocDO.getAddedTypeCode());
                    queryResult.setAddedTypeName(stAddedServiceTypeDocDO.getAddedTypeName());
                    queryResultList.add(queryResult);
                }
            }
        } catch (Exception e) {
            log.error(" 查询增值服务,异常:{}", Throwables.getStackTraceAsString(e));
        }
        return resultValueHolderV14;
    }


    public ValueHolderV14<StCFullcarCostQueryResult> queryFullcarCost(StCFullcarCostQueryRequest request) {
        log.info(LogUtil.format("StRpcService.queryFullcarCost request:{}", "查找整车报价入参", JSONObject.toJSONString(request)));
        ValueHolderV14<StCFullcarCostQueryResult> resultValueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                "SUCCESS");
        try {
            resultValueHolderV14 = fullcarCostQueryCmd.queryFullcarCost(request);
            log.info(LogUtil.format("StRpcService.queryFullcarCost resultValueHolderV14:{}", "查找整车报价出参", JSONObject.toJSONString(resultValueHolderV14)));
        } catch (Exception e) {
            log.error(" 查找零担报价设置,异常:{}", Throwables.getStackTraceAsString(e));
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage(e.getMessage());
        }
        return resultValueHolderV14;
    }

    /**
     * 根据物流公司 实体仓 查找零担报价设置
     */
    public ValueHolderV14<StCUnfullcarCostQueryResult> queryUnfullcarCost(StCUnfullcarCostQueryRequest request) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StRpcService.queryUnfullcarCost request:{}", "查找零担报价入参",
                    JSONObject.toJSONString(request)));
        }

        ValueHolderV14<StCUnfullcarCostQueryResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                "SUCCESS");
        try {

            valueHolderV14 = unfullcarCostQueryCmd.queryUnfullcarCost(request);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("StRpcService.queryUnfullcarCost result:{}", "查找零担报价出参",
                        JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {
            log.error(" 查找零担报价设置,异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;
    }

    /**
     * 根据仓库物流 实体仓 查找仓库物流信息 （大货物流明细）
     */
    public ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> queryLogisticStrategyByWarehouseId(StCWarehouseLogisticStrategyQueryRequest request) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StRpcService.queryLogisticStrategyByWarehouseId request:{}", "查找仓库物流信息",
                    JSONObject.toJSONString(request)));
        }


        ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> valueHolderV14 =
                new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {

            valueHolderV14 = warehouseLogisticStrategyQueryCmd.queryLogisticStrategyByWarehouseId(request);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("StRpcService.queryLogisticStrategyByWarehouseId result:{}", "查找仓库物流信息",
                        JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {
            log.error(" 查找仓库物流信息,大货物流明细,异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;

    }

    /**
     * 统计商品价格策略明细是否存在
     *
     * @param stCPriceList 价格list
     * @param proId        商品Id
     * @return int
     */
    public List<BigDecimal> queryPriceTotal(List<Long> stCPriceList, Long proId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryPriceTotal入参,价格方案集合:{}, ", "queryPriceTotal", proId), stCPriceList);
        }
        List<BigDecimal> priceTotalList = shopStrategyQueryServiceCmd.queryPriceTotal(stCPriceList, proId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryPriceTotal出参:{}", "queryPriceTotal", proId), priceTotalList);
        }
        return priceTotalList;
    }

    /**
     * 判断“物流公司”是否在订单中“发货仓库”对应的【仓库物流规则】中存在且启用
     *
     * @param cpClogisticsId    物流公司Id
     * @param cpCphyWarehouseId 发货仓库Id
     * @return int
     */
    public int queryLogisticsRule(Long cpClogisticsId, Long cpCphyWarehouseId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryLogisticsRule入参", cpCphyWarehouseId, cpClogisticsId));
        }
        String redisKey = OmsRedisKeyResources.bulidLockStQueryLogisticsRuleKey();
        String redisHashKeyStr = "";
        if (null != cpCphyWarehouseId && null != cpClogisticsId) {
            redisHashKeyStr = redisHashKeyStr + "_" + cpCphyWarehouseId + "_" + cpClogisticsId;
        } else {
            return -1;
        }

        //判断key是否存在
        CusRedisTemplate<String, Integer> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        Boolean isExist = redisTemplate.opsForHash().hasKey(redisKey, redisHashKeyStr);
        int result;
        if (!isExist) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryLogisticsRule仓库物流规则入参", cpCphyWarehouseId, cpClogisticsId));
            }
            result = expressQueryServiceCmd.queryLogisticsRule(cpClogisticsId, cpCphyWarehouseId);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryLogisticsRule仓库物流规则出参:{}"), JSON.toJSONString(result));
            }
            redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, result);
        } else {
            redisTemplate = RedisOpsUtil.getObjRedisTemplate();
            Object object = redisTemplate.opsForHash().get(redisKey, redisHashKeyStr);
            if (object != null) {
                return (Integer) object;
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("queryLogisticsRule仓库物流规则入参", cpCphyWarehouseId, cpClogisticsId));
                }
                result = expressQueryServiceCmd.queryLogisticsRule(cpClogisticsId, cpCphyWarehouseId);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("queryLogisticsRule仓库物流规则出参:{}"), JSON.toJSONString(result));
                }
            }
        }
        return result;
    }


    /**
     * 仓库限制数量
     *
     * @param cpCphyWarehouseId 实体仓库Id
     * @param cpCogisticsId     物流公司Id
     * @return Long
     */
    public Long queryPriceList(Long cpCphyWarehouseId, Long cpCogisticsId) {
        String redisKey = OmsRedisKeyResources.bulidLockStWarehouseQueryPriceListKey();
        String redisHashKeyStr = "";
        if (null != cpCphyWarehouseId && null != cpCogisticsId) {
            redisHashKeyStr = cpCphyWarehouseId + "_" + cpCogisticsId;
        } else {
            return null;
        }
        //判断key是否存在
        CusRedisTemplate<String, Long> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        Boolean isExist = redisTemplate.opsForHash().hasKey(redisKey, redisHashKeyStr);
        Long priceList;
        if (!isExist) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("仓库限制数量入参", "queryPriceList", cpCphyWarehouseId, cpCogisticsId));
            }
            priceList = expressQueryServiceCmd.countProLimitNumber(cpCphyWarehouseId, cpCogisticsId);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("仓库限制数量出参:{}", "queryPriceList", cpCphyWarehouseId, cpCogisticsId), priceList);
            }
            redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, priceList);
        } else {
            redisTemplate = RedisOpsUtil.getObjRedisTemplate();
            Object object = redisTemplate.opsForHash().get(redisKey, redisHashKeyStr);
            if (object != null) {
                Long limitQty = (Long) object;
                return limitQty;
            } else {
                priceList = expressQueryServiceCmd.countProLimitNumber(cpCphyWarehouseId, cpCogisticsId);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("仓库限制数量出参:{}", "queryPriceList", cpCphyWarehouseId, cpCogisticsId),
                            priceList);
                }
                redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, priceList);
            }
        }
        return priceList;
    }

    /**
     * 查询所有可以进行合并订单的店铺
     * Ljp add
     *
     * @return
     */
    public List<StCMergeOrderDO> queryAllMergeShop() {

        List<StCMergeOrderDO> mergeOrderList = null;
        String redisKey = OmsRedisKeyResources.buildAutoMergeAllListRedisKey();
        try {
            if (RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey)) {
                String mergeOrderJson = (String) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
                if (StringUtils.isNoneBlank(mergeOrderJson)) {
                    List<StCMergeOrderDO> stCMergeOrderDOS = JSON.parseArray(mergeOrderJson, StCMergeOrderDO.class);
                    mergeOrderList = stCMergeOrderDOS;

                    return mergeOrderList;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询所有店铺合并策略异常:{}"), Throwables.getStackTraceAsString(e));
        }

        try {
            mergeOrderList = shopStrategyQueryServiceCmd.queryAllMergeOrder();

            if (CollectionUtils.isNotEmpty(mergeOrderList)) {
                try {
                    //存放在redis中
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(mergeOrderList));
                } catch (Exception e) {
                    throw new NDSException("redis保存所有自动合单策略异常," + e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("st查询所有可以进行合并订单的店铺异常:{}"), Throwables.getStackTraceAsString(e));
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("st查询所有可以进行合并订单的店铺返回结果:{}"), JSON.toJSONString(mergeOrderList));
        }
        return mergeOrderList;
    }

    /**
     * description：斯凯奇项目合单策略-新增品类限制需求 增加品类限制明细查询返回
     *
     * <AUTHOR>
     * @date 2021/5/13
     */
    public List<MergeOrderStrategyResult> queryAllMergeShopInfo() {

        List<MergeOrderStrategyResult> mergeOrderList = null;

        String redisKey = OmsRedisKeyResources.buildNewAutoMergeAllListRedisKey();
        try {
            if (RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey)) {
                String mergeOrderJson = (String) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
                if (StringUtils.isNoneBlank(mergeOrderJson)) {
                    List<MergeOrderStrategyResult> stCMergeOrderDOS = JSON.parseArray(mergeOrderJson,
                            MergeOrderStrategyResult.class);
                    mergeOrderList = stCMergeOrderDOS;

                    return mergeOrderList;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis查询所有店铺合并策略异常:{}"), Throwables.getStackTraceAsString(e));
        }

        try {
            mergeOrderList = shopStrategyQueryServiceCmd.queryAllMergeOrderInfo();

            if (CollectionUtils.isNotEmpty(mergeOrderList)) {
                try {
                    //存放在redis中
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(mergeOrderList));
                } catch (Exception e) {
                    throw new NDSException("redis保存所有自动合单策略异常," + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("st查询所有可以进行合并订单的店铺异常:{}"), Throwables.getStackTraceAsString(e));
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("st查询所有可以进行合并订单的店铺返回结果:{}"), JSON.toJSONString(mergeOrderList));
        }
        return mergeOrderList;
    }

    /**
     * 获取店铺策略信息
     *
     * @return
     */
    public StCShopStrategyDO selectOcStCShopStrategyByCpCshopId(Long id) {
        return shopStrategyQueryServiceCmd.selectOcStCShopStrategyByCpCshopId(id);
    }

    /**
     * 查找订单下的店铺刷单策略，并且在合法时间内的策略
     *
     * @param orderId 订单Id
     * @param shopId  店铺Id
     * @param keyWord 买家留言
     * @param payTime 付款时间
     * @return ValueHolderV14
     */
    public ValueHolderV14<StCScalpingDO> queryScalPingShop(Long orderId, Long shopId, String keyWord, Date payTime) {

        ValueHolderV14<StCScalpingDO> valueHolderV14 = new ValueHolderV14<>();
        try {
            String redisKey = OmsRedisKeyResources.buildScalpingRedisKey(shopId, keyWord);
            CusRedisTemplate<String, StCScalpingDO> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            StCScalpingDO scalpingInfo = objRedisTemplate.opsForValue().get(redisKey);
            PrintLogUtils.printDeBugLog("订单OrderId:{},判断查询订单刷单策略key是否存在:{};", orderId,
                    objRedisTemplate.hasKey(redisKey));
            StCScalpingDO finalScalpingInfo = null;
            if (scalpingInfo != null) {
                finalScalpingInfo = scalpingInfo;
            } else {
                finalScalpingInfo = strategyCenterCmd.queryScalpingList(shopId, keyWord, payTime);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, finalScalpingInfo);
            }
            PrintLogUtils.printDeBugLog("订单OrderId:{},的订单入参shopId:{},出参:{};", orderId, shopId, finalScalpingInfo);
            if (finalScalpingInfo != null) {
                if (finalScalpingInfo.getBeginTime() != null && finalScalpingInfo.getEndTime() != null) {
                    //只有在满足条件的时候才是刷单订单
                    if ((finalScalpingInfo.getBeginTime().before(payTime) && finalScalpingInfo.getEndTime().after(payTime))) {
                        valueHolderV14.setCode(ResultCode.SUCCESS);
                        valueHolderV14.setData(finalScalpingInfo);
                        valueHolderV14.setMessage("订单OrderId" + orderId + "该订单为刷单订单");
                        return valueHolderV14;
                    } else {
                        valueHolderV14.setCode(ResultCode.SUCCESS);
                        valueHolderV14.setData(null);
                        valueHolderV14.setMessage("订单OrderId" + orderId + "该订单非刷单订单");
                        return valueHolderV14;
                    }
                } else {
                    valueHolderV14.setCode(ResultCode.SUCCESS);
                    valueHolderV14.setData(null);
                    valueHolderV14.setMessage("订单OrderId" + orderId + "该订单非刷单订单");
                    return valueHolderV14;
                }
            } else {
                valueHolderV14.setCode(ResultCode.SUCCESS);
                valueHolderV14.setData(null);
                valueHolderV14.setMessage("订单OrderId" + orderId + "该订单非刷单订单");
                return valueHolderV14;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查找是否为刷单订单接口异常:{}", orderId), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            return valueHolderV14;
        }
    }

    /**
     * 查询所有店铺审核策略
     *
     * @return
     */
    public List<StCAutoCheckResult> queryAutoCheckAllList() {

        List<StCAutoCheckResult> autoCheckList = null;
        String redisKey = OmsRedisKeyResources.buildAutoCheckAllListRedisKey();
        try {
            //先从redis获取
            if (RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey)) {
                String autoCheckJson = (String) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
                if (StringUtils.isNotBlank(autoCheckJson)) {
                    autoCheckList = JSON.parseArray(autoCheckJson, StCAutoCheckResult.class);

                    return autoCheckList;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis查询所有自动审单策略异常:{}"), Throwables.getStackTraceAsString(e));
        }

        try {
            ValueHolderV14<List<StCAutoCheckResult>> valueHolderV14 = strategyCenterCmd.queryAUtoCheckAllListAndItem();
            if (valueHolderV14.isOK()) {
                RuntimeCompute runtimeCompute = new RuntimeCompute();
                runtimeCompute.startRuntime();
                autoCheckList = valueHolderV14.getData();

                if (CollectionUtils.isNotEmpty(autoCheckList)) {
                    try {
                        RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey,
                                JSON.toJSONString(autoCheckList));
                    } catch (Exception e) {
                        log.error(LogUtil.format("redis保存所有自动审单策略异常:{}"), Throwables.getStackTraceAsString(e));
                    }
                }
            }
        } catch (NDSException e) {
            log.error(LogUtil.format("查询所有店铺审核策略异常:{}"), Throwables.getStackTraceAsString(e));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryAutoCheckAllList. 出参={}"), JSON.toJSONString(autoCheckList));
        }
        return autoCheckList;
    }

    /**
     * 查询所有店铺开票策略
     *
     * @return
     */
    public List<StCAutoInvoiceDO> queryAllAutoInvoice() {
        List<StCAutoInvoiceDO> autoInvoiceList;
        try {
            autoInvoiceList = shopStrategyQueryServiceCmd.queryAllAutoInvoice();
        } catch (NDSException e) {
            return null;
        }
        return autoInvoiceList;
    }

    /**
     * 经销商自有商品策略服务
     * ming.fz add
     *
     * @param shopId      店铺id
     * @param paymentDate 付款时间
     * @param skuIds      商品id
     * @return
     */
    public ValueHolderV14<List<Long>> voidSgPhyOutNotices(Long shopId, Date paymentDate, List<Long> skuIds) {
        ValueHolderV14<List<Long>> result = new ValueHolderV14();

        SellOwngoodsQueryRequest request = new SellOwngoodsQueryRequest();
        request.setCpCShopId(shopId);
        request.setEffectiveDate(paymentDate);
        request.setSkuIdList(skuIds);
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("经销商自有商品策略服务接口入参：{}"), JSON.toJSONString(request));
            }
            result = sellOwngoodsQueryCmd.selectSkuBySellOwngoods(request);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("经销商自有商品策略服务返回结果：{}"), JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用经销商自有商品服务异常信息:{}"), Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("调用经销商自有商品服务sellOwngoodsQueryCmd.selectSkuBySellOwngoods:异常信息！" + e.getMessage());
        }
        return result;
    }


    /**
     * 判断是否有开票策略开发票策略
     *
     * @param shopId
     * @param invoiceNode
     */
    public Boolean queryAutoInvoiceInfo(Long shopId, Integer invoiceNode) {
        Boolean result = Boolean.FALSE;
        StCAutoInvoiceDO autoInvoiceInfo = shopStrategyQueryServiceCmd.queryStCAutoInvoice(shopId);
        if (autoInvoiceInfo != null && null != invoiceNode && invoiceNode.equals(autoInvoiceInfo.getInvoiceNode()) && null != autoInvoiceInfo.getIsAutoInvoiceNotice() && "Y".equals(autoInvoiceInfo.getIsAutoInvoiceNotice())) {
            result = Boolean.TRUE;
        }
        return result;
    }

    /**
     * 查直播解析策略
     *
     * @param cpCShopId
     * @param orderDate
     * @param payTime
     * @return
     */
    public List<StCLiveCastStrategyAllResult> queryLiveCastStrategy(Long cpCShopId, Date orderDate, Date payTime) {
        // 组key
        String redisKey = StRedisKeyResources.buildLiveStrategyRedisKey(cpCShopId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查直播解析策略:{}/{}", cpCShopId, redisKey), cpCShopId, redisKey);
        }
        List<StCLiveCastStrategyAllResult> results = null;

        CusRedisTemplate<Object, Object> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        // 先从redis里取
        Boolean hasKey = redisTemplate.hasKey(redisKey);
        if (Objects.nonNull(hasKey) && hasKey) {
            String redisResult = (String) redisTemplate.opsForValue().get(redisKey);
            if (Objects.nonNull(redisResult)) {
                results = JSON.parseArray(redisResult, StCLiveCastStrategyAllResult.class);
                log.info(LogUtil.format("dataFromRedis:{},{}", cpCShopId), cpCShopId, JSON.toJSONString(results));
            }
        } else {
            // 如果取不到，则到ST服务查询
            ValueHolderV14<List<StCLiveCastStrategyAllResult>> vhResult =
                    strategyCenterCmd.queryLiveCastStrategy(cpCShopId, orderDate, payTime);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryLiveCastStrategy,vhResult={},#cpCShopId=", cpCShopId),
                        JSON.toJSONString(vhResult));
            }

            if (Objects.nonNull(vhResult) && vhResult.isOK()) {
                results = vhResult.getData();
                // 存redis
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(results),
                        StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
            }
        }
        return results;
    }

    /**
     * @param shopId
     * @return com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy
     * <AUTHOR>
     * @Description st查询开启的订单推单延时策略
     * @Date 14:10 2020/9/17
     **/
    public StCOrderPushDelayStrategy queryOrderPushDelayStrategy(Long shopId) {
        StCOrderPushDelayStrategy strategy;
        String key = StRedisKeyResources.buildShopOrderPushDelayKey(shopId);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            strategy = (StCOrderPushDelayStrategy) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            return strategy;
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        try {
            strategy = stCOrderPushDelayStrategyQueryCmd.queryOrderPushDelayStrategy(shopId);
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, strategy, cacheTime, TimeUnit.HOURS);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("st查询所有开启的订单推单延时策略返回结果:", shopId) + JSONObject.toJSONString(strategy));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("st查询开启的订单推单延时策略异常:{}"), Throwables.getStackTraceAsString(e));
            return null;
        }
        return strategy;
    }

    /**
     * @param ecode
     * @return StCVipcomCooperationNo
     * <AUTHOR>
     * @Description st查询开启的订单推单延时策略
     * @Date 20:10 2021/7/7
     **/
    public StCVipcomCooperationNo queryCooperationNoInfoByEcode(String ecode) {
        StCVipcomCooperationNo stCVipcomCooperationNo;
        String key = StRedisKeyResources.buildShopOrderCooperationNoKey(ecode);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            stCVipcomCooperationNo = (StCVipcomCooperationNo) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            return stCVipcomCooperationNo;
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        try {
            stCVipcomCooperationNo = vipcomCooperationNoCmd.queryCooperationNoInfo(ecode);
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, stCVipcomCooperationNo, cacheTime,
                    TimeUnit.HOURS);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("根据常态合作编码，查询常态合作编码信息返回结果:", ecode) + JSONObject.toJSONString(stCVipcomCooperationNo));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("根据常态合作编码，查询常态合作编码信息异常:{}", ecode), Throwables.getStackTraceAsString(e));
            return null;
        }
        return stCVipcomCooperationNo;
    }

    /**
     * 根据店铺ID查询有效的Hold策略
     *
     * @param shopId
     * @return
     */
    public List<StCHoldOrderRequest> queryStCHoldOrderByShopId(Long shopId) {
        String key = StRedisKeyResources.buildShopHoldOrderStKey(shopId);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            List<StCHoldOrderRequest> result =
                    (List<StCHoldOrderRequest>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            return result;
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        List<StCHoldOrderRequest> result = stCHoldOrderQueryCmd.queryStCHoldOrderByShopId(shopId);
        if (CollectionUtils.isNotEmpty(result)) {
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, result, cacheTime, TimeUnit.HOURS);
        }
        return result;
    }

    /**
     * 根据订单自动审核策略ID获取审核等待时间列表
     *
     * @param sTAutocheckId 订单策略ID
     * @return List<StCAutocheckAuditMarkDO>
     */
    public List<StCAutocheckAuditMarkDO> queryAuditMarkListById(Long sTAutocheckId) {
        List<StCAutocheckAuditMarkDO> stAuditMarkList = new ArrayList<>();
        try {
            stAuditMarkList = auditMarkQueryCmd.selectCStAutditMarkItemInfo(sTAutocheckId);
        } catch (Exception e) {
            log.error(LogUtil.format("st查询订单审核策略对应的审核等待时间列表异常{}"), Throwables.getStackTraceAsString(e));
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("st查询订单审核策略对应的审核等待时间列表返回结果") + JSONObject.toJSONString(stAuditMarkList));
        }
        return stAuditMarkList;
    }

    /**
     * 查询所有可以进行换货订单的店铺
     *
     * @return
     */
    public List<StCExchangeStrategyOrderDO> queryAllExchangeShop() {
        List<StCExchangeStrategyOrderDO> list;
        String redisKey = OmsRedisKeyResources.buildExchangeAllListRedisKey();
        try {
            if (redisUtil.strRedisTemplate.hasKey(redisKey)) {
                String autoCheckJson = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
                list = JSON.parseArray(autoCheckJson, StCExchangeStrategyOrderDO.class);
                return list;
            }
            list = shopStrategyQueryServiceCmd.queryAllExchangeShopStrategy();
            redisUtil.strRedisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(list));
        } catch (Exception e) {
            log.error(LogUtil.format(" st查询所有可以进行换货订单的店铺异常{}"), Throwables.getStackTraceAsString(e));
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" st查询所有可以进行换货订单的店铺返回结果") + JSONObject.toJSONString(list));
        }
        return list;
    }

    /**
     * 根据店铺ID查询换货策略
     *
     * @param shopId
     * @return
     */
    public StCExchangeStrategyOrderDO queryExchangeStrategyByShopId(Long shopId) {
        List<StCExchangeStrategyOrderDO> list = this.queryAllExchangeShop();
        Map<Long, StCExchangeStrategyOrderDO> shopMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO : list) {
                shopMap.put(stCExchangeStrategyOrderDO.getCpCShopId(), stCExchangeStrategyOrderDO);
            }
        }
        return shopMap.get(shopId);
    }

    /**
     * 根据实体仓id获取 实体仓条码，商品。品牌。仓库信息
     *
     * @param cpCPhyWarehouseId 实体仓id
     * @return
     */
    public StCWarehouseQueryResult getStCWarehouseQueryResultByWareHouseId(Long cpCPhyWarehouseId) {
        String key = StRedisKeyResources.buildPhyWarehouseSplitOrderKey(cpCPhyWarehouseId);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            StCWarehouseQueryResult result =
                    (StCWarehouseQueryResult) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            return result;
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        StCWarehouseQueryResult result =
                stCWarehouseQueryCmd.getStCWarehouseQueryResultByWareHouseId(cpCPhyWarehouseId);
        RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, result, cacheTime, TimeUnit.HOURS);
        return result;
    }

    /**
     * 根据日期查找在有效期内的运费方案
     *
     * @param date
     * @return
     */
    public List<StCPostfeeDO> searchEffectiveFreightPlan(Date date) {
        return otherFeesQueryServiceCmd.searchEffectiveFreightPlan(date);
    }


    /**
     * @param stCPostfeeId 运费方案id
     * @param logisticsId  物流id
     * @param provinceId   省id
     * @param cityIdTmp    市id
     * @param areaIdTmp    区id
     * @return
     */
    public List<StCPostfeeItemDO> searchStCPostfeeItem(Long stCPostfeeId, Long logisticsId, Long provinceId,
                                                       Long cityIdTmp, Long areaIdTmp) {
        return otherFeesQueryServiceCmd.searchStCPostfeeItem(stCPostfeeId, logisticsId, provinceId, cityIdTmp,
                areaIdTmp);
    }


    /**
     * 根据邮费策略id获取仓库信息
     *
     * @param stCPostfeeId
     * @return
     */
    public List<StCPostfeeWarehouseDO> searchStCPostfeeWarehouse(Long stCPostfeeId) {
        return otherFeesQueryServiceCmd.searchStCPostfeeWarehouse(stCPostfeeId);
    }

    /**
     * 根据
     *
     * @param expressId
     * @return
     */
    public List<StCExpressPackageDO> selectStCExpressPackageInfo(Long expressId) {
        return expressQueryServiceCmd.selectStCExpressPackageInfo(expressId);
    }

    /**
     * 根据省市区获取物流区域信息
     *
     * @param provinceId
     * @param cityId
     * @param areaId
     * @return
     */
    public List<ExpressAreaRequest> selectLogisticsIdInfoByProvinceCityArea(Long provinceId, Long cityId, Long areaId) {
        return expressQueryServiceCmd.selectLogisticsIdInfoByProvinceCityArea(provinceId, cityId, areaId);
    }


    /**
     * 根据时间区间获取物流区域信息
     *
     * @param orderDate
     * @param currentDate
     * @return
     */
    public List<StCExpressDO> selectStCExpressByCpCShopId(Date orderDate, Date currentDate) {
        return expressQueryServiceCmd.selectStCExpressByCpCShopId(orderDate, currentDate);
    }

    /**
     * 根据实体仓id和数量获取仓库物流规则明细
     *
     * @param phyWarehouseId 实体仓id
     * @param qtyAll         包裹数量
     * @return
     */
    public List<StCExpressAllocationItemDO> selectLogisticsIdBycpCPhyWarehouseId(Long phyWarehouseId,
                                                                                 BigDecimal qtyAll) {
        return expressQueryServiceCmd.selectLogisticsIdBycpCPhyWarehouseId(phyWarehouseId, qtyAll);
    }

    /**
     * 根据物流id获取物流方案明细仓库信息
     *
     * @param expressId 物流id
     * @return
     */
    public List<StCExpressWarehouseItemDO> selectStCExpressWarehouseItemInfo(Long expressId) {
        return expressQueryServiceCmd.selectStCExpressWarehouseItemInfo(expressId);
    }


    /**
     * 根据物流id获取物流计划区域明细信息
     *
     * @param expressId 物流id
     * @return
     */
    public List<StCExpressPlanAreaItemDO> selectStCExpressPlanAreaItem(Long expressId) {
        return expressQueryServiceCmd.selectStCExpressPlanAreaItem(expressId);
    }


    /**
     * 根据物流方案id获取物流方案商品明细
     *
     * @param expressId
     * @return
     */
    public List<StCExpressProItemDO> selectStCExpressProItem(Long expressId) {
        return expressQueryServiceCmd.selectStCExpressProItem(expressId);
    }


    /**
     * 获取仓库物流优先级方案
     *
     * @param request
     */
    public ValueHolderV14<StCWarehouseLogisticsRankDO> queryLogisticsRankInfo(WarehouseLogisticsRankRequest request) {
        return warehouseLogisticsQueryCmd.queryLogisticsRankInfo(request);
    }

    /**
     * 查找派单方案ID集合
     *
     * @param shopId     店铺Id
     * @param expireDate 符合日期
     * @return List<Long>
     */
    public List<Long> selectPlanByShopId(Long shopId, Date expireDate) {
        try {
            String redisKey = OmsRedisKeyResources.bulidLockStSendPlanIdKey(shopId);
            List<Long> sendPlanIdList = (List<Long>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendPlanIdList)) {
                //按大促、活动、日常（大促>活动>日常）优先级和创建时间排序，查询方案
                sendPlanIdList = sendPlanRuleQueryServiceCmd.selectSendPlanList(shopId, expireDate);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, sendPlanIdList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("店铺Id：{}，派单方案：{}", shopId, sendPlanIdList));
            }
            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendPlanIdList)) {
                return null;
            }
            return sendPlanIdList;
        } catch (Exception e) {
            log.error(LogUtil.format("查找派单方案错误{},店铺Id：", shopId), Throwables.getStackTraceAsString(e));
        }
        return null;
    }


    /**
     * 查找派单方案的派单规则ID集合
     *
     * @param sendPlanId
     * @return
     */
    public List<Long> selectRuleById(Long sendPlanId) {
        try {
            String redisKey = OmsRedisKeyResources.bulidLockStSendPlanRuleIdKey(sendPlanId);
            List<Long> sendRuleIdList = (List<Long>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendRuleIdList)) {
                //优先级和创建时间排序，查询派单规则
                sendRuleIdList = sendPlanRuleQueryServiceCmd.selectSendPlanItemList(sendPlanId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, sendRuleIdList);
            }
            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendRuleIdList)) {
                return null;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("派单规则ID：{},派单方案ID：", sendPlanId), sendRuleIdList);
            }
            return sendRuleIdList;
        } catch (Exception e) {
            log.error(LogUtil.format("查找派单规则错误：{},派单方案ID：", sendPlanId), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 查找派单方案的派单规则ID集合
     *
     * @param sendPlanId
     * @return
     */
    public List<Long> selectSendPlanItemList(Long sendPlanId) {
        try {
            String redisKey = OmsRedisKeyResources.bulidLockStSendPlanRuleIdKey(sendPlanId);
            List<Long> sendRuleIdList = (List<Long>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendRuleIdList)) {
                //优先级和创建时间排序，查询派单规则
                sendRuleIdList = sendPlanRuleQueryServiceCmd.selectSendPlanItemList(sendPlanId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, sendRuleIdList);
            }
            if (org.apache.commons.collections.CollectionUtils.isEmpty(sendRuleIdList)) {
                return null;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("派单规则ID：{},派单方案ID：", sendPlanId), sendRuleIdList);
            }
            return sendRuleIdList;
        } catch (Exception e) {
            log.error(LogUtil.format("查找派单规则错误：{},派单方案ID：", sendPlanId), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 查找派单规则
     *
     * @param sendRuleId
     * @return
     */
    public StCSendRuleDO selectSendRuleBySendRuleId(Long sendRuleId) {
        try {
            String redisKey = OmsRedisKeyResources.bulidLockStSendRuleKey(sendRuleId);
            //存放在redis中
            StCSendRuleDO stCSendRuleDO =
                    (StCSendRuleDO) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
            if (null == stCSendRuleDO) {
                //优先级和创建时间排序，查询派单规则
                stCSendRuleDO = sendPlanRuleQueryServiceCmd.selectSendRuleType(sendRuleId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, stCSendRuleDO);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("派单规则ID：{}", stCSendRuleDO));
            }
            return stCSendRuleDO;
        } catch (Exception e) {
            log.error(LogUtil.format("查找派单规则错误：{},派单规则ID：", sendRuleId), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 查找唯品会规则
     *
     * @param sendRuleId
     * @param cpCVipcomWahouseId
     * @return
     */
    public List<StCSendRuleAddressVipDo> selectSendRuleVip(Long sendRuleId, Long cpCVipcomWahouseId) {
        try {
            String redisKey = OmsRedisKeyResources.bulidLockStCSendRuleAddressVipKey(sendRuleId, cpCVipcomWahouseId);
            List<StCSendRuleAddressVipDo> stCSendRuleAddressVipDoList =
                    (List<StCSendRuleAddressVipDo>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(stCSendRuleAddressVipDoList)) {
                stCSendRuleAddressVipDoList = sendPlanRuleQueryServiceCmd.selectSendRuleAddressVoList(sendRuleId,
                        cpCVipcomWahouseId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, stCSendRuleAddressVipDoList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询唯品会规则：{},派单规则sendRuleId/cpCVipcomWahouseId：", sendRuleId,
                        cpCVipcomWahouseId), stCSendRuleAddressVipDoList);
            }
            return stCSendRuleAddressVipDoList;
        } catch (Exception e) {
            log.error(LogUtil.format("查询唯品会规则错误：{}，派单规则sendRuleId/cpCVipcomWahouseId：", sendRuleId,
                    cpCVipcomWahouseId), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 根据实体仓id获取对应的物流信息
     *
     * @param phyId 实体仓id
     */
    public List<Long> getLogisticInfoByWarehouseId(Long phyId) {
        return expressAllocationCheckCmd.getLogisticInfoByWarehouseId(phyId);
    }

    /**
     * 判断界面录入的“实体仓库”是否为【店铺同步库存策略】中存在且启用
     *
     * @param storeIds
     * @return
     */
    public boolean checkByStoreIds(List<Long> storeIds) {
        return syncStockStrategyQueryServiceCmd.checkByStoreIds(storeIds);
    }

    /**
     * 获取全部的店铺策略
     *
     * @return
     */
    public List<StCShopStrategyDO> queryAllShopStrategy() {
        return shopStrategyQueryServiceCmd.queryAllShopStrategy();
    }

    /**
     * 根据策略id获取明细
     *
     * @param id
     * @return
     */
    public List<StCShopStrategyItemDO> queryShopStrategyItem(Long id) {
        return shopStrategyQueryServiceCmd.queryShopStrategyItem(id);
    }

    /**
     * 传入店铺和sku,判断该sku是否为虚拟条码
     *
     * @param shopId   店铺Id
     * @param skuEcode 条码Ecode
     * @return boolean
     */
    public boolean isDiffPriceSku(Long shopId, String skuEcode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("isDifferenPriceSku入参 店铺Id/明细sku：", shopId, skuEcode));
        }
        boolean reuslt = shopStrategyQueryServiceCmd.isDifferenPriceSku(shopId, skuEcode);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("isDifferenPriceSku出参:{}"), reuslt);
        }
        return reuslt;
    }

    /**
     * 根据店铺id查找库存同步策略
     *
     * @param shopId 店铺Id
     */
    public List<CpCOrgChannelItemEntity> querySyncStockStrategy(Long shopId) {
        List<CpCOrgChannelItemEntity> result = syncStockStrategyQueryServiceCmd.querySyncStockStrategy(shopId);
        return result;
    }

    /**
     * 获取全部的库存同步策略
     *
     * @return
     */
    public List<SyncStockStrategyQueryResult> queryAllSyncStockStrategy() {
        return syncStockStrategyQueryServiceCmd.queryAllSyncStockStrategy();
    }

    /**
     * 查询店铺同步库存策略，获取逻辑仓优先级
     *
     * @param cpCShopId
     * @return
     */
    public List<StCSyncStockStrategyVo> selectSyncStockStrategyByCpCShopId(Long cpCShopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("querySyncStockStrategyItemByID,店铺Id：", cpCShopId));
        }
        List<StCSyncStockStrategyVo> shopStoreList =
                syncStockStrategyQueryServiceCmd.querySyncStockStrategyItemByID(cpCShopId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("querySyncStockStrategyItemByID:") + shopStoreList);
        }
        return shopStoreList;
    }

    /**
     * 订单加急打标策略
     *
     * @param shopId   店铺id
     * @param vipLevel 会员等级
     * @return
     */
    public ValueHolderV14<StCOrderUrgentStrategyDO> selectOrderUrgentStrategy(Long shopId, Integer vipLevel) {
        return orderUrgentStrategyCmd.selectOrderUrgentStrategy(shopId, vipLevel);
    }

    /**
     * 查询实体仓存在于维护的操作费方案中实体仓中，并且当前日期在操作费方案有效期内的“操作方案”
     *
     * @param PlanType          订单所属操作费方案类型
     * @param cpCPhyWarehouseId 实体仓id
     * @return
     */
    public List<StCOperationcostDO> selectStCOperationcostInfo(int PlanType, Long cpCPhyWarehouseId) {
        return otherFeesQueryServiceCmd.selectStCOperationcostInfo(PlanType, cpCPhyWarehouseId);
    }

    /**
     * @param shopId               店铺ID
     * @param cpCPhyWarehouseId    实体仓ID
     * @param vipcomWarehouseEcode JITX仓库编码
     * @return
     */
    public StCVipcomJitxWarehouse queryJitxCapacity(Long shopId, Long cpCPhyWarehouseId, String vipcomWarehouseEcode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryJitxCapacity入参:店铺Id/实体仓Id/唯品会仓库编码：", shopId, cpCPhyWarehouseId,
                    vipcomWarehouseEcode));
        }
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseCmd.execute(shopId, cpCPhyWarehouseId,
                vipcomWarehouseEcode);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryJitxCapacity出参数：{} "), JSON.toJSONString(jitxWarehouse));
        }
        return jitxWarehouse;
    }

    public List<StCVipcomJitxWarehouse> queryVipWarehouseListByShopId(Long shopId) {
        log.info(LogUtil.format("StRpcService.queryVipWarehouseListByShopId shopId:{}",
                "StRpcService.queryVipWarehouseListByShopId"), shopId);
        List<StCVipcomJitxWarehouse> jitxWarehouseList = jitxWarehouseCmd.selectVipWarehouseListByShopId(shopId);
        log.info(LogUtil.format("StRpcService.queryVipWarehouseListByShopId jitxWarehouseList:{}",
                "StRpcService.queryVipWarehouseListByShopId"), JSON.toJSONString(jitxWarehouseList));
        return jitxWarehouseList;
    }

    public List<StCVipcomJitxWarehouse> queryByShopIdAndJitxUnShopWarehouseEcode(Long shopId, String jitxUnShopWarehouseEcode) {
        StCVipcomJitxWarehouse jitxWarehouse = new StCVipcomJitxWarehouse();
        jitxWarehouse.setCpCShopId(shopId);
        jitxWarehouse.setVipcomUnshopWarehouseEcode(jitxUnShopWarehouseEcode);
        return jitxWarehouseCmd.selectByShopIdAndVipcomUnshopWarehouseEcode(shopId, jitxUnShopWarehouseEcode);
    }

    public StCVipcomJitxWarehouse queryVipcomWarehouse(Long shopId, String storeCode, String wareCode) {
        if (log.isDebugEnabled()) {
            log.debug("queryJitxCapacity入参:店铺Id:{},门店编码:{}，非门店编码：{} ", shopId, storeCode, wareCode);
        }
        StCVipcomJitxWarehouse request = new StCVipcomJitxWarehouse();
        request.setCpCShopId(shopId);
        request.setVipcomWarehouseEcode(storeCode);
        request.setVipcomUnshopWarehouseEcode(wareCode);
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseCmd.selectVipcomWarehouse(request);
        if (log.isDebugEnabled()) {
            log.debug("queryJitxCapacity出参数::{} ", JSON.toJSONString(jitxWarehouse));
        }
        return jitxWarehouse;
    }

    /**
     * @param jitxWarehouse   JITX仓库产能配置
     * @param calculationType 操作方式：加产能/减当日产能
     * @return StCVipcomJitxWarehouse 返回产能配置
     */
    public StCVipcomJitxWarehouse updateJitxCapacity(StCVipcomJitxWarehouse jitxWarehouse, Integer calculationType) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("updateJitxCapacity入参:{}"), JSONObject.toJSONString(jitxWarehouse));
        }
        ValueHolder valueHolder = jitxWarehouseCmd.updateJitxCapacity(jitxWarehouse, calculationType);
        return jitxWarehouse;
    }

    /**
     * 查询邮件模板
     *
     * @param shopId   店铺id
     * @param taskNode 任务节点
     * @return
     */
    public ValueHolderV14<MailInfoResult> querySenderByNodeAndShop(int taskNode, long shopId, Long var3) {
        return vipMail.querySenderByNodeAndShop(taskNode, shopId, var3);
    }

    /**
     * 根据店铺id查询 订单自动审核策略
     *
     * @param shopId
     * @return
     */
    public StCAutoCheckResult queryOcStCAutocheck(Long shopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryOcStCAutocheck入参:ShopId={}", shopId));
        }

        if (shopId == null || shopId == 0L) {
            return null;
        }

        StCAutoCheckResult autoCheckResult = null;

        try {
            if (redisUtil.strRedisTemplate.hasKey(OmsRedisKeyResources.bulidLockStCAutoCheckKey(shopId))) {
                String stCAutocheckDOJson =
                        redisUtil.strRedisTemplate.opsForValue().get(OmsRedisKeyResources.bulidLockStCAutoCheckKey(shopId));

                if (StringUtils.isNotBlank(stCAutocheckDOJson)) {
                    autoCheckResult = JSONObject.parseObject(stCAutocheckDOJson, StCAutoCheckResult.class);

                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("autoCheckResult:{}", autoCheckResult));
                    }

                    return autoCheckResult;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("redis根据店铺查询店铺自动审核策略异常,{}"), Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("redis中没有自动审核店铺策略,调用RPC,ShopId=", shopId));
        }

        try {
            autoCheckResult = shopStrategyQueryServiceCmd.queryOcStCAutoCheckWithItems(shopId);

            if (autoCheckResult != null) {
                try {
                    redisUtil.strRedisTemplate.opsForValue().set(OmsRedisKeyResources.bulidLockStCAutoCheckKey(shopId), JSON.toJSONString(autoCheckResult));
                } catch (Exception e) {
                    log.error(LogUtil.format("redis保存店铺自动审核策略异常{},ShopId={}", shopId),
                            Throwables.getStackTraceAsString(e));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("获取自动审核策略异常,{}"), Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryOcStCAutocheck入参:ShopId=", shopId),
                    JSONObject.toJSONString(autoCheckResult));
        }

        return autoCheckResult;
    }

    /**
     * 根据店铺查询订单退单自动审核策略
     *
     * @param shopId
     * @return boolean
     */
    public boolean isExitsRefundOrderStrategy(Long shopId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("isExitsRefundOrderStrategy入参,店铺Id" + shopId));
        }
        boolean result = shopStrategyQueryServiceCmd.isExitsRefundOrderStrategy(shopId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("isExitsRefundOrderStrategy出参:") + result);
        }
        return result;
    }

    /**
     * 根据店铺id查询是否传AG
     *
     * @param id
     * @return
     */
    public boolean isToAgByShopStrategy(Long id) {
        String redisKey = OmsRedisKeyResources.buildLockShopStrategyRedisKey(id);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey);
        if (hasKey != null && hasKey) {
            String redisResult = (String) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(redisResult)) {
                StCShopStrategyDO stCShopStrategy = JSON.parseObject(redisResult, StCShopStrategyDO.class);
                if (stCShopStrategy != null) {
                    return "Y".equalsIgnoreCase(stCShopStrategy.getIsAg());
                }
            }
        } else {
            StCShopStrategyDO stCShopStrategy = shopStrategyQueryServiceCmd.selectOcStCShopStrategyByCpCshopId(id);
            // 存redis
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(stCShopStrategy),
                    StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
            if (stCShopStrategy != null) {
                return "Y".equalsIgnoreCase(stCShopStrategy.getIsAg());
            }
        }
        return false;
    }

    /**
     * 获取短信策略
     *
     * @param cpCShopId 店铺id
     * @param taskNode  任务节点
     * @param var3
     * @return
     */
    public ValueHolderV14<StcMsgDO> queryMsgInfo(Long cpCShopId, String taskNode, String var3) {
        return stCMsgStrategyQueryCmd.queryMsgInfo(cpCShopId, taskNode, var3);
    }

    /**
     * 根据店铺id获取 发货单派单方案
     *
     * @param shopId
     * @return
     */
    public List<StCSendPlanDO> selectSendPlanListByShopId(Long shopId) {
        return sendPlanRuleQueryServiceCmd.selectSendPlanListByShopId(shopId);
    }

    /**
     * 根据店铺id获取 订单派单方案明细
     *
     * @param shopId
     * @return
     */
    public List<StCSendPlanItemDO> selectSendPlanItemListByPlanId(Long shopId) {
        return sendPlanRuleQueryServiceCmd.selectSendPlanItemListByPlanId(shopId);
    }

    /**
     * 获取订单派单规则仓库优先级明细
     *
     * @param regionProvinceId 省份id
     * @param sendRuleId       规则id
     * @return
     */
    public List<StCSendRuleAddressRankDO> selectSendRuleAddressRankList(Long regionProvinceId, Long sendRuleId) {
        return sendPlanRuleQueryServiceCmd.selectSendRuleAddressRankList(regionProvinceId, sendRuleId);
    }

    /**
     * 根据 订单派单规则策略 ids查找
     *
     * @param sendRuleIdList
     * @return
     */
    public List<StCSendRuleDO> selectSendRuleByIdList(List<Long> sendRuleIdList) {
        return sendPlanRuleQueryServiceCmd.selectSendRuleByIdList(sendRuleIdList);
    }

    /**
     * 获取分仓比例
     *
     * @param warehouseList
     * @param sendRuleId
     * @return
     */
    public List<StCSendRuleWarehouseRateDO> selectWarehouseRateMapper(List<Long> warehouseList, Long sendRuleId) {
        return sendPlanRuleQueryServiceCmd.selectWarehouseRateMapper(warehouseList, sendRuleId);
    }

    /**
     * 统计发货仓库发货数量
     *
     * @param warehouseList 实体发货仓库
     * @param sendRuleId    派单规则
     * @return BigDecimal
     */
    public BigDecimal selectQtySendTotal(List<Long> warehouseList, Long sendRuleId) {
        return sendPlanRuleQueryServiceCmd.selectQtySendTotal(warehouseList, sendRuleId);
    }

    /**
     * 更新发货仓库数量
     *
     * @param warehouseId 仓库Id
     * @return Integer
     */
    public Integer updateRuleWarehouseRate(Long warehouseId) {
        return sendPlanRuleQueryServiceCmd.updateRuleWarehouseRate(warehouseId);
    }

    /**
     * 查询店铺同步库存策略，获取逻辑仓优先级
     *
     * @param shopId
     * @return
     */
    public List<StCSyncStockStrategyVo> selectSyncStockStrategy(Long shopId) {
        return syncStockStrategyQueryServiceCmd.querySyncStockStrategyItemByID(shopId);
    }

    /**
     * 查询默认缺货仓库
     *
     * @param shopId
     * @return
     */
    public List<StCShopStrategyDO> selectDefaultShop(Long shopId) {
        return shopStrategyDefaultStoreQueryCmd.selectDefaultStore(shopId);
    }

    /**
     * 查询唯品会派单规则
     *
     * @param shopId
     * @return
     */
    public List<StCSendRuleAddressVipDo> findRuleAddressVipByShopId(Long shopId, String jitCode) {
        try {
            String redisKey = StRedisKeyResources.buildShopSendPlanAndRuleKey(shopId);
            CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
            Boolean hasKey = strRedisTemplate.hasKey(redisKey);
            if (hasKey != null && hasKey) {
                String json = strRedisTemplate.opsForValue().get(redisKey);
                return JSON.parseArray(json, StCSendRuleAddressVipDo.class);
            } else {
                List<StCSendRuleAddressVipDo> ruleAddressVipByShopId =
                        sendPlanRuleQueryServiceCmd.findRuleAddressVipByShopId(shopId, jitCode);
                if (CollectionUtils.isNotEmpty(ruleAddressVipByShopId)) {
                    strRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(ruleAddressVipByShopId),
                            StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
                    return ruleAddressVipByShopId;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("findRuleAddressVipByShopId发生异常{}"), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 根据店铺查找店铺价格方案
     *
     * @param shopId 店铺Id
     * @return List<Long>
     */
    public List<Long> queryPriceList(Long shopId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryPriceList入参:店铺Id=", shopId));
        }
        String key = StRedisKeyResources.buildShopPriceStrategyKey(shopId);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            List<Long> list = (List<Long>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            return CollectionUtils.isEmpty(list) ? null : list;
        }

        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        List<Long> priceList = shopStrategyQueryServiceCmd.queryPriceList(shopId);
        RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, priceList, cacheTime, TimeUnit.HOURS);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryPriceList出参:{},店铺Id=", shopId), priceList);
        }
        return priceList;

    }

    /**
     * <AUTHOR>
     * @Date 21:44 2021/5/27
     * @Description 查询符合策略的订单价格策略明细
     */
    public StCOrderPriceItemDO queryStCOrderPriceItemByPriceId(Long priceId, String policyType) {
        if (priceId == null || policyType == null) {
            return null;
        }
        return shopStrategyQueryServiceCmd.queryStCOrderPriceItemByPriceId(priceId, policyType);
    }

    /**
     * <AUTHOR>
     * @Date 13:41 2021/6/2
     * @Description 根据系统拆单查询自定义拆单
     */
    public StCSplitReasonRequest queryStCSplitReasonBysystemSplitReason(String systemSplitReason) {
        List<StCSplitReasonRequest> stCSplitReasonRequestList =
                stCSplitReasonQueryCmd.queryStCSplitReasonBySplitReason(systemSplitReason);
        if (CollectionUtils.isNotEmpty(stCSplitReasonRequestList)) {
            return stCSplitReasonRequestList.get(0);
        }
        log.info(LogUtil.format("查询st策略自定义拆单服务返回: {}"), stCSplitReasonRequestList);
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 14:18 2021/6/2
     * @Description 查询所有的自定义成拆单转成map
     */
    public Map<String, StCSplitReasonRequest> queryStCSplitReasonAll() {
        Map<String, StCSplitReasonRequest> map = new HashMap<>();
        List<StCSplitReasonRequest> stCSplitReasonAll = stCSplitReasonQueryCmd.queryStCSplitReasonAll();
        if (CollectionUtils.isEmpty(stCSplitReasonAll)) {
            return null;
        }
        log.info(LogUtil.format("查询st策略自定义拆单服务返回: {}"), stCSplitReasonAll);
        map = stCSplitReasonAll.stream().collect(Collectors.toMap(StCSplitReasonRequest::getSystemSplitReason,
                Function.identity(), (key1, key2) -> key1));
        return map;
    }

    /**
     * 根据店铺ID查询有效的卡单策略
     *
     * @param shopId
     * @return
     */
    public StDetentionPolicyRequest queryStDetentionPolicyByShopId(Long shopId) {
        String key = StRedisKeyResources.buildShopDetentionPolicyStKey(shopId);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            StDetentionPolicyRequest result =
                    (StDetentionPolicyRequest) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            return result;
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        StDetentionPolicyRequest result = stCDetentionPolicyQueryCmd.queryStCDetentionPolicyByShopId(shopId);
        if (result != null) {
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, result, cacheTime, TimeUnit.HOURS);
        }
        return result;
    }

    /**
     * description: 查询有效的定金预售预下沉策略
     *
     * @Author: liuwenjin
     * @Date 2021/9/24 12:10 下午
     */
    public List<StCDepositPreSaleSinkRequest> queryDepositPreSaleSinkList(Long warehouseId) {
        log.info(LogUtil.format("查询定金预售预下沉策略入参warehouseId：", warehouseId));
        String key = StRedisKeyResources.buildShopDepositPreSaleStKey(warehouseId);
        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        if (hasKey != null && hasKey) {
            List<StCDepositPreSaleSinkRequest> result =
                    (List<StCDepositPreSaleSinkRequest>) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
            log.info(LogUtil.format("从redis查询定金预售预习沉策略出参result {}"), JSON.toJSONString(result));
            return result;
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        List<StCDepositPreSaleSinkRequest> result = stCDepositPreSaleSinkQueryCmd.queryDepositPreSaleSink(warehouseId);
        if (result != null) {
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, result, cacheTime, TimeUnit.HOURS);
        }
        log.info(LogUtil.format("从rpc查询定金预售预习沉策略出参result {}"), JSON.toJSONString(result));
        return result;
    }

    /**
     * description: 查询有效的订单打标策略
     *
     * @Author: YCH
     * todo 作废
     */
    public List<StCOrderLabelRequest> queryCOrderLabelList(Long shopId) {
        log.info(" 查询有效的订单打标策略入参param {}", shopId);
        //        String key = StRedisKeyResources.buildShopOrderLabelStKey(shopId);
        //        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        //        if (hasKey != null && hasKey) {
        //            List<StCOrderLabelRequest> result = (List<StCOrderLabelRequest>) RedisOpsUtil
        //            .getObjRedisTemplate().opsForValue().get(key);
        //            log.info(" 从redis查询订单打标策略出参result {}",JSON.toJSONString(result));
        //            if (CollectionUtils.isNotEmpty(result)){
        //                return result;
        //            }
        //        }
        List<StCOrderLabelRequest> result = stCOrderLabelQueryCmd.queryDepositPreSaleSink(shopId);
        //        if (CollectionUtils.isNotEmpty(result)){
        //            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key,result);
        //        }
        log.info(" 从rpc查询订单打标策略出参result {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 新的打标策略
     *
     * @param shopId
     * @return
     */
    public List<StCOrderLabelRequest> selectCOrderLabelList(Long shopId) {
        String redisKey = StRedisKey.ST_ORDER_LABEL + shopId;
        String orderLabel = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(orderLabel)) {
            List<StCOrderLabelRequest> stCOrderLabelRequests = JSON.parseArray(orderLabel, StCOrderLabelRequest.class);
            return stCOrderLabelRequests;
        } else {
            ValueHolderV14<List<StCOrderLabelRequest>> holder = stCOrderLabelQueryCmd.selectOrderLabel(shopId);
            if (holder.isOK()) {
                List<StCOrderLabelRequest> data = holder.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    String str = JSONObject.toJSONString(data);
                    redisUtil.strRedisTemplate.opsForValue().set(redisKey, str);
                    return data;
                }
            }
            return null;
        }


    }


    /**
     * description:根据实体仓ID查询物流id
     *
     * @Author: liuwenjin
     * @Date 2022/2/27 11:46 上午
     */
    public Long getLogisticInfoByWarehouseIdAndShopId(Long warehouseId, Long shopId) {
        log.info("根据实体仓ID查询物流id，shopId {},warehouseId{}", shopId, warehouseId);
        //        String key = StRedisKeyResources.buildWarehouseIdStKey(warehouseId);
        //        Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
        //        if (hasKey != null && hasKey) {
        //            Long logisticIds = (Long) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
        //            log.info("根据实体仓ID查询物流id{}",logisticIds);
        //            if (logisticIds != null){
        //                return logisticIds;
        //            }
        //        }
        List<Long> logisticIds = expressQueryServiceCmd.getLogisticInfoByWarehouseIdAndShopId(warehouseId, shopId);
        log.info("根据实体仓ID查询物流id{}", logisticIds);
        if (CollectionUtils.isNotEmpty(logisticIds)) {
            return logisticIds.get(0);
        } else {
            return null;
        }
    }


    /**
     * 根据店铺查找店铺价格方案
     *
     * @param shopId 店铺Id
     * @return List<Long>
     */
    public StCPriceResult queryPricesByShopId(Long shopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("根据店铺ID查询店铺价格策略！shopId={}", "StRpcService"), shopId);
        }
        String key = StRedisKeyResources.buildShopPriceStrategyInfoKey(shopId);
        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        Boolean hasKey = strRedisTemplate.hasKey(key);
        if (hasKey != null && hasKey) {
            String value = strRedisTemplate.opsForValue().get(key);
            if (StringUtils.isNotEmpty(value)) {
                return JSON.parseObject(value, StCPriceResult.class);
            }
        }
        int cacheTime = StRedisKeyResources.getCacheTimeConf();
        ValueHolderV14<StCPriceResult> v14 = shopStrategyQueryServiceCmd.queryPricesByShopId(shopId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("根据店铺ID查询店铺价格策略！result={}", "StRpcService"), JSON.toJSONString(v14));
        }
        if (v14.isOK() && v14.getData() != null) {
            StCPriceResult result = v14.getData();
            strRedisTemplate.opsForValue().set(key, JSON.toJSONString(result), cacheTime, TimeUnit.HOURS);
            return result;
        } else {
            return null;
        }
    }


    /**
     * 查询仓库物流策略
     *
     * @param warehouseId 发货仓库Id
     * @return int
     */
    public List<StCWarehouseLogisticStrategyResult> getWarehouseExpress(Long warehouseId) {

        String redisKey = OmsRedisKeyResources.buildExpressByWarehouseKey(warehouseId);

        //判断key是否存在
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        Object redisResult = redisTemplate.opsForValue().get(redisKey);


        List<StCWarehouseLogisticStrategyResult> result;
        if (redisResult == null) {
            StCWarehouseLogisticStrategyItemQueryRequest request = new StCWarehouseLogisticStrategyItemQueryRequest();
            request.setWarehouseIdList(Collections.singletonList(warehouseId));
            ValueHolderV14<List<StCWarehouseLogisticStrategyResult>> v14 =
                    warehouseExpressQueryCmd.queryLogisticStrategyByWarehouseAndLogistics(request);
            if (v14 == null || CollectionUtils.isEmpty(v14.getData())) {
                result = new ArrayList<>();
            } else {
                result = v14.getData();
            }
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(result), 20L, TimeUnit.MINUTES);
            return result;
        } else {
            result = JSONObject.parseArray(redisResult.toString(), StCWarehouseLogisticStrategyResult.class);
        }
        return result;
    }


    /**
     * 查询仓库物流策略
     *
     * @param provinceId 省
     * @param cityId     市
     * @param areaId     区
     * @return int
     */
    public List<Long> getBansExpress(Long warehouseId, Long provinceId, Long cityId, Long areaId) {

        String redisKey = OmsRedisKeyResources.buildExpressByBansKey();

        //判断key是否存在
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        StCBansAreaStrategyRequest request = new StCBansAreaStrategyRequest();
        String redisHashKeyStr = warehouseId + "_";

        request.setCpCPhyWarehouseId(warehouseId);

        if (provinceId != null) {
            redisHashKeyStr = redisHashKeyStr + provinceId;
            request.setCpCProvinceId(provinceId);
        }
        if (cityId != null) {
            redisHashKeyStr = redisHashKeyStr + "_" + cityId;
            request.setCpCCityId(cityId);
        }
        if (areaId != null && areaId != 0L) {
            redisHashKeyStr = redisHashKeyStr + "_" + areaId;
            request.setCpCRegionAreaId(areaId);
        }

        Object redisResult = redisTemplate.opsForHash().get(redisKey, redisHashKeyStr);
        ValueHolderV14<StCBansAreaStrategyResult> v14 = null;
        List<Long> expressIds = null;
        if (redisResult == null) {
            v14 = bansExpressQueryCmd.queryBansAreaStrategy2(request);
            if (v14 == null || v14.getData() == null || v14.getData().getCpCLogisticsIdList() == null) {
                expressIds = new ArrayList<>();
            } else {
                expressIds = v14.getData().getCpCLogisticsIdList();
            }
            redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, JSONObject.toJSONString(expressIds));
            return expressIds;
        } else {
            expressIds = JSONObject.parseArray(redisResult.toString(), Long.class);
        }
        return expressIds;
    }


    /**
     * 查询店铺物流策略
     *
     * @param shopId shopId
     * @return int
     */
    public List<StCShopLogisticStrategyItem> getShopExpress(Long shopId) {

        String redisKey = OmsRedisKeyResources.buildExpressByShopKey(shopId);

        //判断key是否存在
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        Object redisResult = redisTemplate.opsForValue().get(redisKey);

        List<StCShopLogisticStrategyItem> result;
        if (redisResult == null) {
            StCShopLogisticStrategyQueryRequest request = new StCShopLogisticStrategyQueryRequest();
            request.setCpCShopId(shopId);
            ValueHolderV14<List<StCShopLogisticStrategyItem>> v14 =
                    shopExpressQueryCmd.queryStCShopLogisticStrategy(request);
            if (v14 == null || CollectionUtils.isEmpty(v14.getData())) {
                result = new ArrayList<>();
            } else {
                result = v14.getData();
            }
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(result));
            return result;
        } else {
            result = JSONObject.parseArray(redisResult.toString(), StCShopLogisticStrategyItem.class);
        }
        return result;
    }


    /**
     * 查询商品物流策略
     *
     * @return int
     */
    public List<StCProLogisticStrategy> queryStCProLogisticStrategy(List<Long> psCProIdList, List<Long> psCProdimIdList,
                                                                    List<String> numIids, Long provinceId, Long warehouseId,
                                                                    Long cityId, Long areaId) {

        StCProLogisticStrategyQueryRequest request = new StCProLogisticStrategyQueryRequest();
        request.setPsCProIdList(psCProIdList);
        request.setPsCProdimIdList(psCProdimIdList);
        request.setNumIids(numIids);
        request.setProvinceId(provinceId);
        request.setCityId(cityId);
        request.setAreaId(areaId);
        request.setWarehouseId(warehouseId);
        ValueHolderV14<List<StCProLogisticStrategy>> v14 = stCProLogisticStrategyQueryCmd.queryStCProLogisticStrategy(request);
        if (v14 == null || CollectionUtils.isEmpty(v14.getData())) {
            return new ArrayList<>();
        } else {
            return v14.getData();
        }
    }


    public List<StCExpressPriceStrategyQueryResult> getExpressPrice(Long warehouseId, Long provinceId) {

        String redisKey = OmsRedisKeyResources.buildExpressPrice(warehouseId);

        //判断key是否存在
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();

        Object redisResult = redisTemplate.opsForValue().get(redisKey);

        List<StCExpressPriceStrategyQueryResult> result;
        if (redisResult == null) {
            StCExpressPriceStrategyQueryRequest request = new StCExpressPriceStrategyQueryRequest();
            request.setCpCPhyWarehouseId(warehouseId);
            request.setCpCProvinceId(provinceId);
            result = stCExpressPriceStrategyQueryCmd.queryStCExpressPriceByParams(request);
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(result));
            return result;
        } else {
            result = JSONObject.parseArray(redisResult.toString(), StCExpressPriceStrategyQueryResult.class);
        }
        return result;
    }

    /**
     * 根据商品编码查已审核周期购策略
     * @param proCode 商品编码
     * @return List<StCCyclePurchaseStrategyResult>
     */
    public List<StCCyclePurchaseStrategyResult> queryCyclePurchaseStrategyByProCode(String proCode) {

        List<StCCyclePurchaseStrategyResult> cyclePurchaseStrategyResultList = null;

        String redisKey = OmsRedisKeyResources.buildCyclePurchaseStrategyProCodeRedisKey(proCode);
        try {
            // 存在策略key
            if (Boolean.TRUE.equals(RedisOpsUtil.getStrRedisTemplate().hasKey(redisKey))) {
                String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(redisKey);
                if(log.isDebugEnabled()){
                    log.debug(LogUtil.format("queryCyclePurchaseStrategyByProCode From Redis Key={}; proCode={};SelectResult={}"),
                            redisKey, proCode, redisValue);
                }
                if (StringUtils.isNoneBlank(redisValue)) {
                    cyclePurchaseStrategyResultList = JSON.parseArray(redisValue, StCCyclePurchaseStrategyResult.class);
                }
                return cyclePurchaseStrategyResultList;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("queryCyclePurchaseStrategyByProCode From Redis error") + Throwables.getStackTraceAsString(e));
        }

        try {
            StCCyclePurchaseStrategyQueryRequest request = new StCCyclePurchaseStrategyQueryRequest();
            request.setProCode(proCode);
            ValueHolderV14<List<StCCyclePurchaseStrategyResult>> v14 = cyclePurchaseStrategyCmd.queryCyclePurchaseStrategyByProCode(request);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryCyclePurchaseStrategyByProCode From RPC proCode={};SelectResult={}"), proCode, JSON.toJSONString(v14));
            }
            if(!v14.isOK()){
                log.error(LogUtil.format("queryCyclePurchaseStrategyByProCode From RPC error ") + v14.getMessage());
                return null;
            }
            cyclePurchaseStrategyResultList = v14.getData();
            //存放在redis中
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(v14.getData()));
        } catch (Exception e) {
            log.error(LogUtil.format("queryCyclePurchaseStrategyByProCode From RPC error ") + Throwables.getStackTraceAsString(e));
            return null;
        }
        return cyclePurchaseStrategyResultList;
    }
    /**
     * 根据商品编码查已审核周期购策略
     * @param id 策略ID
     * @return StCCyclePurchaseStrategyResult
     */
    public StCCyclePurchaseStrategyResult queryCyclePurchaseStrategyById(Long id) {

        StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult = null;

        String redisKey = OmsRedisKeyResources.buildCyclePurchaseStrategyIdRedisKey(id);
        try {
            // 存在策略key
            if (Boolean.TRUE.equals(RedisOpsUtil.getStrRedisTemplate().hasKey(redisKey))) {
                String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(redisKey);
                if(log.isDebugEnabled()){
                    log.debug(LogUtil.format("queryCyclePurchaseStrategyById From Redis Key={}; id={};SelectResult={}"),
                            redisKey, id, redisValue);
                }
                if (StringUtils.isNoneBlank(redisValue)) {
                    cyclePurchaseStrategyResult = JSON.parseObject(redisValue, StCCyclePurchaseStrategyResult.class);
                }
                return cyclePurchaseStrategyResult;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("queryCyclePurchaseStrategyById From Redis error") + Throwables.getStackTraceAsString(e));
        }

        try {
            StCCyclePurchaseStrategyQueryRequest request = new StCCyclePurchaseStrategyQueryRequest();
            request.setId(id);
            ValueHolderV14<StCCyclePurchaseStrategyResult> v14 = cyclePurchaseStrategyCmd.queryCyclePurchaseStrategyById(request);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryCyclePurchaseStrategyById From RPC proCode={};SelectResult={}"), id, JSON.toJSONString(v14));
            }
            if(!v14.isOK()){
                log.error(LogUtil.format("queryCyclePurchaseStrategyById From RPC error ") + v14.getMessage());
                return null;
            }
            cyclePurchaseStrategyResult = v14.getData();
            //存放在redis中
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(v14.getData()));
        } catch (Exception e) {
            log.error(LogUtil.format("queryCyclePurchaseStrategyById From RPC error ") + Throwables.getStackTraceAsString(e));
            return null;
        }
        return cyclePurchaseStrategyResult;
    }

    /**
     * 根据店铺ID查询店铺策略物流公司明细
     * @param shopId
     * @return
     */
    public StCShopStrategyLogisticsItemResult queryShopStrategyLogisticsList(Long shopId){
        try{
            StCShopStrategyLogisticsItemResult shopLogistics = shopStrategyQueryServiceCmd.queryShopStrategyLogisticsList(shopId);
            return shopLogistics;
        }catch (Exception e){
            log.error(LogUtil.format("StRpcService.queryShopStrategyLogisticsList.shopId={}","StRpcService") ,Throwables.getStackTraceAsString(e));
            return null;
        }
    }
    /**
     * description:异常信息匹配异常定义策略
     * @Author:  liuwenjin
     * @Date 2022/10/17 15:51
     */
    public StCMatchAbnormalStrategy matchAbnormal(String msg,Integer type){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" matchAbnormal 异常信息匹配入参 msg{},type{}"),msg,type);
        }
        StCMatchAbnormalStrategy matchAbnormalStrategy = null;
        ValueHolderV14<List<StCMatchAbnormalStrategy>> v14 = stCMatchAbnormalStrategyMatchCmd.match(type,msg);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" matchAbnormal 异常信息匹配出参 result{}"),JSON.toJSONString(v14));
        }
        if (v14.isOK()){
            List<StCMatchAbnormalStrategy> list = v14.getData();
            if (CollectionUtils.isNotEmpty(list)){
                matchAbnormalStrategy = list.get(0);
            }
        }
        return matchAbnormalStrategy;
    }

    public ValueHolderV14<StCAllocationCostQueryResult> queryAllocationCost(StCAllocationCostQueryRequest request) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StRpcService.queryAllocationCost request:{}", "查找调拨报价入参",
                    JSONObject.toJSONString(request)));
        }

        ValueHolderV14<StCAllocationCostQueryResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,
                "SUCCESS");
        try {

            StCAllocationCostQueryResult stCAllocationCostQueryResult = cAllocationCostQueryCmd.queryAllocationCost(request);
            valueHolderV14.setData(stCAllocationCostQueryResult);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("StRpcService.queryAllocationCost result:{}", "查找调拨报价出参",
                        JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {
            log.error(" 查找调拨报价设置,异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;

    }
    /**
     * description:京东代销订单查询物流
     * @Author:  liuwenjin
     * @Date 2022/12/12 19:38
     */
    public List<Long> queryJdDistributionLogistics(Long shopId,String code){

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" StRpcService.queryJdDistributionLogistics request shopId:{},code{}"),shopId,code);
        }
        List<Long> list = new ArrayList<>();
        try {
            ValueHolderV14<List<Long>> v14 = jdDistributionLogisticsCmd.getLogisticsIdsByDistributionId(shopId, code);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" StRpcService.queryJdDistributionLogistics 出参 {}"),JSON.toJSONString(v14));
            }
            if (v14.isOK()) {
                list = v14.getData();
            }
        } catch (Exception e) {
            log.error(" 京东代销订单查询物流异常:{}", Throwables.getStackTraceAsString(e));
            return null;
        }
        return list;
    }

    public List<OcBPreOrderModelResult> getAllModel() {
        ValueHolderV14<List<StCPreorderModelStrategyResult>> valueHolderV14 = modelStrategyCmd.getAllModel();
        if (!valueHolderV14.isOK()) {
            return new ArrayList<>();
        }
        List<StCPreorderModelStrategyResult> modelStrategyResultList = valueHolderV14.getData();
        if (CollectionUtils.isEmpty(modelStrategyResultList)) {
            return new ArrayList<>();
        }
        List<OcBPreOrderModelResult> ocBPreOrderModelResultList = new ArrayList<>();
        for (StCPreorderModelStrategyResult modelStrategyResult : modelStrategyResultList) {
            OcBPreOrderModelResult result = new OcBPreOrderModelResult();
            result.setModelCode(modelStrategyResult.getCode());
            result.setModelName(modelStrategyResult.getName());
            ocBPreOrderModelResultList.add(result);
        }
        return ocBPreOrderModelResultList;
    }

    public List<StCPreorderFieldStrategyDO> getFieldStrategyByModelCode(String modelCode) {
        ValueHolderV14 valueHolderV14 = modelStrategyCmd.getFieldsByModelCode(modelCode);
        if (!valueHolderV14.isOK()) {
            return new ArrayList<>();
        }
        return (List<StCPreorderFieldStrategyDO>) valueHolderV14.getData();
    }

    public List<StCPreorderItemStrategyDO> getItemByModelCode(String modelCode) {
        ValueHolderV14 valueHolderV14 = modelStrategyCmd.getItemByModelCode(modelCode);
        if (!valueHolderV14.isOK()) {
            return new ArrayList<>();
        }
        return (List<StCPreorderItemStrategyDO>) valueHolderV14.getData();
    }

    /**
     * 根据仓库查询调拨预估报价设置
     *
     * @param warehouseIds
     * @return
     */
    public ValueHolderV14<List<StCAllocationStorageCostStrategy>> queryFactoryCostByWarehouseIds(List<Long> warehouseIds) {
        return stCAllocationStorageCostQueryCmd.queryByWarehouseIds(warehouseIds);
    }

    /**
     * 根据增值服务名称+仓库 获取策略及价格信息
     *
     * @param typeDocName
     * @param cpCStoreId
     * @return
     */
    public ValueHolderV14<StAddedServiceStrategyQueryResult> selectByTypeDocNameAndCpCStoreId(String typeDocName, Long cpCStoreId) {
        return addedServiceStrategyQueryCmd.selectByTypeDocNameAndCpCPhyWarehouseId(typeDocName, cpCStoreId);
    }

    public ValueHolderV14<StAddedServiceStrategyDocResult> selectDocInfoByTypeDocName(String typeDocName) {
        return addedServiceStrategyQueryCmd.selectDocInfoByTypeDocName(typeDocName);
    }

    public ValueHolderV14<List<LabelingRequirementsQueryResult>> queryAllDoc() {
        ValueHolderV14<List<LabelingRequirementsQueryResult>> value = new ValueHolderV14<>();
        List<LabelingRequirementsQueryResult> list = new ArrayList<>();
        ValueHolderV14<List<StAddedServiceStrategyDocResult>> valueHolderV14;
        try {
            valueHolderV14 = addedServiceStrategyQueryCmd.queryAllDoc();
        } catch (Exception e) {
            value.setCode(ResultCode.FAIL);
            value.setMessage(e.getMessage());
            log.error("查询所有文档异常:{}", Throwables.getStackTraceAsString(e));
            return value;
        }
        if (valueHolderV14.isOK()) {
            List<StAddedServiceStrategyDocResult> docResults = valueHolderV14.getData();
            if (CollectionUtils.isNotEmpty(docResults)) {
                for (StAddedServiceStrategyDocResult docResult : docResults) {
                    LabelingRequirementsQueryResult result = new LabelingRequirementsQueryResult();
                    result.setAddedTypeCode(docResult.getAddedTypeCode());
                    result.setAddedTypeName(docResult.getAddedTypeName());
                    list.add(result);
                }
            }
            value.setData(list);
            return value;
        }
        value.setCode(valueHolderV14.getCode());
        value.setMessage(valueHolderV14.getMessage());
        return value;
    }


    public Integer queryArrivalDays(Long cpCLogisticsId, Long cpCPhyWarehouseId, Long provinceId) {
        StCUnfullcarCostArrivalDaysRequest request = new StCUnfullcarCostArrivalDaysRequest();
        request.setCpCLogisticsId(cpCLogisticsId);
        request.setCpCPhyWarehouseId(cpCPhyWarehouseId);
        request.setProvinceId(provinceId);

        ValueHolderV14<StCUnfullcarCostArrivalDaysResponse> holderV14 = stCUnfullcarCostItemQueryCmd.queryArrivalDays(request);
        if (Objects.nonNull(holderV14) && holderV14.isOK() && Objects.nonNull(holderV14.getData())) {
            return holderV14.getData().getArrivalDays();
        }

        log.debug(LogUtil.format("查询到货时间异常，入参:{},结果:{}", "StRpcService.queryArrivalDays"),
                JSONObject.toJSONString(request), JSONObject.toJSONString(holderV14));
        return null;
    }

    public StCBnWarehouseLogisticsConfigDO queryByWarehouseCodeAndLogisticsCode(String warehouseCode, String logisticsCode) {
        ValueHolderV14<StCBnWarehouseLogisticsConfigDO> result = bnQueryCmd.queryByWarehouseCodeAndLogisticsCode(warehouseCode, logisticsCode);
        if (result.isOK()) {
            return result.getData();
        }
        return null;
    }

    public StCBnProblemConfigDO queryBnProblemByText(String problemText) {
        ValueHolderV14<StCBnProblemConfigDO> result = bnQueryCmd.queryBnProblemByText(problemText);
        if (result.isOK()) {
            return result.getData();
        }
        return null;
    }

    public StCPreOccupyProvincePriority queryByProvinceEcode(String provinceCode) {
        ValueHolderV14<StCPreOccupyProvincePriority> valueHolderV14 = provincePriorityQueryCmd.queryByProvinceEcode(provinceCode);
        if (valueHolderV14.isOK()) {
            return valueHolderV14.getData();
        }
        return null;
    }

    public StCPreOccupyWarehousePriority queryByWarehouseEcode(String warehouseCode) {
        ValueHolderV14<StCPreOccupyWarehousePriority> valueHolderV14 = warehousePriorityQueryCmd.queryByWarehouseEcode(warehouseCode);
        if (valueHolderV14.isOK()) {
            return valueHolderV14.getData();
        }
        return null;
    }

    public StCCustomLabelDO queryByEname(String ename) {
        try {
            return customLabelQueryCmd.queryByEname(ename);
        } catch (Exception e) {
            log.error("查询自定义标签异常:{}", Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 根据平台id查询【关键字快递拦截策略】获取关键字
     *
     * @param platformId
     * @return
     */
    public List<String> queryKeywordsInterceptByPlatformId(Long platformId) {
        if (platformId == null) {
            return new ArrayList<>();
        }
        List<String> keywords = new ArrayList<>();
        String redisKey = StCConstants.ST_C_KEYWORDS_INTERCEPT_STRATEGY + platformId;
        CusRedisTemplate<Object, Object> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (Boolean.TRUE.equals(strRedisTemplate.hasKey(redisKey))) {
            String redisValue = (String) strRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(redisValue)) {
                keywords = JSONObject.parseArray(redisValue, String.class);
            }
        } else {
            ValueHolderV14<List<StCKeywordsInterceptStrategy>> v14 =
                    stCKeywordsInterceptStrategyQueryCmd.queryByPlatformId(platformId);
            if (!v14.isOK()) {
                log.error(LogUtil.format("StRpcService.queryKeywordsInterceptByPlatformId error:{}",
                        "StRpcService.queryKeywordsInterceptByPlatformId"), v14.getMessage());
                return new ArrayList<>();
            }
            List<StCKeywordsInterceptStrategy> strategyList = v14.getData();
            if (CollectionUtils.isNotEmpty(strategyList)) {
                keywords = strategyList.stream().map(StCKeywordsInterceptStrategy::getKeywords)
                        .distinct().collect(Collectors.toList());
            }
            strRedisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(keywords), 1L, TimeUnit.HOURS);
        }
        return keywords;
    }

}