package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.service.LogCommonService;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterExchangeTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderUtils;
import com.jackrain.nea.web.DefaultWebEvent;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除服务
 **/
@Slf4j
@Service
public class StCEquityBarterStrategyItemDeleteService extends CommandAdapter {

    @Autowired
    private StCEquityBarterStrategyItemMapper itemMapper;

    @Autowired
    private StCEquityBarterStrategyMapper strategyMapper;

    @Autowired
    private LogCommonService logCommonService;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    /**
     * 执行删除操作
     *
     * @param querySession 查询会话
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        User user = querySession.getUser();
        
        log.info("删除对等换货策略明细请求：{}", JSON.toJSONString(param));
        try {
            // 参数校验
            validateRequest(param);

            // 查询要删除的明细记录
            List<StCEquityBarterStrategyItem> itemsToDelete = findItemsToDelete(param);

            if (CollectionUtils.isEmpty(itemsToDelete)) {
                return ValueHolderUtils.getFailValueHolder("未找到符合条件的对等换货策略明细");
            }

            // 记录删除日志
            List<OcBOperationLog> operationLogs = buildDeleteLogs(itemsToDelete, user);

            // 执行删除
            List<Long> itemIds = itemsToDelete.stream().map(StCEquityBarterStrategyItem::getId).collect(Collectors.toList());
            itemMapper.deleteBatchIds(itemIds);

            // 批量保存日志
            if (CollectionUtils.isNotEmpty(operationLogs)) {
                logCommonService.batchInsertLog(operationLogs);
            }

            // 清除相关缓存
            clearRelatedCache(itemsToDelete);

            log.info("成功删除对等换货策略明细，删除数量：{}", itemsToDelete.size());
            return ValueHolderUtils.getSuccessValueHolder("删除了 " + itemsToDelete.size() + " 条明细记录", "删除成功");

        } catch (NDSException e) {
            log.error("删除对等换货策略明细失败：{}", e.getMessage());
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        } catch (Exception e) {
            log.error("删除对等换货策略明细异常", e);
            return ValueHolderUtils.getFailValueHolder("删除失败：" + e.getMessage());
        }
    }

    /**
     * 删除对等换货策略明细（直接参数版本）
     *
     * @param shopType 店铺类型
     * @param shopIds 店铺ID列表
     * @param exchangeSkuId 换货商品ID
     * @param exchangeQty 换货数量
     * @param equitySkuId 对等商品ID
     * @param equityQty 对等数量
     * @param user 操作用户
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<String> deleteStrategyItems(Integer shopType, 
                                                      List<Long> shopIds, 
                                                      Long exchangeSkuId, 
                                                      BigDecimal exchangeQty, 
                                                      Long equitySkuId, 
                                                      BigDecimal equityQty, 
                                                      User user) {
        try {
            // 参数校验
            validateDirectParams(shopType, shopIds, exchangeSkuId, exchangeQty, equitySkuId, equityQty);

            // 查询要删除的明细记录
            List<StCEquityBarterStrategyItem> itemsToDelete = findItemsToDeleteDirect(
                    shopType, shopIds, exchangeSkuId, exchangeQty, equitySkuId, equityQty);

            if (CollectionUtils.isEmpty(itemsToDelete)) {
                return new ValueHolderV14<>(-1, "未找到符合条件的对等换货策略明细");
            }

            // 记录删除日志
            List<OcBOperationLog> operationLogs = buildDeleteLogs(itemsToDelete, user);

            // 执行删除
            List<Long> itemIds = itemsToDelete.stream().map(StCEquityBarterStrategyItem::getId).collect(Collectors.toList());
            itemMapper.deleteBatchIds(itemIds);

            // 批量保存日志
            if (CollectionUtils.isNotEmpty(operationLogs)) {
                logCommonService.batchInsertLog(operationLogs);
            }

            // 清除相关缓存
            clearRelatedCache(itemsToDelete);

            log.info("成功删除对等换货策略明细，删除数量：{}", itemsToDelete.size());
            return new ValueHolderV14<>(0, "删除成功", "删除了 " + itemsToDelete.size() + " 条明细记录");

        } catch (NDSException e) {
            log.error("删除对等换货策略明细失败：{}", e.getMessage());
            return new ValueHolderV14<>(-1, e.getMessage());
        } catch (Exception e) {
            log.error("删除对等换货策略明细异常", e);
            return new ValueHolderV14<>(-1, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 直接参数校验
     */
    private void validateDirectParams(Integer shopType, List<Long> shopIds, Long exchangeSkuId, 
                                     BigDecimal exchangeQty, Long equitySkuId, BigDecimal equityQty) {
        if (shopType == null) {
            throw new NDSException("店铺类型不能为空");
        }

        if (shopType == 1 && CollectionUtils.isEmpty(shopIds)) {
            throw new NDSException("当店铺类型为指定店铺时，店铺ID列表不能为空");
        }

        if (exchangeSkuId == null) {
            throw new NDSException("换货商品ID不能为空");
        }

        if (exchangeQty == null) {
            throw new NDSException("换货数量不能为空");
        }

        if (equitySkuId == null) {
            throw new NDSException("对等商品ID不能为空");
        }

        if (equityQty == null) {
            throw new NDSException("对等数量不能为空");
        }
    }

    /**
     * 查找要删除的明细记录（直接参数版本）
     */
    private List<StCEquityBarterStrategyItem> findItemsToDeleteDirect(Integer shopType, List<Long> shopIds,
                                                                     Long exchangeSkuId, BigDecimal exchangeQty,
                                                                     Long equitySkuId, BigDecimal equityQty) {
        QueryWrapper<StCEquityBarterStrategyItem> queryWrapper = new QueryWrapper<>();
        
        // 根据换货商品ID、换货数量、对等商品ID、对等数量查询
        queryWrapper.lambda()
                .eq(StCEquityBarterStrategyItem::getPsCSkuId, exchangeSkuId)
                .eq(StCEquityBarterStrategyItem::getQty, exchangeQty)
                .eq(StCEquityBarterStrategyItem::getEquitySkuId, equitySkuId)
                .eq(StCEquityBarterStrategyItem::getEquityQty, equityQty);

        List<StCEquityBarterStrategyItem> allItems = itemMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(allItems)) {
            return new ArrayList<>();
        }

        // 根据店铺类型进一步筛选
        List<StCEquityBarterStrategyItem> filteredItems = new ArrayList<>();
        
        for (StCEquityBarterStrategyItem item : allItems) {
            StCEquityBarterStrategy strategy = strategyMapper.selectById(item.getStCEquityBarterStrategyId());
            if (strategy == null) {
                continue;
            }

            // 店铺类型：1-指定店铺；2-所选指定店铺
            if (shopType == 1) {
                // 指定店铺：策略类型为2且店铺ID在指定列表中
                if ("2".equals(strategy.getType()) && shopIds.contains(strategy.getCpCShopId())) {
                    filteredItems.add(item);
                }
            } else if (shopType == 2) {
                // 所选指定店铺：策略类型为2的所有记录
                if ("2".equals(strategy.getType())) {
                    filteredItems.add(item);
                }
            }
        }

        return filteredItems;
    }

    /**
     * 参数校验（JSONObject版本）
     */
    private void validateRequest(JSONObject param) {
        Integer shopType = param.getInteger("shopType");
        if (shopType == null) {
            throw new NDSException("店铺类型不能为空");
        }

        if (shopType == 1 && (param.getJSONArray("shopIds") == null || param.getJSONArray("shopIds").isEmpty())) {
            throw new NDSException("当店铺类型为指定店铺时，店铺ID列表不能为空");
        }

        if (param.getLong("exchangeSkuId") == null) {
            throw new NDSException("换货商品ID不能为空");
        }

        if (param.getBigDecimal("exchangeQty") == null) {
            throw new NDSException("换货数量不能为空");
        }

        if (param.getLong("equitySkuId") == null) {
            throw new NDSException("对等商品ID不能为空");
        }

        if (param.getBigDecimal("equityQty") == null) {
            throw new NDSException("对等数量不能为空");
        }
    }

    /**
     * 查找要删除的明细记录（JSONObject版本）
     */
    private List<StCEquityBarterStrategyItem> findItemsToDelete(JSONObject param) {
        QueryWrapper<StCEquityBarterStrategyItem> queryWrapper = new QueryWrapper<>();
        
        // 根据换货商品ID、换货数量、对等商品ID、对等数量查询
        queryWrapper.lambda()
                .eq(StCEquityBarterStrategyItem::getPsCSkuId, param.getLong("exchangeSkuId"))
                .eq(StCEquityBarterStrategyItem::getQty, param.getBigDecimal("exchangeQty"))
                .eq(StCEquityBarterStrategyItem::getEquitySkuId, param.getLong("equitySkuId"))
                .eq(StCEquityBarterStrategyItem::getEquityQty, param.getBigDecimal("equityQty"));

        List<StCEquityBarterStrategyItem> allItems = itemMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(allItems)) {
            return new ArrayList<>();
        }

        // 根据店铺类型进一步筛选
        List<StCEquityBarterStrategyItem> filteredItems = new ArrayList<>();
        Integer shopType = param.getInteger("shopType");
        List<Long> shopIds = new ArrayList<>();
        if (param.getJSONArray("shopIds") != null) {
            for (Object obj : param.getJSONArray("shopIds")) {
                shopIds.add(Long.valueOf(obj.toString()));
            }
        }
        
        for (StCEquityBarterStrategyItem item : allItems) {
            StCEquityBarterStrategy strategy = strategyMapper.selectById(item.getStCEquityBarterStrategyId());
            if (strategy == null) {
                continue;
            }

            // 店铺类型：1-指定店铺；2-所选指定店铺
            if (shopType == 1) {
                // 指定店铺：策略类型为2且店铺ID在指定列表中
                if ("2".equals(strategy.getType()) && shopIds.contains(strategy.getCpCShopId())) {
                    filteredItems.add(item);
                }
            } else if (shopType == 2) {
                // 所选指定店铺：策略类型为2的所有记录
                if ("2".equals(strategy.getType())) {
                    filteredItems.add(item);
                }
            }
        }

        return filteredItems;
    }

    /**
     * 构建删除日志
     */
    private List<OcBOperationLog> buildDeleteLogs(List<StCEquityBarterStrategyItem> itemsToDelete, User user) {
        List<OcBOperationLog> operationLogs = new ArrayList<>();

        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            // 构建日志内容：[换货商品编码],[换货商品名称],[换货数量],[对等商品编码],[对等商品名称],[对等数量],[换货类型描述]
            StringBuilder logContent = new StringBuilder();
            logContent.append("[").append(item.getPsCSkuCode()).append("]")
                    .append(",[").append(item.getPsCSkuName()).append("]")
                    .append(",[").append(item.getQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(item.getEquitySkuCode()).append("]")
                    .append(",[").append(item.getEquitySkuName()).append("]")
                    .append(",[").append(item.getEquityQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(getExchangeTypeDesc(item.getExchangeType())).append("]");

            OcBOperationLog operationLog = logCommonService.getOperationLog(
                    "ST_C_EQUITY_BARTER_STRATEGY_ITEM",
                    "DEL",
                    item.getStCEquityBarterStrategyId(),
                    "对等换货策略明细",
                    "删除对等换货策略明细",
                    logContent.toString(),
                    "删除",
                    user
            );

            operationLogs.add(operationLog);
        }

        return operationLogs;
    }

    /**
     * 获取换货类型描述
     */
    private String getExchangeTypeDesc(String exchangeType) {
        if ("1".equals(exchangeType)) {
            return EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getDesc();
        } else if ("2".equals(exchangeType)) {
            return EquityBarterExchangeTypeEnum.EQUITY.getDesc();
        }
        return "未知";
    }

    /**
     * 清除相关缓存
     */
    private void clearRelatedCache(List<StCEquityBarterStrategyItem> itemsToDelete) {
        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            StCEquityBarterStrategy strategy = strategyMapper.selectById(item.getStCEquityBarterStrategyId());
            if (strategy != null) {
                if (strategy.getCpCShopId() == null) {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
                } else {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + strategy.getCpCShopId());
                }
            }
        }
    }
}
