package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.dto.StCEquityBarterStrategyItemDeleteRequest;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.service.LogCommonService;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterExchangeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ShopTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除服务
 **/
@Slf4j
@Service
public class StCEquityBarterStrategyItemDeleteService {

    @Autowired
    private StCEquityBarterStrategyItemMapper itemMapper;

    @Autowired
    private StCEquityBarterStrategyMapper strategyMapper;

    @Autowired
    private LogCommonService logCommonService;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    /**
     * 删除对等换货策略明细
     *
     * @param request 删除请求参数
     * @param user    操作用户
     * @return 删除结果
     */
    public ValueHolderV14<Void> deleteStrategyItems(StCEquityBarterStrategyItemDeleteRequest request, User user) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "对等明细删除完毕，请检查数据！");
        try {
            // 参数校验
            validateDeleteRequest(request);
            // 查询要删除的明细记录
            List<StCEquityBarterStrategyItem> itemsToDelete = findItemsToDeleteDirect(request);

            if (CollectionUtils.isEmpty(itemsToDelete)) {
                return new ValueHolderV14<>(-1, "未找到符合条件的对等换货策略明细");
            }

            // 记录删除日志
            List<OcBOperationLog> operationLogs = buildDeleteLogs(itemsToDelete, user);

            // 执行删除
            List<Long> itemIds = itemsToDelete.stream().map(StCEquityBarterStrategyItem::getId).collect(Collectors.toList());
            itemMapper.deleteBatchIds(itemIds);

            // 批量保存日志
            if (CollectionUtils.isNotEmpty(operationLogs)) {
                logCommonService.batchInsertLog(operationLogs);
            }

            // 清除相关缓存
            clearRelatedCache(itemsToDelete);

            log.info("成功删除对等换货策略明细，删除数量：{}", itemsToDelete.size());
            return new ValueHolderV14<>(0, "删除成功", "删除了 " + itemsToDelete.size() + " 条明细记录");

        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 校验删除请求参数
     */
    private void validateDeleteRequest(StCEquityBarterStrategyItemDeleteRequest request) {
        // 校验店铺类型
        Integer shopType = request.getShopType();
        if (shopType == null) {
            throw new NDSException("店铺类型不能为空！");
        }
        if (!ShopTypeEnum.isValidCode(shopType)) {
            throw new NDSException("店铺类型参数错误！");
        }

        // 当店铺类型为指定店铺时，店铺ID列表必填
        if (ShopTypeEnum.SPECIFIED_SHOP.getCode().equals(shopType) && CollectionUtils.isEmpty(request.getShopIds())) {
            throw new NDSException("当店铺类型为指定店铺时，店铺不能为空！");
        }

        // 校验换货商品ID
        if (request.getExchangeSkuId() == null) {
            throw new NDSException("换货商品不能为空！");
        }

        // 校验换货数量
        if (request.getExchangeQty() == null) {
            throw new NDSException("换货数量不能为空！");
        }

        // 校验对等商品ID
        if (request.getEquitySkuId() == null) {
            throw new NDSException("对等商品ID不能为空！");
        }

        // 校验对等数量
        if (request.getEquityQty() == null) {
            throw new NDSException("对等数量不能为空！");
        }
    }

    /**
     * 查找要删除的明细记录（直接参数版本）
     */
    private List<StCEquityBarterStrategyItem> findItemsToDeleteDirect(Integer shopType, List<Long> shopIds,
                                                                      Long exchangeSkuId, BigDecimal exchangeQty,
                                                                      Long equitySkuId, BigDecimal equityQty) {
        QueryWrapper<StCEquityBarterStrategyItem> queryWrapper = new QueryWrapper<>();

        // 根据换货商品ID、换货数量、对等商品ID、对等数量查询
        queryWrapper.lambda()
                .eq(StCEquityBarterStrategyItem::getPsCSkuId, exchangeSkuId)
                .eq(StCEquityBarterStrategyItem::getQty, exchangeQty)
                .eq(StCEquityBarterStrategyItem::getEquitySkuId, equitySkuId)
                .eq(StCEquityBarterStrategyItem::getEquityQty, equityQty);

        List<StCEquityBarterStrategyItem> allItems = itemMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(allItems)) {
            return new ArrayList<>();
        }

        // 根据店铺类型进一步筛选
        List<StCEquityBarterStrategyItem> filteredItems = new ArrayList<>();

        for (StCEquityBarterStrategyItem item : allItems) {
            StCEquityBarterStrategy strategy = strategyMapper.selectById(item.getStCEquityBarterStrategyId());
            if (strategy == null) {
                continue;
            }

            // 店铺类型：1-指定店铺；2-所选指定店铺
            if (shopType == 1) {
                // 指定店铺：策略类型为2且店铺ID在指定列表中
                if ("2".equals(strategy.getType()) && shopIds.contains(strategy.getCpCShopId())) {
                    filteredItems.add(item);
                }
            } else if (shopType == 2) {
                // 所选指定店铺：策略类型为2的所有记录
                if ("2".equals(strategy.getType())) {
                    filteredItems.add(item);
                }
            }
        }

        return filteredItems;
    }

    /**
     * 参数校验（JSONObject版本）
     */
    private void validateRequest(JSONObject param) {
        Integer shopType = param.getInteger("shopType");
        if (shopType == null) {
            throw new NDSException("店铺类型不能为空");
        }

        if (shopType == 1 && (param.getJSONArray("shopIds") == null || param.getJSONArray("shopIds").isEmpty())) {
            throw new NDSException("当店铺类型为指定店铺时，店铺ID列表不能为空");
        }

        if (param.getLong("exchangeSkuId") == null) {
            throw new NDSException("换货商品ID不能为空");
        }

        if (param.getBigDecimal("exchangeQty") == null) {
            throw new NDSException("换货数量不能为空");
        }

        if (param.getLong("equitySkuId") == null) {
            throw new NDSException("对等商品ID不能为空");
        }

        if (param.getBigDecimal("equityQty") == null) {
            throw new NDSException("对等数量不能为空");
        }
    }

    /**
     * 查找要删除的明细记录（JSONObject版本）
     */
    private List<StCEquityBarterStrategyItem> findItemsToDelete(JSONObject param) {
        QueryWrapper<StCEquityBarterStrategyItem> queryWrapper = new QueryWrapper<>();

        // 根据换货商品ID、换货数量、对等商品ID、对等数量查询
        queryWrapper.lambda()
                .eq(StCEquityBarterStrategyItem::getPsCSkuId, param.getLong("exchangeSkuId"))
                .eq(StCEquityBarterStrategyItem::getQty, param.getBigDecimal("exchangeQty"))
                .eq(StCEquityBarterStrategyItem::getEquitySkuId, param.getLong("equitySkuId"))
                .eq(StCEquityBarterStrategyItem::getEquityQty, param.getBigDecimal("equityQty"));

        List<StCEquityBarterStrategyItem> allItems = itemMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(allItems)) {
            return new ArrayList<>();
        }

        // 根据店铺类型进一步筛选
        List<StCEquityBarterStrategyItem> filteredItems = new ArrayList<>();
        Integer shopType = param.getInteger("shopType");
        List<Long> shopIds = new ArrayList<>();
        if (param.getJSONArray("shopIds") != null) {
            for (Object obj : param.getJSONArray("shopIds")) {
                shopIds.add(Long.valueOf(obj.toString()));
            }
        }

        for (StCEquityBarterStrategyItem item : allItems) {
            StCEquityBarterStrategy strategy = strategyMapper.selectById(item.getStCEquityBarterStrategyId());
            if (strategy == null) {
                continue;
            }

            // 店铺类型：1-指定店铺；2-所选指定店铺
            if (shopType == 1) {
                // 指定店铺：策略类型为2且店铺ID在指定列表中
                if ("2".equals(strategy.getType()) && shopIds.contains(strategy.getCpCShopId())) {
                    filteredItems.add(item);
                }
            } else if (shopType == 2) {
                // 所选指定店铺：策略类型为2的所有记录
                if ("2".equals(strategy.getType())) {
                    filteredItems.add(item);
                }
            }
        }

        return filteredItems;
    }

    /**
     * 构建删除日志
     */
    private List<OcBOperationLog> buildDeleteLogs(List<StCEquityBarterStrategyItem> itemsToDelete, User user) {
        List<OcBOperationLog> operationLogs = new ArrayList<>();

        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            // 构建日志内容：[换货商品编码],[换货商品名称],[换货数量],[对等商品编码],[对等商品名称],[对等数量],[换货类型描述]
            StringBuilder logContent = new StringBuilder();
            logContent.append("[").append(item.getPsCSkuCode()).append("]")
                    .append(",[").append(item.getPsCSkuName()).append("]")
                    .append(",[").append(item.getQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(item.getEquitySkuCode()).append("]")
                    .append(",[").append(item.getEquitySkuName()).append("]")
                    .append(",[").append(item.getEquityQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(getExchangeTypeDesc(item.getExchangeType())).append("]");

            OcBOperationLog operationLog = logCommonService.getOperationLog(
                    "ST_C_EQUITY_BARTER_STRATEGY_ITEM",
                    "DEL",
                    item.getStCEquityBarterStrategyId(),
                    "对等换货策略明细",
                    "删除对等换货策略明细",
                    logContent.toString(),
                    "删除",
                    user
            );

            operationLogs.add(operationLog);
        }

        return operationLogs;
    }

    /**
     * 获取换货类型描述
     */
    private String getExchangeTypeDesc(String exchangeType) {
        if ("1".equals(exchangeType)) {
            return EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getDesc();
        } else if ("2".equals(exchangeType)) {
            return EquityBarterExchangeTypeEnum.EQUITY.getDesc();
        }
        return "未知";
    }

    /**
     * 清除相关缓存
     */
    private void clearRelatedCache(List<StCEquityBarterStrategyItem> itemsToDelete) {
        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            StCEquityBarterStrategy strategy = strategyMapper.selectById(item.getStCEquityBarterStrategyId());
            if (strategy != null) {
                if (strategy.getCpCShopId() == null) {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
                } else {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + strategy.getCpCShopId());
                }
            }
        }
    }
}
