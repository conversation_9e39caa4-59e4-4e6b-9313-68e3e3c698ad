package com.jackrain.nea.st.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.dto.StCEquityBarterStrategyItemDeleteRequest;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.service.LogCommonService;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterExchangeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ShopTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.ps.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.RpcPsService;
import com.jackrain.nea.st.service.StRedisKey;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除服务
 **/
@Slf4j
@Service
public class StCEquityBarterStrategyItemDeleteService {

    @Autowired
    private StCEquityBarterStrategyItemMapper itemMapper;

    @Autowired
    private StCEquityBarterStrategyMapper strategyMapper;

    @Autowired
    private LogCommonService logCommonService;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    @Autowired
    private RpcPsService rpcPsService;

    /**
     * 删除对等换货策略明细
     *
     * @param request 删除请求参数
     * @param user    操作用户
     * @return 删除结果
     */
    public ValueHolderV14<Void> deleteStrategyItems(StCEquityBarterStrategyItemDeleteRequest request, User user) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "对等明细删除完毕，请检查数据！");
        try {
            // 参数校验
            validateDeleteRequest(request);
            // 第一步：先查询主表，获取符合条件的策略列表
            List<StCEquityBarterStrategy> strategies = findStrategies(request);
            if (CollectionUtils.isEmpty(strategies)) {
                throw new NDSException("未找到符合条件的对等换货策略！");
            }

            // 提取策略ID列表
            List<Long> strategyIds = strategies.stream()
                    .map(StCEquityBarterStrategy::getId)
                    .collect(Collectors.toList());
            // 第二步：根据策略ID列表和前端入参查询策略明细
            List<StCEquityBarterStrategyItem> itemsToDelete = findStrategyItems(request, strategyIds);

            if (CollectionUtils.isEmpty(itemsToDelete)) {
                throw new NDSException("未找到符合条件的对等换货策略明细！");
            }
            StCEquityBarterStrategyItemDeleteService bean =
                    ApplicationContextHandle.getBean(StCEquityBarterStrategyItemDeleteService.class);
            bean.delAndSaveLog(user, itemsToDelete, strategies);
            v14.setMessage("对等明细删除完毕共" + itemsToDelete.size() + "条，请检查数据！");
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("删除失败：" + e.getMessage());
        }
        return v14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delAndSaveLog(User user, List<StCEquityBarterStrategyItem> itemsToDelete,
                              List<StCEquityBarterStrategy> strategies) {
        // 记录删除日志
        List<OcBOperationLog> operationLogs = buildDeleteLogs(itemsToDelete, user);
        // 执行删除
        List<Long> itemIds = itemsToDelete.stream().map(StCEquityBarterStrategyItem::getId).collect(Collectors.toList());
        itemMapper.deleteBatchIds(itemIds);
        // 批量保存日志
        if (CollectionUtils.isNotEmpty(operationLogs)) {
            logCommonService.batchInsertLog(operationLogs);
        }
        // 清除相关缓存（使用主表策略列表）
        clearRelatedCache(strategies);
    }

    /**
     * 校验删除请求参数
     */
    private void validateDeleteRequest(StCEquityBarterStrategyItemDeleteRequest request) {
        // 校验店铺类型
        Integer shopType = request.getShopType();
        if (shopType == null) {
            throw new NDSException("店铺类型不能为空！");
        }
        if (!ShopTypeEnum.isValidCode(shopType)) {
            throw new NDSException("店铺类型参数错误！");
        }

        // 当店铺类型为指定店铺时，店铺ID列表必填
        if (ShopTypeEnum.SPECIFIED_SHOP.getCode().equals(shopType) && CollectionUtils.isEmpty(request.getShopIds())) {
            throw new NDSException("当店铺类型为指定店铺时，店铺不能为空！");
        }

        // 校验换货商品ID
        if (request.getExchangeSkuId() == null) {
            throw new NDSException("换货商品不能为空！");
        }

        // 校验换货数量
        if (request.getExchangeQty() == null) {
            throw new NDSException("换货数量不能为空！");
        }

        // 校验对等商品ID
        if (request.getEquitySkuId() == null) {
            throw new NDSException("对等商品ID不能为空！");
        }

        // 校验对等数量
        if (request.getEquityQty() == null) {
            throw new NDSException("对等数量不能为空！");
        }
    }

    /**
     * 查找符合条件的策略列表
     */
    private List<StCEquityBarterStrategy> findStrategies(StCEquityBarterStrategyItemDeleteRequest request) {
        QueryWrapper<StCEquityBarterStrategy> strategyQueryWrapper = new QueryWrapper<>();

        // 限制只查询指定店铺的策略（type=2）
        strategyQueryWrapper.lambda().eq(StCEquityBarterStrategy::getType, "2");

        // 根据店铺类型进一步限制
        if (ShopTypeEnum.SPECIFIED_SHOP.getCode().equals(request.getShopType())) {
            // 指定店铺：限制店铺ID在指定列表中
            if (CollectionUtils.isNotEmpty(request.getShopIds())) {
                strategyQueryWrapper.lambda().in(StCEquityBarterStrategy::getCpCShopId, request.getShopIds());
            }
        }
        // 所选指定店铺：不需要额外限制，只要type=2即可

        return strategyMapper.selectList(strategyQueryWrapper);
    }

    /**
     * 根据策略ID列表和前端入参查询策略明细
     */
    private List<StCEquityBarterStrategyItem> findStrategyItems(StCEquityBarterStrategyItemDeleteRequest request, List<Long> strategyIds) {
        QueryWrapper<StCEquityBarterStrategyItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda()
                .in(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, strategyIds)
                .eq(StCEquityBarterStrategyItem::getPsCSkuId, request.getExchangeSkuId())
                .eq(StCEquityBarterStrategyItem::getQty, request.getExchangeQty())
                .eq(StCEquityBarterStrategyItem::getEquitySkuId, request.getEquitySkuId())
                .eq(StCEquityBarterStrategyItem::getEquityQty, request.getEquityQty());

        return itemMapper.selectList(itemQueryWrapper);
    }

    /**
     * 构建删除日志（优化版本）
     */
    private List<OcBOperationLog> buildDeleteLogs(List<StCEquityBarterStrategyItem> itemsToDelete, User user) {
        List<OcBOperationLog> operationLogs = new ArrayList<>();

        // 收集所有需要查询的SKU ID
        Set<Long> allSkuIds = new HashSet<>();
        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            allSkuIds.add(item.getPsCSkuId());
            allSkuIds.add(item.getEquitySkuId());
        }

        // 批量查询SKU信息
        Map<Long, PsCSku> skuMap = rpcPsService.querySkuByIds(allSkuIds);

        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            // 获取换货商品信息
            PsCSku exchangeSku = skuMap.get(item.getPsCSkuId());
            String exchangeSkuCode = exchangeSku != null ? exchangeSku.getEcode() : "";
            String exchangeSkuName = exchangeSku != null ? exchangeSku.getName() : "";

            // 获取对等商品信息
            PsCSku equitySku = skuMap.get(item.getEquitySkuId());
            String equitySkuCode = equitySku != null ? equitySku.getEcode() : "";
            String equitySkuName = equitySku != null ? equitySku.getName() : "";

            // 构建日志内容：[换货商品编码],[换货商品名称],[换货数量],[对等商品编码],[对等商品名称],[对等数量],[换货类型描述]
            StringBuilder logContent = new StringBuilder();
            logContent.append("[").append(exchangeSkuCode).append("]")
                    .append(",[").append(exchangeSkuName).append("]")
                    .append(",[").append(item.getQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(equitySkuCode).append("]")
                    .append(",[").append(equitySkuName).append("]")
                    .append(",[").append(item.getEquityQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(getExchangeTypeDesc(item.getExchangeType())).append("]");

            OcBOperationLog operationLog = logCommonService.getOperationLog(
                    "ST_C_EQUITY_BARTER_STRATEGY_ITEM",
                    "DEL",
                    item.getStCEquityBarterStrategyId(),
                    "对等换货策略明细",
                    "删除对等换货策略明细",
                    logContent.toString(),
                    "删除",
                    user
            );

            operationLogs.add(operationLog);
        }

        return operationLogs;
    }

    /**
     * 获取换货类型描述
     */
    private String getExchangeTypeDesc(String exchangeType) {
        if ("1".equals(exchangeType)) {
            return EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getDesc();
        } else if ("2".equals(exchangeType)) {
            return EquityBarterExchangeTypeEnum.EQUITY.getDesc();
        }
        return "未知";
    }

    /**
     * 清除相关缓存
     */
    private void clearRelatedCache(List<StCEquityBarterStrategy> strategies) {
        for (StCEquityBarterStrategy strategy : strategies) {
            if (strategy.getCpCShopId() == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + strategy.getCpCShopId());
            }
        }
    }
}
