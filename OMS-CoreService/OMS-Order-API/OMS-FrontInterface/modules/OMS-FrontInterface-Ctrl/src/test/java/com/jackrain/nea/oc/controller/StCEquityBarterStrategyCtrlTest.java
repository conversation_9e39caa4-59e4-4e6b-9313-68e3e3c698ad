package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.vo.StCEquityBarterStrategyItemDeleteRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略控制器测试
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class StCEquityBarterStrategyCtrlTest {

    @Test
    public void testDeleteRequestSerialization() {
        StCEquityBarterStrategyItemDeleteRequest request = new StCEquityBarterStrategyItemDeleteRequest();
        request.setShopScope(1);
        request.setShopIds(Arrays.asList(1001L, 1002L));
        request.setExchangeSkuId(110101101101L);
        request.setExchangeQty(new BigDecimal("2.0000"));
        request.setEquitySkuId(110100000030L);
        request.setEquityQty(new BigDecimal("1.0000"));

        String json = JSON.toJSONString(request);
        System.out.println("删除请求JSON: " + json);

        StCEquityBarterStrategyItemDeleteRequest parsed = JSON.parseObject(json, StCEquityBarterStrategyItemDeleteRequest.class);
        System.out.println("解析后的请求: " + JSON.toJSONString(parsed));
    }
}
