package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略控制器测试
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class StCEquityBarterStrategyCtrlTest {

    @Test
    public void testDeleteRequestSerialization() {
        JSONObject param = new JSONObject();
        param.put("shopScope", 1);
        param.put("shopIds", Arrays.asList(1001L, 1002L));
        param.put("exchangeSkuId", 110101101101L);
        param.put("exchangeQty", new BigDecimal("2.0000"));
        param.put("equitySkuId", 110100000030L);
        param.put("equityQty", new BigDecimal("1.0000"));

        String json = JSON.toJSONString(param);
        System.out.println("删除请求JSON: " + json);

        JSONObject parsed = JSON.parseObject(json);
        System.out.println("解析后的请求: " + JSON.toJSONString(parsed));

        // 测试参数获取
        System.out.println("shopScope: " + parsed.getInteger("shopScope"));
        System.out.println("exchangeSkuId: " + parsed.getLong("exchangeSkuId"));
        System.out.println("exchangeQty: " + parsed.getBigDecimal("exchangeQty"));
    }
}
