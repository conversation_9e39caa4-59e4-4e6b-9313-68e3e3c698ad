package com.jackrain.nea.oc.oms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 删除对等换货策略明细请求
 **/
@Data
@ApiModel("删除对等换货策略明细请求")
public class StCEquityBarterStrategyItemDeleteRequest {

    @ApiModelProperty(value = "店铺范围：1-指定店铺；2-所选指定店铺", required = true)
    @NotNull(message = "店铺范围不能为空")
    private Integer shopScope;

    @ApiModelProperty(value = "店铺id的list(当店铺范围为指定店铺时必填)")
    private List<Long> shopIds;

    @ApiModelProperty(value = "换货商品id", required = true)
    @NotNull(message = "换货商品id不能为空")
    private Long exchangeSkuId;

    @ApiModelProperty(value = "换货数量", required = true)
    @NotNull(message = "换货数量不能为空")
    private BigDecimal exchangeQty;

    @ApiModelProperty(value = "对等商品id", required = true)
    @NotNull(message = "对等商品id不能为空")
    private Long equitySkuId;

    @ApiModelProperty(value = "对等数量", required = true)
    @NotNull(message = "对等数量不能为空")
    private BigDecimal equityQty;
}
