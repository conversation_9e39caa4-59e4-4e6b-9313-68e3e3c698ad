# 对等换货策略明细删除接口说明

## 接口概述

本接口用于删除对等换货策略明细，支持根据店铺范围、换货商品、对等商品等条件精确删除对应的明细记录，并记录详细的操作日志。

## 接口信息

- **接口路径**: `/api/cs/oc/oms/stCEquityBarterStrategy/item/delete`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

### StCEquityBarterStrategyItemDeleteRequest 参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| shopType | Integer | 是 | 店铺类型：1-指定店铺；2-所选指定店铺 |
| shopIds | Array<Long> | 条件必填 | 店铺id的数组(当店铺类型为指定店铺时必填) |
| exchangeSkuId | Long | 是 | 换货商品id |
| exchangeQty | BigDecimal | 是 | 换货数量 |
| equitySkuId | Long | 是 | 对等商品id |
| equityQty | BigDecimal | 是 | 对等数量 |

### 参数说明

1. **shopType**:
   - `1`: 指定店铺 - 只删除指定店铺ID列表中的策略明细
   - `2`: 所选指定店铺 - 删除所有指定店铺类型的策略明细

2. **shopIds**: 当shopType=1时必填，指定要删除的店铺ID列表

3. **exchangeSkuId**: 换货商品的SKU ID

4. **exchangeQty**: 换货数量，支持小数

5. **equitySkuId**: 对等商品的SKU ID

6. **equityQty**: 对等数量，支持小数

## 请求示例

```json
{
    "shopType": 1,
    "shopIds": [1001, 1002],
    "exchangeSkuId": 110101101101,
    "exchangeQty": 2.0000,
    "equitySkuId": 110100000030,
    "equityQty": 1.0000
}
```

## 响应格式

### 成功响应

```json
{
    "code": 0,
    "message": "删除成功",
    "data": "删除了 2 条明细记录"
}
```

### 失败响应

```json
{
    "code": -1,
    "message": "未找到符合条件的对等换货策略明细",
    "data": null
}
```

## 业务逻辑

### 删除逻辑

1. **参数校验**: 验证必填参数和业务规则
2. **条件查询**: 根据换货商品ID、换货数量、对等商品ID、对等数量查询明细
3. **店铺筛选**: 根据店铺范围进一步筛选符合条件的明细
4. **执行删除**: 批量删除符合条件的明细记录
5. **记录日志**: 为每条删除的明细记录操作日志
6. **清除缓存**: 清除相关的Redis缓存

### 日志格式

删除操作会在`oc_b_operation_log`表中记录日志，日志内容格式为：

```
[换货商品编码],[换货商品名称],[换货数量],[对等商品编码],[对等商品名称],[对等数量],[换货类型描述]
```

示例：
```
[110101101101],[认养一头牛纯奶200ml12入二提装],[2.0000],[110100000030],[认养一头牛纯奶250ML柳叶包12入二提装],[1.0000],[多换少]
```

### 换货类型说明

- `1`: 多换少
- `2`: 对等置换

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 失败，具体错误信息见message字段 |

## 常见错误

1. **参数校验失败**
   - 店铺类型不能为空
   - 店铺类型参数错误，只能为1(指定店铺)或2(所选指定店铺)
   - 当店铺类型为指定店铺时，店铺ID列表不能为空
   - 换货商品ID不能为空
   - 换货数量不能为空
   - 对等商品ID不能为空
   - 对等数量不能为空

2. **业务逻辑错误**
   - 未找到符合条件的对等换货策略明细
   - 数据库操作异常

## 注意事项

1. 删除操作不可逆，请谨慎操作
2. 删除会清除相关的Redis缓存
3. 所有删除操作都会记录详细的操作日志
4. 接口支持事务，如果删除过程中出现异常会自动回滚

## 使用示例

### cURL 示例

```bash
curl -X POST \
  http://your-domain/api/cs/oc/oms/stCEquityBarterStrategy/item/delete \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-token' \
  -d '{
    "shopScope": 1,
    "shopIds": [1001, 1002],
    "exchangeSkuId": 110101101101,
    "exchangeQty": 2.0000,
    "equitySkuId": 110100000030,
    "equityQty": 1.0000
  }'
```

### JavaScript 示例

```javascript
const deleteRequest = {
    shopType: 1,
    shopIds: [1001, 1002],
    exchangeSkuId: 110101101101,
    exchangeQty: 2.0000,
    equitySkuId: 110100000030,
    equityQty: 1.0000
};

fetch('/api/cs/oc/oms/stCEquityBarterStrategy/item/delete', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-token'
    },
    body: JSON.stringify(deleteRequest)
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        console.log('删除成功:', data.data);
    } else {
        console.error('删除失败:', data.message);
    }
});
```
