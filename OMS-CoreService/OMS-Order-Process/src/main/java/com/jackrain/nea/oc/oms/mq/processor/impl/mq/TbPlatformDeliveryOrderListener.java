package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.util.OcBOrderDeliveryFailUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 淘宝平台发货接口接收云枢纽回传MQ
 *
 * @author: 胡林洋
 * @since: 2019/5/23
 * create at : 2019/5/10 10:14
 */
@Slf4j
@RocketMqMessageListener(name = "TbPlatformDeliveryOrderListener", type = MqTypeEnum.CLOUD)
public class TbPlatformDeliveryOrderListener implements BaseMessageListener {
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private OcBOrderDeliveryFailUtil deliveryFailUtil;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        User user = SystemUserResource.getRootUser();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("淘宝平台发货接口接收云枢纽回传MQ.MsgBody: {}", messageKey), messageBody);
            }
            JSONArray objectArray = JSON.parseArray(messageBody);
            JSONObject object = JSON.parseObject(objectArray.get(0).toString());

            boolean result = object.getBoolean("issuccess");
            Long id = object.getLong("ID");
            String tid = object.getString("TID");
            String expressCode = object.getString("OUT_SID");
            String msg = object.getString("error_msg");
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(id);
            String logMsg = "订单" + id + "(平台单号=" + tid + ";物流单号=" + expressCode + ")TB平台发货MQ结果" + (result ? "成功" : msg);
            /**
             * ☆1、无论失败成功，添加日志
             */
            omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, msg, user);
            if (!result) {
                /**
                 * ☆2、失败更新相关单据，直接提交消息
                 */
                orderPlatformDeliveryService.failUpdate(expressCode, msg, ocBOrder);
                deliveryFailUtil.addOcBOrderDeliveryFail(ocBOrder);
                return;
            }


            /**
             * ☆3、更新明细发货状态，更新发货信息列表，校验是否更新主表为平台发货
             */
            orderPlatformDeliveryService.updateSendGoodsStatusByExpressCode(id, expressCode, 1L);
            ocBOrderItemMapper.updateItemsWhenDeliverySuccess(id, tid);
            List<String> tidList = ocBOrderItemMapper.selectCanPlatformItemTidList(id);
            tidList.remove(tid);
            if (tidList.size() > 0) {
                //先不做处理，只更新明细发货状态，等后面的tid发货消息过来一起更新主表状态
            } else {
                //直接更新主表状态为平台发货
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setIsForce(1L);
                order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                //平台发货时间赋值
                order.setPlatformDeliveryTime(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.PLATFORM_DELIVERY_TIME,new Date(),order.getId(),SystemUserResource.getRootUser());
                //作废退单
                orderPlatformDeliveryService.updateOrder(order);
                orderPlatformDeliveryService.updateReturnOrder(ocBOrder);
            }

        } catch (Exception e) {
            log.error(LogUtil.format("淘宝平台发货失败.异常:{}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
