package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create at : 2020-06-23 18:08
 * @description: 退供单转调拨单，SG结果回传
 *
 * 无用废弃
 */

// fixme tag:sg_to_oms_transfer_result_verify_postback
@Deprecated
@Component
@Slf4j
//@RocketMqMessageListener(name = "TransferToOmsMqProcessorImpl", type = MqTypeEnum.DEFAULT, groupIdSuffix = "SG4")
public class TransferToOmsMqProcessorImpl {

    @Autowired
    protected IpVipReturnOrderService ipVipReturnOrderService;

    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.debug(LogUtil.format("退供单转调拨单，SG结果回传: {}", messageKey), messageBody);
        JSONObject messageJson = JSONObject.parseObject(messageBody);
        String orderNo = messageJson.getString("returnOrderCode");
        String transferStatus = messageJson.getString("transferStatus");
        String resultMsg = messageJson.getString("resultMsg");
        if (transferStatus.equalsIgnoreCase(TransferOrderStatus.TRANSFERRED.toKeyword())) {
            ipVipReturnOrderService.updateTransferStatus("转换成功！",
                    orderNo, TransferOrderStatus.TRANSFERRED.toInteger());
        } else {
            ipVipReturnOrderService.updateTransferStatus("转换失败，保存退供单失败！失败原因：" + resultMsg,
                    orderNo, TransferOrderStatus.TRANSFERFAIL.toInteger());
        }
    }
}
