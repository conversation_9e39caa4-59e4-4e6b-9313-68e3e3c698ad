package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItemLog;
import com.burgeon.r3.sg.store.model.result.out.SgBOutNoticesBatchSaveAndWMSResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.ipcs.model.LabelRequirementsItemRequest;
import com.jackrain.nea.ipcs.model.LabelRequirementsRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToWingDeliveryTaskMapper;
import com.jackrain.nea.oc.oms.model.OcBOrderItemLabelRequirementInfo;
import com.jackrain.nea.oc.oms.model.UpdateInDistributionMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderUpdateInDistributionService;
import com.jackrain.nea.oc.oms.services.OrderExceptionTagService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.retail.service.RetailNotifyService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 订单“更新配货中”MQ消息处理器
 *
 * @author: 胡林洋
 * @since: 2019-05-09
 * create at : 2019-05-09 14:08
 */
// fixme tag:sg_to_oms_wms_out_creat_receipt
@Component
@Slf4j
//@RocketMqMessageListener(name = "UpdateInDistributionMqProcessorImpl", type = MqTypeEnum.DEFAULT, groupIdSuffix = "SG1")
public class UpdateInDistributionMqProcessorImpl {

    @Value("${oms.order.toDRP.max.failNumber:5}")
    private Integer failNumber;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderUpdateInDistributionService omsOrderUpdateInDistributionService;
    @Autowired
    private WmsUserCreateUtil wmsUserCreateUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private RetailNotifyService retailNotifyService;
    @Autowired
    private OmsWmsTaskService wmsTaskService;
    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBToWingDeliveryTaskMapper ocBToWingDeliveryTaskMapper;

    @Autowired
    private OrderExceptionTagService orderExceptionTagService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private DefaultProducerSend defaultProducerSend;


    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object o) {
        try {
            User user = wmsUserCreateUtil.initWmsUser();

            JSONObject parseObject = JSONObject.parseObject(messageBody);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" 更新配货中MQ消息处理器 {},", messageKey), messageBody);
            }
            //斯凯奇项目 订单中心-库存中心-DRP(Wing)-WMS 需两次回传  第一次 type=1  第二次 type=2
            Integer type = parseObject.getInteger("type");
            String body = parseObject.getString("body");

            //先保存出库通知单
            JSONArray bodyArray = JSON.parseArray(body);
            if (CollectionUtils.isNotEmpty(bodyArray)) {
                for (Object obj : bodyArray) {
                    JSONObject object = (JSONObject) obj;
                    String noticesBillNo = object.getString("noticesBillNo");
                    Long noticesBillId = object.getLong("id");
                    Long orderId = object.getLong("orderId");
                    Long sourceBilId = object.getLong("sourceBilId");
                    Date outWingToWmsTime = object.getDate("outWingToWmsTime");
                    Date outWmsReceiveTime = object.getDate("outWmsReceiveTime");

                    orderId = orderId == null ? sourceBilId : orderId;
                    if (StringUtils.isNotBlank(noticesBillNo)
                            && orderId != null && orderId > 0L) {
                        OcBOrder update = new OcBOrder();
                        update.setId(orderId);
                        update.setSgBOutBillNo(noticesBillNo);
                        update.setSgBOutBillId(noticesBillId);
                        update.setReserveVarchar04(noticesBillNo);
                        update.setOutWmsReceiveTime(outWmsReceiveTime);
                        ocBOrderMapper.updateById(update);
                    }
                }
            }
            if (type == null || type == 2) {
                List<UpdateInDistributionMqInfo> mqUpdateInDistributionList = JSON.parseArray(body, UpdateInDistributionMqInfo.class);
                getMqProcessResult(user, mqUpdateInDistributionList);
            } else {
                List<SgBOutNoticesBatchSaveAndWMSResult> sgOutNoticesResults = JSON.parseArray(body, SgBOutNoticesBatchSaveAndWMSResult.class);
                this.handleOrder(sgOutNoticesResults, user);
                return;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("UpdateInDistributionMqProcessorImpl.StartProcess Error{}", messageKey), Throwables.getStackTraceAsString(ex));
            throw new MqException(ex);
        }
    }

    private void getMqProcessResult(User user, List<UpdateInDistributionMqInfo> mqUpdateInDistributionList) {
        if (CollectionUtils.isEmpty(mqUpdateInDistributionList)) {
            log.info("UpdateInDistributionMqInfo解析为null");
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" Start Process Consumed Number={}"), mqUpdateInDistributionList.size());
        }
        List<Long> successOrderIds = new ArrayList<>();
        List<Long> errorStatusorderIds = new ArrayList<>();
        for (UpdateInDistributionMqInfo outOrderMqInfo : mqUpdateInDistributionList) {
            int code = outOrderMqInfo.getCode();
            Long orderId = outOrderMqInfo.getOrderId();
            OcBOrder ocBorder = omsOrderService.selectOrderInfo(orderId);
            if (ocBorder != null) {
                OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                ocBOrderRelation.setOrderInfo(ocBorder);
                int orderStatus = ocBorder.getOrderStatus();
                if (code == 0 || code == 19 || code == 2) {
                    if (orderStatus == OmsOrderStatus.CHECKED.toInteger()
                            || orderStatus == OmsOrderStatus.PENDING_WMS.toInteger()
                            || orderStatus == OmsOrderStatus.IN_DISTRIBUTION.toInteger()) {
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("omsOrderUpdateInDistributionService.updateInDistribution", orderId));
                        }
                        successOrderIds.add(orderId);
                        try {
                            sendLabelRequirements(ocBorder);
                        } catch (Exception e) {
                            log.info(LogUtil.format("增值服务下推失败，订单号：{}", orderId), e);
                        }
                    } else {
                        // 状态为不为 已审核、传wms中的订单，调用作废出库通知单并撤回wms服务
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("接收到mq，但由于订单状态不为“已审核”或者“传WMS中”调用更新配货中服务失败", orderId));
                        }
                        errorStatusorderIds.add(orderId);
                    }
                } else if (code == 20 || code == -1) {
                    if (orderStatus != OmsOrderStatus.PENDING_WMS.toInteger()) {
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("接收到mq，但mq返回传WMS接口状态为“-1”失败，由于订单状态不是传wms中", orderId));
                        }
                    }
                    String wmsFailReason = outOrderMqInfo.getFlag();
                    omsOrderLogService.addUserOrderLog(orderId,
                            ocBorder.getBillNo(), OrderLogTypeEnum.DISTRIBUTION_UPDATE.getKey(), ocBorder.getId() + "接收到mq，传WMS状态为【-1】失败原因为：" + wmsFailReason, null, "", null);
                    //将订单状态改为“已审核”
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("接收到mq，但mq返回传WMS接口状态为-1失败", orderId));
                    }
                    //防止订单状态已经为，“配货中“或”仓库发货“或”平台发货“时，再接收到传wms失败的mq，再将改为已审核。
                    if (orderStatus == OmsOrderStatus.PENDING_WMS.toInteger() && !(orderStatus == OmsOrderStatus.DELIVERED.toInteger() || orderStatus == OmsOrderStatus.PLATFORM_DELIVERY.toInteger() || orderStatus == OmsOrderStatus.IN_DISTRIBUTION.toInteger())) {
                        updateOrderStatusPushES(orderId);
                    }

                    OcBOrder update = new OcBOrder();
                    update.setId(ocBorder.getId());
                    String msg = "接收到mq，传WMS状态为【-1】失败原因为：" + wmsFailReason;
                    update.setExceptionExplain(ocBorder.getExceptionExplain());
                    orderExceptionTagService.checkMateException(update,"接收到mq，传WMS状态为【-1】失败原因为：" + msg, OcBOrderConst.ORDER_TOWMS);
                    ocBOrderMapper.updateById(update);
                }
            } else {
                log.error(LogUtil.format("接收到mq，但未在数据库中查询到此订单", orderId));
            }
        }
        //状态为不为 已审核、传wms中的订单，调用作废出库通知单并撤回wms服务
        if (CollectionUtils.isNotEmpty(errorStatusorderIds)) {
            List<OcBOrder> errorStatusOrderList = omsOrderService.selectOrderListByIdsList(errorStatusorderIds);
            if (CollectionUtils.isNotEmpty(errorStatusOrderList)) {
                sgRpcService.invoildOutgoingNotice(errorStatusOrderList, user, true);
            }
        }
        if (CollectionUtils.isNotEmpty(successOrderIds)) {
            //循环结束，拿成功的订单id列表，批量更新为“配货中”
            boolean flag = omsOrderUpdateInDistributionService.batchUpdateInDistribution(successOrderIds, user);
            if (flag) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("接收到mq，并且调用批量更新配货中服务成功订单id为：{}"), successOrderIds);
                }
                for (Long orderId : successOrderIds) {
                    OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);
                    OcBOrder orderInfo = orderRelation.getOrderInfo();
                    if (orderInfo != null ) {
                        if (orderInfo.getIsJcorder() == 1) {
                            continue;
                        }
                    // @20210117 批量更新配货中，给每笔单据增加操作日志
                    omsOrderLogService.addUserOrderLog(orderRelation.getOrderInfo().getId(),
                            orderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.DISTRIBUTION_UPDATE.getKey(),
                            orderRelation.getOrderInfo().getId() + "更新配货中服务",
                            null, null, null);

                        Integer platform = orderInfo.getPlatform();
                        if (platform != null && platform.equals(PlatFormEnum.POS.getCode())) {
                            ValueHolder valueHolder = retailNotifyService.notifyRetailByMq(orderRelation, 1);
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("待发货通知线下pos结果：{}", orderId), valueHolder);
                            }
                        }
                        //配货中回写wing发货中间表
                        if (TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderInfo.getSuggestPresinkStatus())) {
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("配货中回写wing发货中间表", orderId));
                            }
                            ocBToWingDeliveryTaskMapper.updateByTid(orderInfo.getSourceCode(), orderInfo.getSgBOutBillNo());
                        }
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("(批量)，orderRelation.getOrderInfo() == null", orderId));
                        }
                    }

                }
                log.info("调用批量更新配货中服务成功订单id为: {}", successOrderIds);
            } else {
                for (Long orderId : successOrderIds) {
                    updateOrderStatusPushES(orderId);
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("接收到mq，调用批量更新配货中服务失败订单id为：{}"), successOrderIds);
                }
            }
        }
    }

    private void sendLabelRequirements(OcBOrder ocBOrder) {
        // 校验是否tob订单
        boolean isToBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
        if (!isToBOrder) {
            return;
        }
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectUnSuccessRefund(ocBOrder.getId());
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            return;
        }

        // 如果ocBOrderItemList中 labelingRequirements都没有值
        List<OcBOrderItem> containsLabelingRequirements = ocBOrderItemList.stream().filter(item -> item.getLabelingRequirements() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(containsLabelingRequirements)) {
            return;
        }
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        if (cpCPhyWarehouse == null) {
            log.error(LogUtil.format("下推增值服务时,实体仓档案表未查询到数据，仓库id：{}", ocBOrder.getCpCPhyWarehouseId()));
            return;
        }
        // sg需要写一个dubbo接口。查询逻辑占用单，逻辑占用单明细，逻辑占用单明细日志表
        SgBStoOutQueryResult sgBStoOutQueryResult = sgRpcService.querySgBStoOutByOrderId(ocBOrder.getId());
        // 如果合并了 则log表应该是有数据的
        List<SgBStoOutItemLog> itemLogList = sgBStoOutQueryResult.getItemLogs();
        Map<Long, List<OcBOrderItemLabelRequirementInfo>> itemAndProduceDateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemLogList)) {
            // 说明是多行进行合并了。取出来 直接跟之前的去匹配
            for (SgBStoOutItemLog itemLog : itemLogList) {
                if (CollectionUtils.isEmpty(itemAndProduceDateMap.get(itemLog.getSourceBillItemId()))) {
                    itemAndProduceDateMap.put(itemLog.getSourceBillItemId(), new ArrayList<>());
                }
                OcBOrderItemLabelRequirementInfo itemLogInfo = new OcBOrderItemLabelRequirementInfo();
                itemLogInfo.setProduceDate(itemLog.getProduceDate());
                itemLogInfo.setQty(itemLog.getQty());
                itemAndProduceDateMap.get(itemLog.getSourceBillItemId()).add(itemLogInfo);
            }
        } else {
            // 如果为空 说明没有合并 取出来逻辑占用单明细的数据
            List<SgBStoOutItem> itemList = sgBStoOutQueryResult.getItems();
            for (SgBStoOutItem item : itemList) {
                if (CollectionUtils.isEmpty(itemAndProduceDateMap.get(item.getSourceBillItemId()))) {
                    itemAndProduceDateMap.put(item.getSourceBillItemId(), new ArrayList<>());
                }
                OcBOrderItemLabelRequirementInfo itemLogInfo = new OcBOrderItemLabelRequirementInfo();
                itemLogInfo.setProduceDate(item.getProduceDate());
                itemLogInfo.setQty(item.getQty());
                itemAndProduceDateMap.get(item.getSourceBillItemId()).add(itemLogInfo);
            }
        }
        List<OcBOrderItemLabelRequirementInfo> ocBOrderItemLabelRequirementInfoList = new ArrayList<>();
        for (OcBOrderItem item : ocBOrderItemList) {
            List<OcBOrderItemLabelRequirementInfo> itemAndProduceDate = itemAndProduceDateMap.get(item.getId());
            for (OcBOrderItemLabelRequirementInfo info : itemAndProduceDate) {
                OcBOrderItemLabelRequirementInfo ocBOrderItemLabelRequirementInfo = new OcBOrderItemLabelRequirementInfo();
                BeanUtils.copyProperties(item, ocBOrderItemLabelRequirementInfo);
                ocBOrderItemLabelRequirementInfo.setProduceDate(info.getProduceDate());
                ocBOrderItemLabelRequirementInfo.setQty(info.getQty());
                ocBOrderItemLabelRequirementInfoList.add(ocBOrderItemLabelRequirementInfo);
            }
        }
        // ocBOrderItemLabelRequirementInfoList 对skucode，producedate，labelrequirment进行分组
        LabelRequirementsRequest labelRequirementsRequest = new LabelRequirementsRequest();
        Map<String, List<OcBOrderItemLabelRequirementInfo>> itemMap = ocBOrderItemLabelRequirementInfoList.stream().collect(Collectors.groupingBy(ocBOrderItemLabelRequirementInfo -> ocBOrderItemLabelRequirementInfo.getPsCSkuEcode() + "-" + ocBOrderItemLabelRequirementInfo.getProduceDate() + "-" + ocBOrderItemLabelRequirementInfo.getLabelingRequirements()));
        List<LabelRequirementsItemRequest> details = new ArrayList<>();
        for (String key : itemMap.keySet()) {
            List<OcBOrderItemLabelRequirementInfo> itemList = itemMap.get(key);
            String[] keyArray = key.split("-");
            String skuCode = keyArray[0];
            String produceDate = keyArray[1];
            String labelRequirement = keyArray[2];
            // 获取OcBOrderItemLabelRequirementInfo里面的qty 并且相加
            int qtySum = 0;
            for (OcBOrderItemLabelRequirementInfo item : itemList) {
                qtySum = qtySum + item.getQty().intValue();
            }
            LabelRequirementsItemRequest detail = new LabelRequirementsItemRequest();
            detail.setItemCode(skuCode);
            detail.setPlanQty(String.valueOf(qtySum));
            detail.setBatchCode(produceDate);
            detail.setOwnerCode(cpCPhyWarehouse.getOwnerCode());
            detail.setServices(labelRequirement);
            detail.setInventoryType("ZP");
            details.add(detail);
        }
        labelRequirementsRequest.setOwnerCode(cpCPhyWarehouse.getOwnerCode());
        labelRequirementsRequest.setWarehouseCode(cpCPhyWarehouse.getWmsWarehouseCode());
        labelRequirementsRequest.setDeliveryOrderCode(ocBOrder.getSgBOutBillNo());
        labelRequirementsRequest.setUdf01(ocBOrder.getOrderSourcePlatformEcode());
        labelRequirementsRequest.setErpTradeNo(ocBOrder.getBillNo());
        labelRequirementsRequest.setSourceOrderCode(ocBOrder.getTid());
        labelRequirementsRequest.setStorageLocation(cpCPhyWarehouse.getEcode());
        labelRequirementsRequest.setDetails(details);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("method", "taobao.qimen.label.requirements.create");
        jsonObject.put("data", JSONUtil.toJsonStr(labelRequirementsRequest));
        String msgKey = UUID.randomUUID().toString();
        defaultProducerSend.sendTopic("R3_OMS_LABEL_REQUIREMENTS_IPCS", "label_requirements_to_wms", jsonObject.toJSONString(), msgKey);
    }

    /**
     * 更新主表订单状态,推ES
     *
     * @param orderId 订单信息
     * @return boolean
     */

    public boolean updateOrderStatusPushES(Long orderId) {

        OcBOrder ocBorderDto = new OcBOrder();
        ocBorderDto.setId(orderId);
        ocBorderDto.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
        boolean flag = false;
        try {
            //调用贺柳公共更新订单主表方法【包含推送到ES】
            flag = omsOrderService.updateOrderInfo(ocBorderDto);
            if (!flag) {
                log.error(LogUtil.format("传wms失败后，更新订单状态为已审核，ES更新主表订单状态失败", orderId));
            }
        } catch (Exception e) {
            log.error("ES更新订单状态失败{}", e.getMessage());
            flag = false;
            omsOrderLogService.addUserOrderLog(orderId,
                    ocBorderDto.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(), null, null, e.getMessage(), null);
            log.error(LogUtil.format("传wms失败后，订单状态更新为“已审核”时异常{} "), Throwables.getStackTraceAsString(e));
        }
        return flag;
    }


    /**
     * 通过mq接收出库通知单的回执信息
     *
     * @param sgOutNoticesResults
     */
    private void handleOrder(List<SgBOutNoticesBatchSaveAndWMSResult> sgOutNoticesResults, User user) {
        List<Long> longList = new ArrayList<>();
        long starTime = System.currentTimeMillis();
        for (SgBOutNoticesBatchSaveAndWMSResult sgOutNoticesResult : sgOutNoticesResults) {
            //根据返回的状态做不同的操作
            int code = sgOutNoticesResult.getCode();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("进入处理数据 code:{}", sgOutNoticesResult.getSourceBilId()), sgOutNoticesResult.getCode());
            }
            try {
                /**
                 * 返回码
                 * 1=已传WMS
                 * 2=不需传WMS
                 * 3=传WMS失败次数>5、面单信息为空、新增出库通知单失败
                 * 4=新增出库通知单失败,记录到订单-备注,下次定时任务还会拉取
                 * 5=订单反审核后,审单消息延时到达
                 * 9=获取电子面单失败
                 */
                switch (code) {
                    //传wms成功
                    case 0:
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("订单生成出库通知单返回成功", sgOutNoticesResult.getSourceBilId()));
                        }
                        this.saveLog(sgOutNoticesResult.getSourceBilId(), "订单生成出库通知单成功", user);
                        break;
                    //失败
                    case -1:
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("订单生成出库通知单失败改为已审核", sgOutNoticesResult.getSourceBilId()));
                        }
                        OcBOrder order2 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("查询数据库订单状态为:{}", sgOutNoticesResult.getSourceBilId()), order2.getOrderStatus());
                        }
                        omsOrderLogService.addUserOrderLog(order2.getId(), order2.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                                sgOutNoticesResult.getFlag(), null, null, user);

                        break;
                    //已经传WMS
                    case 1:
                        this.updateDistribution(user, sgOutNoticesResult);
                        break;
                    //不用传wms或者已经传wms
                    case 2:
                        this.updateDistribution(user, sgOutNoticesResult);
                        break;
                    //传wms次数大于五次
                    case 3:
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("传wms次数大于五次", sgOutNoticesResult.getSourceBilId()));
                        }
                        OcBOrder order3 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("查询数据库订单状态为:{}", order3.getOrderStatus()));
                        }
                        if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order3.getOrderStatus())) {
                            OcBOrder update3 = new OcBOrder();
                            update3.setId(sgOutNoticesResult.getSourceBilId());
                            update3.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
                            //update3.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
                            //int count = order3.getToDrpCount() == null ? 0 : order3.getToDrpCount();
                            //update3.setToDrpCount(count + 1);
                            update3.setSysremark(sgOutNoticesResult.getFlag());
                            update3.setIsOverfive(1L); //传wms次数超过五次标识   下次不再传wms
                            omsOrderService.updateOrderInfo(update3);
                        }
                        break;
                    //生成出库通知单失败
                    case 4:
                        OcBOrder order4 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("订单获取电子面单部分成功或为空:{}", sgOutNoticesResult.getSourceBilId()), order4.getOrderStatus());
                        }
                        if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order4.getOrderStatus())) {
                            this.resetToDrpStatus(sgOutNoticesResult, order4);
                        }
                        break;
                    //订单已反审核不能推送wms（未生成出库通知单之前）
                    case 5:
                        OcBOrder order5 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("订单已经反审核不推送wms记录:{}", order5.getId()));
                        }
                        omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order5.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                                "订单已反审核不能推送wms", null, null, user);
                        break;
                    /**
                     * 20191023号，新增需求，京东电子面单失败时，记录失败原因到订单【系统备注】字段中
                     */
                    case 9:
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("【code = 9】获取京东电子面单失败", sgOutNoticesResult.getSourceBilId()));
                        }
                        OcBOrder order9 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("查询数据库订单状态为:{}", sgOutNoticesResult.getSourceBilId()), order9.getOrderStatus());
                        }
                        if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order9.getOrderStatus())) {
                            this.resetToDrpStatus(sgOutNoticesResult, order9);
                        }
                        break;
//                        * 10=传TMS成功 * 11=传TMS失败 * 15=传Wing成功 * 16=传Wing失败
                    case 10:
                        OcBOrder order10 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order10 != null) {
                            if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order10.getOrderStatus())) {
                                //更新物流信息
                                OcBOrder update10 = new OcBOrder();
                                update10.setId(sgOutNoticesResult.getSourceBilId());
                                update10.setCpCLogisticsId(sgOutNoticesResult.getCpCLogisticsId());
                                update10.setCpCLogisticsEcode(sgOutNoticesResult.getCpCLogisticsEcode());
                                update10.setCpCLogisticsEname(sgOutNoticesResult.getCpCLogisticsEname());
                                update10.setExpresscode(sgOutNoticesResult.getExpressCode());
                                //update10.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_06);
                                omsOrderService.updateOrderInfo(update10);
                                omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order10.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                                        "传TMS成功,更新订单物流信息成功", null, null, user);
                            }
                        }
                        break;
                    /**
                     * 传TMS失败
                     */
                    case 11:
                        OcBOrder order11 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order11 != null) {
                            if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order11.getOrderStatus())) {
                                //唯品会获取电子面单失败需按照时间间隔不断重试
                                if (PlatFormEnum.VIP_JITX.getCode().equals(order11.getPlatform())) {
                                    String msgContent = "JITX获取电子面单失败:" + SplitMessageUtil.splitMsgBySize(sgOutNoticesResult.getFlag(), SplitMessageUtil.SIZE_200);
                                    if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order11.getOrderStatus())) {
                                        this.jitxOrderResetToDrpStatus(sgOutNoticesResult, order11);
                                    }
                                    omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order11.getBillNo(),
                                            OrderLogTypeEnum.ORDER_TOWMS.getKey(), msgContent, null, null, user);
                                } else {
                                    String msgContent = "传TMS失败:" + SplitMessageUtil.splitMsgBySize(sgOutNoticesResult.getFlag(), SplitMessageUtil.SIZE_200);
                                    if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order11.getOrderStatus())) {
                                        this.resetToDrpStatus(sgOutNoticesResult, order11);
                                    }
                                    omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order11.getBillNo(),
                                            OrderLogTypeEnum.ORDER_TOWMS.getKey(), msgContent, null, null, user);
                                    OcBOrder update = new OcBOrder();
                                    update.setId(order11.getId());
                                    //update.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_01);
                                    //update.setToDrpFailedReason(sgOutNoticesResult.getFlag());
                                    ocBOrderMapper.updateById(update);
                                }
                            }
                        }
                        break;
                    /**
                     * TMS回执失败
                     */
                    case 12:
                        OcBOrder order12 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order12 != null) {
                            if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order12.getOrderStatus())) {
                                OcBOrder update = new OcBOrder();
                                update.setId(order12.getId());
                                //update.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_01);
                                //update.setToDrpFailedReason(sgOutNoticesResult.getFlag());
                                update.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
                                //update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
                                update.setSysremark(sgOutNoticesResult.getFlag());
                                ocBOrderMapper.updateById(update);

                                omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order12.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                                        "TMS回执失败", null, null, user);
                            }
                        }
                        break;
                    case 15:
                        OcBOrder order15 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order15 != null) {
                            if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order15.getOrderStatus())) {
                                OcBOrder update15 = new OcBOrder();
                                update15.setId(sgOutNoticesResult.getSourceBilId());
                                //update15.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
//                            order1.setSysremark(sgOutNoticesResult.getFlag());
                                //update15.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_06);
                                omsOrderService.updateOrderInfo(update15);
                            }
                            omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order15.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                                    "传DRP成功", null, null, user);
                        }
                        break;
                    /**
                     * 传WING失败
                     */
                    case 16:
                        OcBOrder order16 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order16 != null) {
                            if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order16.getOrderStatus())) {
                                this.resetToDrpStatus(sgOutNoticesResult, order16);
                                omsOrderLogService.addUserOrderLog(sgOutNoticesResult.getSourceBilId(), order16.getBillNo(), OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                                        "传DRP失败", null, null, user);
                                OcBOrder update = new OcBOrder();
                                update.setId(order16.getId());
                                //update.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_02);
                                //update.setToDrpFailedReason(sgOutNoticesResult.getFlag());
                                //ocBOrderMapper.updateById(update);
                            }
                        }
                        break;

                    /**
                     * WING回执失败
                     */
                    case 18:
                        OcBOrder order18 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order18 != null) {
                            OcBOrder update = new OcBOrder();
                            update.setId(order18.getId());
                            //update.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_02);
                            //update.setToDrpFailedReason(sgOutNoticesResult.getFlag());
                            //ocBOrderMapper.updateById(update);

                        }
                        break;

                    case 19:
                        this.updateDistribution(user, sgOutNoticesResult);
                        break;
                    /**
                     * 传WMS失败
                     */
                    case 20:
                        OcBOrder order20 = ocBOrderMapper.selectById(sgOutNoticesResult.getSourceBilId());
                        if (order20 != null) {
                            OcBOrder update = new OcBOrder();
                            update.setId(order20.getId());
                            if (StringUtils.isNotEmpty(sgOutNoticesResult.getFlag()) && sgOutNoticesResult.getFlag().contains("可用库存缺")) {
                                //update.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_04);
                            } else {
                                //update.setThirdPartyFailStatus(UpdateInDistributionMqUtil.THIRD_PARTY_FAIL_STATUS_03);
                            }
                            //update.setToDrpFailedReason(sgOutNoticesResult.getFlag());
                            //ocBOrderMapper.updateById(update);
                        }
                        break;

                    default:
                        log.error(LogUtil.format("返回数据异常 code:{}", sgOutNoticesResult.getSourceBilId()), sgOutNoticesResult.getCode());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("返回信息为:{},更新失败", sgOutNoticesResult.getSourceBilId()), sgOutNoticesResult.getFlag());
                //记录更新错误的id
                longList.add(sgOutNoticesResult.getSourceBilId());
            }
        }

        long endTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("返回数据处理处理耗时:{}ms,一共:{}条"), endTime - starTime, sgOutNoticesResults.size());
        }

        //将错误的订单更改为已审核
        if (CollectionUtils.isNotEmpty(longList)) {
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(longList);
            Map<Long, OcBOrder> ocBOrderMap = ocBOrderList.stream().collect(Collectors.toMap(OcBOrder::getId, v -> v,
                    (v1, v2) -> v1));
            for (Long aLong : longList) {
                if (ocBOrderMap.get(aLong) != null) {
                    if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrderMap.get(aLong).getOrderStatus())
                            || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrderMap.get(aLong).getOrderStatus())) {
                        continue;
                    }
                }
                OcBOrder order = new OcBOrder();
                order.setId(aLong);
                order.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
                omsOrderService.updateOrderInfo(order);
            }
        }

    }

    private void resetToDrpStatus(SgBOutNoticesBatchSaveAndWMSResult sgOutNoticesResult, OcBOrder order) {
        OcBOrder update = new OcBOrder();
        update.setId(sgOutNoticesResult.getSourceBilId());
        update.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
        //update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
        //int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
        //update.setToDrpCount(count + 1);
        update.setSysremark(sgOutNoticesResult.getFlag());
        if (omsOrderService.updateOrderInfo(update)) {
//            if (count <= failNumber) {
//                wmsTaskService.updateOcBToWmsTaskStatus(order.getId(), 0);
//            } else {
//                if (log.isDebugEnabled()) {
//                    log.debug(LogUtil.format("当前单据ID:{},传DRP流程失败次数：{}", order.getId()), order.getId(), count);
//                }
//            }
        }
    }

    private void jitxOrderResetToDrpStatus(SgBOutNoticesBatchSaveAndWMSResult sgOutNoticesResult, OcBOrder order) {
        OcBOrder update = new OcBOrder();
        update.setId(sgOutNoticesResult.getSourceBilId());
        update.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
//        update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//        int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//        update.setToDrpCount(count + 1);
        update.setSysremark(sgOutNoticesResult.getFlag());
        if (omsOrderService.updateOrderInfo(update)) {
            Integer minuteNum = businessSystemParamService.jitxGetLabelRetryDelayTime();
            //系统参数为空或为0 时 按照次数进行控制
            if (minuteNum == null || minuteNum == 0) {
//                if (count <= failNumber) {
//                    wmsTaskService.updateOcBToWmsTaskStatus(order.getId(), 0);
//                } else {
//                    if (log.isDebugEnabled()) {
//                        log.debug(" 当前单据ID:{},传DRP流程失败次数：{}", order.getId(), count);
//                    }
//                }
            } else {
                //按照时间间隔重试
                Date delayTime = DateUtil.addDate(new Date(), Calendar.MINUTE, minuteNum);
                wmsTaskService.updateOcBToWmsTaskStatusAndDelayTimeByOrderId(order.getId(), 0, delayTime);
            }
        }
    }

    private void updateDistribution(User user, SgBOutNoticesBatchSaveAndWMSResult sgOutNoticesResult) {
        long starTime = System.currentTimeMillis();
        //不用传wms或者已经传wms 调用配货中服务
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("不需要传wms调用配货中服务", sgOutNoticesResult.getSourceBilId()));
        }
        String noticesBillNo = sgOutNoticesResult.getNoticesBillNo();
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        OcBOrder order = new OcBOrder();
        order.setId(sgOutNoticesResult.getSourceBilId());
        order.setSgBOutBillNo(noticesBillNo);
        ocBOrderRelation.setOrderInfo(order);

        boolean flag = omsOrderUpdateInDistributionService.updateInDistribution(ocBOrderRelation, user);
        if (!flag) {
            log.debug(LogUtil.format("updateInDistribution调用更新配货中服务失败,返回:{}", sgOutNoticesResult.getSourceBilId()), flag);
        }
        this.saveLog(sgOutNoticesResult.getSourceBilId(), "调用更新配货中服务", user);
        long endTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用更新配货中服务耗时:{}ms"), endTime - starTime);
        }
    }


    private void saveLog(Long id, String msg, User user) {
        try {
            omsOrderLogService.addUserOrderLog(id, "", OrderLogTypeEnum.ORDER_TOWMS.getKey(),
                    msg, null, null, user);
        } catch (Exception e) {
            log.error(LogUtil.format("记录日志出错: {}"), Throwables.getStackTraceAsString(e));
        }
    }

}
