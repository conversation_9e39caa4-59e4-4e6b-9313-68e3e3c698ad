package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.services.OmsTaobaoExchangeService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/12/1 3:29 下午
 * @Version 1.0
 */
@Step(order = 40, description = "根据换货状态以及相关是否存在换货订单, 跳转不同的逻辑")
@Slf4j
@Component
public class Step040ExchangeStatusCore extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {

    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try{
            //换货状态
            String status = ipBTaobaoExchange.getStatus();
            //换货的sku信息
            OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
            //换货待处理    买家已退货，待收货  待买家退货  待发出换货商品
            boolean isReturnExit = (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.BUYER_RETURN_COLLECT_GOODS.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.STAY_BUYER_RETURN.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.WAIT_ISSUE_EXCHANGE_GOODS.getName().equals(status));

//            boolean isAgree = (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)
//                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.WAIT_BUYER_MODIFY.getName().equals(status)
//                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.BUYER_RETURN_COLLECT_GOODS.getName().equals(status))
//                    && ocBReturnOrder != null && ocBReturnOrder.getTbDisputeId() != null;
            if (isReturnExit && ocBReturnOrder == null) {
                //跳转到生成退换货单
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step100SaveExchangeOrder.class);
            } else if (isReturnExit && ocBReturnOrder.getTbDisputeId() == null) {
                //跳转到 更新退换货单和换货订单 如果为生成换货订单  则生成
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step090UpdateExchangeOrder.class);
            } else if (isReturnExit &&  ocBReturnOrder.getTbDisputeId() != null){
                String sku = orderInfo.getProductSku().getSkuEcode();
                Long qty = ipBTaobaoExchange.getQty();
                OcBReturnOrderExchange ocBReturnOrderExchange = orderInfo.getOcBReturnOrderExchanges().get(0);
                BigDecimal qtyExchange = ocBReturnOrderExchange.getQtyExchange();
                String psCSkuEcode = ocBReturnOrderExchange.getPsCSkuEcode();
                //sku及数量和地址发生变化
                if (!sku.equals(psCSkuEcode) || qtyExchange.compareTo(new BigDecimal(qty)) != 0 || omsTaobaoExchangeService.isAddressChange(orderInfo)){
                    //跳转到取消退换货单 和 作废换货订单 然后重新生成新的 退换货单和换货订单
                    return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step080CancelToGenerateOrder.class);
                }
                // 判断姓名或者手机号 是否发生变化
                omsTaobaoExchangeService.updateConsigneeAndPhone(ocBReturnOrder, ipBTaobaoExchange, orderInfo.getExchangeOrder(), operateUser);
                //判断订单是否占单成功
                if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)) {
                    List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeOrder();
                    if (CollectionUtils.isEmpty(exchangeOrder)) {
                        //未查询到换货订单
                        String message = "未查询到换货订单,此换货单异常";
                        ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                                ipBTaobaoExchange);
                        return new ProcessStepResult<>(StepStatus.FINISHED,message);
                    }
                    List<OmsOrderExchangeRelation> middleState = exchangeOrder.stream().filter(p -> OmsOrderStatus.ORDER_DEFAULT.toInteger()
                            .equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(middleState)) {
                        String message = "换货订单状态不满足此次转换,等待下一次准换";
                        ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), message,
                                ipBTaobaoExchange);
                        return new ProcessStepResult<>(StepStatus.FINISHED,message);
                    }
                    List<OmsOrderExchangeRelation> orderExchangeRelations = exchangeOrder.stream().filter(p -> OmsOrderStatus.BE_OUT_OF_STOCK.toInteger()
                            .equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orderExchangeRelations)) {
                        //跳转拒绝换货策略
                        return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step060RefuseExchangeGoods.class);
                    } else {
                        //跳转同意换货策略
                        return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step050AgreeExchangeGoods.class);

                    }
                }
                if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.BUYER_RETURN_COLLECT_GOODS.getName().equals(status)) {
                    //todo 已存在则更新退换货单订单的物流信息
                    String message = "退换货订单已存在,更新物流单号及物流公司";
                    omsTaobaoExchangeService.updateLogisticsCodeAndName(ocBReturnOrder, ipBTaobaoExchange, operateUser);
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FINISHED,message);
                }
                String message = "未修改sku及数量,标记为已转换";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED,message);
            } else if ((TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)
                    ||TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.REFUND_PLEASE.getName().equals(status))
                    && ocBReturnOrder != null) {
                //跳转到取消退换货单 和 作废换货订单    (请退款)则处理换货转退货(删除换货明细)
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step070ExchangeToReturnOrClose.class);
            }
            String message = "当前状态暂不满足转换条件";
            ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                    ipBTaobaoExchange);
            return new ProcessStepResult<>(StepStatus.FINISHED,message);
        }catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "处理跳转失败"+e.getMessage());
            log.error(LogUtil.format("处理跳转失败:{}", "处理跳转失败"), Throwables.getStackTraceAsString(e));
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
       }
    }
}
