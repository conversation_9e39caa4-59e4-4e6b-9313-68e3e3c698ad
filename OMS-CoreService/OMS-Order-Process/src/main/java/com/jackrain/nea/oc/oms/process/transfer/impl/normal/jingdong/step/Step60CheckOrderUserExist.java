package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongUser;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 3:41 PM
 * 判断【收货人信息表】中是否存在数据
 */
@Step(order = 60, description = "判断【收货人信息表】中是否存在数据")
@Slf4j
@Component
public class Step60CheckOrderUserExist extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {
    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongUser jingdongUser = orderInfo.getJingdongUser();
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();
        //仅用来更新
        IpBJingdongOrder updateOrder = new IpBJingdongOrder();
        // updateOrder.setId(jingdongOrder.getId());
        Long orderId = jingdongOrder.getOrderId();

        if (jingdongUser != null) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "下一步转换", Step70SaveOmsOrder.class);
        } else {
            updateOrder.setTransdate(new Date());
            updateOrder.setTransCount(jingdongOrder.getTransCount() + 1L);
            updateOrder.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());
            updateOrder.setSysremark("订单对应的京东用户表为空，不允许单据转换");
            ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
            return new ProcessStepResult<>(StepStatus.FINISHED, "订单对应的京东用户表为空，不允许单据转换");
        }
    }
}
