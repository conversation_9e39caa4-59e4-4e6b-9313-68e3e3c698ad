package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiWen.z
 * create at: 2019/10/13 0013
 */
@Step(order = 4, description = "检查渠道订单是否已经存在")
@Component
public class Step004CheckOmsOrderExist extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();

        String orderStatus = orderInfo.getTaobaoOrder().getStatus();
        String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
        String orderNo = orderInfo.getOrderNo();
        long orderId = orderInfo.getOrderId();
        List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderInfo(orderInfo.getTaobaoOrder().getTid());

        // 增加是否关闭订单判断。如果是关闭订单状态，则直接标记为转换
        boolean isTradeClosed = TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equalsIgnoreCase(orderStatus);
        if (isTradeClosed) {
            if (CollectionUtils.isNotEmpty(findOrderInfoList)) {
                boolean isSuccess = orderService.preSaleTransactionClosure(findOrderInfoList, stepTradeStatus, operateUser);
                if (!isSuccess) {
                    //取消订单失败 结束  等待补偿任务继续处理
                    //因为预售退货，在退单转换时同时转了订单关闭状态，导致redis加锁失败，所以更新中间表状态为未转换，等待下次转换
                    //2020-09-21 yj
                    this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                            TransferOrderStatus.NOT_TRANSFER, "交易关闭,预售未付尾款取消订单处理失败");
                    return new ProcessStepResult<>(StepStatus.FAILED, "交易关闭,预售未付尾款取消订单处理失败");
                }
            }
            String operateMessage = "订单状态=" + orderStatus + ";订单已经被关闭。进入下一阶段设置已转换状态。OrderId="
                    + orderId + ";OrderNo=" + orderNo;
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.SUCCESS);
            //2019-11-21 订单关闭 更新卖家备注
            stepResult.setNextStepOperateObj(findOrderInfoList);
            stepResult.setNextStepClass(Step070UpdateSellerRemark.class);
        }

        if (findOrderInfoList != null && findOrderInfoList.size() > 0) {
            List<OcBOrder> normalOmsOrders = new ArrayList<>();
            for (OcBOrder ocOrderInfo : findOrderInfoList) {
                if (ocOrderInfo == null) {
                    continue;
                }
                Integer ocStatus = ocOrderInfo.getOrderStatus();
                if (OmsOrderStatus.CANCELLED.toInteger().equals(ocStatus) || OmsOrderStatus.SYS_VOID.toInteger().equals(ocStatus)) {
                    continue;
                }
                normalOmsOrders.add(ocOrderInfo);
            }
            if (normalOmsOrders.size() == ResultCode.SUCCESS) {
                this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED,
                        "订单已取消或作废");
                String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId()
                        + "订单已取消或作废,标记为已转换");
                return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
            }
            stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "已存在. OrderId=" + orderId
                    + ";OrderNo=" + orderNo + " 进入更新卖家备注,订单状态,订单金额阶段。");
            stepResult.setNextStepOperateObj(normalOmsOrders);
            stepResult.setNextStepClass(Step007UpdateSellerRemark.class);
        } else {
            stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "，进入下一阶段");
        }
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }
}
