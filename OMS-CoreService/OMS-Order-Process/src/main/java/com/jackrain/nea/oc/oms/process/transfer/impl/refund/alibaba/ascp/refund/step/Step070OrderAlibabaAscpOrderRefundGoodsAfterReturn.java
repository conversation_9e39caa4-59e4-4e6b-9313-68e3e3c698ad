package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 9:50 上午
 * @Version 1.0
 */
@Step(order = 70, description = "发货处理")
@Slf4j
@Component
public class Step070OrderAlibabaAscpOrderRefundGoodsAfterReturn extends BaseAlibabaAscpOrderRefundProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderRefundRelation> {
    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderRefundRelation> startProcess(IpBAlibabaAscpOrderRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBAlibabaAscpOrderRefund orderRefund = orderInfo.getOrderRefund();
        try {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            omsOrderRelation = omsOrderRelation.stream()
                    .filter(obj -> TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode().equals(obj.getOrderMark()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(omsOrderRelation)) {
                orderRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), "需再次转换", orderRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "转换结束");
            }
            ReturnOrderRelation relation = this.goodsAfterOrderIsExist(orderRefund, orderInfo.getIpBAlibabaAscpOrderRefundItems());
            List<Long> existReturnOrder = relation.getIds();
            if (CollectionUtils.isEmpty(existReturnOrder) || relation.isFlag()) {
//                    BigDecimal refundAmount = orderRefund.getRefundAmount();
                orderRefundService.setAfOrderAmount(orderInfo, orderRefund);
                //不存在 按子订单维度生成退换货单
                List<OcBReturnOrderRelation> orderRelations = orderRefundService.
                        standplatRefundOrderToReturnOid(orderInfo, omsOrderRelation, orderRefund, orderInfo.getIpBAlibabaAscpOrderRefundItems(), operateUser);
                if (CollectionUtils.isNotEmpty(orderRelations)) {
                    List<Long> refundIds = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, operateUser);
                    OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
                    orderRefundService.foundRefundSlipAfter(refundIds, ocBOrder, orderRefund, operateUser);
                    String remark = SysNotesConstant.SYS_REMARK29;
//                        orderRefund.setRefundAmount(refundAmount);
                    orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, orderRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "生成退换货单成功!转换完成!");

                } else {
                    String remark = "申请数量大于可退数量,转换结束";
                    orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, orderRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                }
            } else {
                //存在退换货单 更新
                String logisticsNo = orderRefund.getTmsOrderCode();
                String statusString = OmsOrderRefundStatus.toStatusString(OmsOrderRefundStatus.SUCCESS.toInteger());
                omsRefundOrderService.saveExistReturnOrder(existReturnOrder, logisticsNo, null, statusString, operateUser);
                String remark = SysNotesConstant.SYS_REMARK5;
                orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, orderRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, remark);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货后退单转换异常:{}", "发货后退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            orderRefundService.updateRefundIsTransError(orderRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    /**
     * 客退的退单是否存在的处理
     *
     * @param orderRefund
     * @return
     */
    public ReturnOrderRelation goodsAfterOrderIsExist(IpBAlibabaAscpOrderRefund orderRefund, List<IpBAlibabaAscpOrderRefundItem> orderRefundItems) {
        ReturnOrderRelation relation = new ReturnOrderRelation();
        List<Long> existReturnOrder = this.isExistReturnOrderRefundByReturnId(orderRefund.getBizOrderCode());
        if (CollectionUtils.isEmpty(existReturnOrder)) {
            List<String> ooids = orderRefundItems.stream()
                    .filter(item -> item.getSubOrderCode() != null)
                    .map(IpBAlibabaAscpOrderRefundItem::getSubOrderCode)
                    .distinct()
                    .collect(Collectors.toList());
            if (ooids.isEmpty()) {
                relation.setIds(existReturnOrder);
                relation.setFlag(false);
                return relation;
            }
            List<Long> refundByoid = this.isExistReturnOrderRefundByoid(StringUtils.join(ooids, ","));
            //将必要的退款数据更新到对应的退货单
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundByoid);
                List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(refundByoid);
                this.updateOcBReturnOrder(orderRefund, ocBReturnOrders, orderRefunds);
                relation.setIds(refundByoid);
                relation.setFlag(true);
            }
        } else {
            relation.setIds(existReturnOrder);
            relation.setFlag(false);
        }
        return relation;
    }


    /**
     * 根据明细表的退款单号查询退单是否存在
     *
     * @param refundNo
     * @return
     */
    public List<Long> isExistReturnOrderRefundByReturnId(String refundNo) {
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundIdAndStatus(refundNo);
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }
        return null;
    }

    /**
     * 根据明细表的oid查询退货单是否存在
     *
     * @param oid
     * @return
     */
    public List<Long> isExistReturnOrderRefundByoid(String oid) {
        if (oid == null) {
            return null;
        }
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByOid(oid);
        if (CollectionUtils.isNotEmpty(ids)) {
            //排除已经取消的退换货订单
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return null;
    }

    public void updateOcBReturnOrder(IpBAlibabaAscpOrderRefund ascpOrderRefund, List<OcBReturnOrder> ocBReturnOrders,
                                     List<OcBReturnOrderRefund> orderRefunds) {
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getReturnId())) {
                continue;
            }
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            //平台退款单号
            returnOrder.setReturnId(ascpOrderRefund.getBizOrderCode());
            //卖家昵称
//            returnOrder.setBuyerNick(ascpOrderRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ascpOrderRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ascpOrderRefund.getModified());
            //货物退回时间
            //returnOrder.setReturnTime(ascpOrderRefund.getGoodReturnTime());
            //退款说明
//            returnOrder.setReturnDesc(ascpOrderRefund.getReturnReason());
            //商品应退金额(
//            returnOrder.setReturnAmtList(ascpOrderRefund.getRefundAmount());
            //售后/售中
            //returnOrder.setReturnPhase(ascpOrderRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
//            returnOrder.setReturnAmtActual(ascpOrderRefund.getRefundAmount());
            //卖家昵称
            //returnOrder.setSellerNick(ascpOrderRefund.getSellerNick());
            //物流公司名称
//            String companyName = ascpOrderRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ascpOrderRefund.getTmsOrderCode());
//            returnOrder.setCpCLogisticsEname(companyName);
            // 运费 by 秦俊龙
//            returnOrder.setReturnAmtShip(ascpOrderRefund.getReturnShipamount());
            ocBReturnOrderMapper.updateById(returnOrder);
        }
        for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
            if (returnOrderRefund.getOid() != null && returnOrderRefund.getOid().equals(ascpOrderRefund.getBizOrderCode())) {
                OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                orderRefund.setId(returnOrderRefund.getId());
                orderRefund.setRefundBillNo(ascpOrderRefund.getBizOrderCode());
                QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
                wrapper.eq("id", returnOrderRefund.getId());
                wrapper.eq("oc_b_return_order_id", returnOrderRefund.getOcBReturnOrderId());
                //更新之前分库建必须设置为空
                ocBReturnOrderRefundMapper.update(orderRefund, wrapper);
            }
        }

    }
}
