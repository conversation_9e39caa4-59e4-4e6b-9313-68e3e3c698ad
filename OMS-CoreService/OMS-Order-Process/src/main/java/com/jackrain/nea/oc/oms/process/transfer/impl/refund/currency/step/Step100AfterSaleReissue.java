package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2023/3/31
 */
@Slf4j
@Component
@Step(order = 100, description = "售后补寄")
public class Step100AfterSaleReissue
        extends BaseStanPlatRefundProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        String orderNo = ipBStandplatRefund.getOrderNo();
        String returnNo = ipBStandplatRefund.getReturnNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("afterSaleReissue.start...", orderNo, returnNo));
        }
        // check standPlat bill
        ProcessStepResult checkStandRefundResult = checkStandPlatRefund(ipBStandplatRefund);
        if (StepStatus.SUCCESS != checkStandRefundResult.getStatus()) {
            return checkStandRefundResult;
        }
        // check oms origin order
        ProcessStepResult checkOmsOrderResult = checkOmsOrder(orderInfo);
        if (StepStatus.SUCCESS != checkOmsOrderResult.getStatus()) {
            return new ProcessStepResult<>(StepStatus.FINISHED, "转换失败");
        }
        return checkOmsOrderResult;
    }

    /**
     * 校验中间表单据
     *
     * @param standRefund
     * @return
     */
    private ProcessStepResult checkStandPlatRefund(IpBStandplatRefund standRefund) {
        boolean isUnTrans = standRefund.getIstrans().equals(TransferOrderStatus.NOT_TRANSFER.toInteger());
        if (!isUnTrans) {
            String message = "单据不是未转换状态,转换失败";
            TransRefundNodeTipUtil.standardTransTipCAS(standRefund, TransNodeTipEnum.DEFAULT);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    message, standRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "success");
    }

    /**
     * 校验零售发货单
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult checkOmsOrder(OmsStandPlatRefundRelation orderInfo) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        // 原单不存在
        if (CollectionUtils.isEmpty(omsOrderRelations)) {
            if (checkStandPlatRefundOverrideTime(ipBStandplatRefund)) {
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK2, ipBStandplatRefund);
            } else {
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        SysNotesConstant.SYS_REMARK1, ipBStandplatRefund);
            }
            return new ProcessStepResult<>(StepStatus.FAILED, "转换失败");
        }
        // 判断是否存在有效的原始订单
        ProcessStepResult omsOrdersCheckResult = checkIsExistEffectiveOrder(omsOrderRelations);
        if (StepStatus.SUCCESS != omsOrdersCheckResult.getStatus()) {
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    omsOrdersCheckResult.getMessage(), ipBStandplatRefund);
            return omsOrdersCheckResult;
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "success");
    }

    /**
     * 判断中间表退单时间是否超过指定时间
     *
     * @param standRefund
     * @return
     */
    private boolean checkStandPlatRefundOverrideTime(IpBStandplatRefund standRefund) {
        Long boundTimes = 24 * 60 * 60 * 1000L + standRefund.getCreationdate().getTime();
        return boundTimes < System.currentTimeMillis();
    }

    /**
     * 判断是否存在有效的原始订单
     *
     * @param omsRelations
     * @return
     */
    private ProcessStepResult checkIsExistEffectiveOrder(List<OmsOrderRelation> omsRelations) {
        Iterator<OmsOrderRelation> iterator = omsRelations.iterator();
        while (iterator.hasNext()) {
            OmsOrderRelation next = iterator.next();
            OcBOrder order = next.getOcBOrder();
            Integer status = order.getOrderStatus();
            if (OmsOrderStatus.CANCELLED.toInteger().equals(status) || OmsOrderStatus.SYS_VOID.toInteger().equals(status)) {
                String suffixInfo = order.getSuffixInfo();
            //    if (!"REFUND-VOID".equals(suffixInfo)) {
                    iterator.remove();
            //    }
            }
        }
        if (CollectionUtils.isEmpty(omsRelations)) {
            return new ProcessStepResult<>(StepStatus.FAILED, SysNotesConstant.SYS_REMARK4);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "success");
    }

}
