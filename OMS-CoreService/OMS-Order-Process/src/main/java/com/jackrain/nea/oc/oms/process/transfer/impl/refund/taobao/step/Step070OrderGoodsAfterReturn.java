package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IntermediateTableRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 9:50 上午
 * @Version 1.0
 */
@Step(order = 70, description = "发货处理")
@Slf4j
@Component
public class Step070OrderGoodsAfterReturn extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        try {
            //退单中间表的退单状态
            String status = ipBTaobaoRefund.getStatus();
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            for (int i = 0; i < omsOrderRelation.size(); i++) {
                OmsOrderRelation omsOrderRelation1 = omsOrderRelation.get(i);
                if (!TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode().equals(omsOrderRelation1.getOrderMark())) {
                    omsOrderRelation.remove(omsOrderRelation1);
                    i--;
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("{} 查询订单和退单信息Step1:{}", this.getClass().getName(), JSONObject.toJSONString(omsOrderRelation));
            }
            if (CollectionUtils.isEmpty(omsOrderRelation)) {
                TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DEFAULT);
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        "转换完成", ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "转换结束");
            }
            ReturnOrderRelation relation = omsRefundOrderService.goodsAfterOrderIsExist(ipBTaobaoRefund);
            if (log.isDebugEnabled()) {
                log.debug("{} 查询退单信息Step2:{}", this.getClass().getName(), JSONObject.toJSONString(relation));
            }
            List<Long> existReturnOrder = relation.getIds();
            if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status)) {
                //卖家拒绝退款新增逻辑，查询店铺策略（是否自动取消退换货单） 若为空或为否 不取消退换货单，直接转换成功
                if (TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status)) {
                    if (omsRefundOrderService.checkReturnOrder(ipBTaobaoRefund.getCpCShopId())) {
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                "转换完成", ipBTaobaoRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, "不自动取消退换货单，转换成功");
                    }
                }
                //退款关闭
                //取消已发货退款单
                omsRefundOrderService.closedRefundSlip(ipBTaobaoRefund.getRefundId());
                if (CollectionUtils.isNotEmpty(existReturnOrder)) {
                    omsRefundOrderService.refundOrderClose(existReturnOrder, omsOrderRelation, ipBTaobaoRefund, operateUser);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "退款关闭,准换完成");
                } else {
                    String remark = SysNotesConstant.SYS_REMARK57;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "未找到退换货单,准换结束");
                }
            } else if ((TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status))) {
                //如果平台状态为(买家已经申请退款，等待卖家同意）(卖家拒绝退款)，则不进行处理，
                String remark = SysNotesConstant.SYS_REMARK27;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, remark);
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
                OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
                log.debug(this.getClass().getName() + " 查询退单信息Step3:{}", relation.isFlag() + "是否存在订单信息" + JSONObject.toJSONString(existReturnOrder));
                if (CollectionUtils.isEmpty(existReturnOrder) || relation.isFlag()) {
                    //不存在 按子订单维度生成退换货单
                    List<OcBReturnOrderRelation> orderRelations = taobaoRefundOrderTransferUtil.
                            taobaoRefundOrderToReturnOid(omsOrderRelation, ipBTaobaoRefund, operateUser, relation.getQty(), false);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("查询退单信息Step4:{};",
                                "Step070OrderGoodsAfterReturn"), JSONObject.toJSONString(orderRelations));
                    }
                    if (CollectionUtils.isNotEmpty(orderRelations)) {
                        // List<Long> refundIds = omsReturnOrderService.insertOmsRetuenOrderInfo(orderRelations, operateUser);
                        List<Long> refundIds = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, ipBTaobaoRefund, omsOrderRelation.size(), operateUser);
                        omsRefundOrderService.foundRefundSlipAfter(refundIds, ocBOrder, ipBTaobaoRefund, operateUser);
                        String remark = SysNotesConstant.SYS_REMARK29;
                        TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DEFAULT);
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, ipBTaobaoRefund);
                        //执行赠品后发
                        omsReturnOrderService.giftsThenSend(orderInfo.getOmsOrderRelation(), orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);
                        return new ProcessStepResult<>(StepStatus.FINISHED, "生成退换货单成功!转换完成!");

                    } else {
                        String remark = "申请数量大于可退数量,转换结束";
                        //判断已发货退款单是否存在
                        omsRefundOrderService.foundRefundSlipAfterNoUpdate(ocBOrder, ipBTaobaoRefund, operateUser);
                        TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.QTY_NOT_ENOUGH);
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, ipBTaobaoRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                    }
                } else {
                    //存在退换货单 更新
                    String sid = ipBTaobaoRefund.getSid();
                    String companyName = ipBTaobaoRefund.getCompanyName();
                    //判断已发货退款单是否存在
                    omsRefundOrderService.foundRefundSlipAfterNoUpdate(existReturnOrder, ocBOrder, ipBTaobaoRefund, operateUser);
                    omsRefundOrderService.saveExistReturnOrder(existReturnOrder, sid, companyName, status, operateUser);
                    String remark = SysNotesConstant.SYS_REMARK5;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                    //调用退换单审核服务
                    //omsRefundOrderService.callRefundAudit(existReturnOrder, operateUser);
                    return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK25;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBTaobaoRefund);
            }
            return new ProcessStepResult<>(StepStatus.FINISHED);
        } catch (Exception e) {
            log.error(LogUtil.format("发货后退单转换异常,异常信息:{}", "发货后退单转换异常", ipBTaobaoRefund.getRefundId()), Throwables.getStackTraceAsString(e), e);
            //修改中间表状态及系统备注
            ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    /**
     * 是否原本为发货后仅退款却因为无发货单转而处理成发货后的处理逻辑
     *
     * @param orderInfo
     * @return
     */
    private boolean isGARefundOnly2GAReturn(OmsTaobaoRefundRelation orderInfo) {
        // 货物状态 goodStatus：TaobaoReturnOrderExt.GoodStatus.BUYER_NOT_RECEIVED
        String goodStatus = orderInfo.getIpBTaobaoRefund().getGoodStatus();
        // 中间表单据状态 status：TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE / TaobaoReturnOrderExt.RefundStatus.SUCCESS
        String status = orderInfo.getIpBTaobaoRefund().getStatus();
        // 发货信息
        boolean isExistsDelivers = orderInfo.isExistsDelivers();

        // 对于原来的处理逻辑为：返货后仅退款的单据
        // 由于没有找到发货信息（历史单据）
        // 需要将其按发货后退货退款的逻辑处理
        boolean result = TaobaoReturnOrderExt.GoodStatus.BUYER_NOT_RECEIVED.getCode().equals(goodStatus)
                && (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status) || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status))
                && !isExistsDelivers;

        if (log.isDebugEnabled()) {
            log.debug("isGARefundOnly2GAReturn.result:{}", result);
        }

        return result;
    }
}
