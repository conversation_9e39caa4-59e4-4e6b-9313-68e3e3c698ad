package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItemEx;
import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 20:06 2020/6/19
 * description ：唯品会退供单-校验商品是否本地存在
 * @ Modified By：
 */
@Step(order = 5, description = "校验商品是否本地存在")
@Slf4j
@Component
public class Step05CheckProductIsExist extends BaseVipReturnOrderProcessStep implements IOmsOrderProcessStep<IpVipReturnOrderRelation> {

    @Autowired
    private IpVipReturnOrderService ipVipReturnOrderService;

    @Override
    public ProcessStepResult<IpVipReturnOrderRelation> startProcess(IpVipReturnOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        boolean hasErrorInfo = false;
        String msg = Resources.getMessage("不存在;");
        StringBuilder sbErrorInfo = new StringBuilder();
        Map<String,Long> skuCode = new HashMap<>();
        for (IpBVipReturnOrderItemEx vipReturnOrderItemEx : orderInfo.getVipReturnOrderItems()) {
            String barcode = vipReturnOrderItemEx.getBarcode();
            if (StringUtils.isEmpty(barcode)) {
                hasErrorInfo = true;
                sbErrorInfo.append("商品条码为空;");
                sbErrorInfo.append("\r\n");
            } else {
                //通过国际条码或者国标码匹配系统条码
                PsSkuResult psSkuResult = psRpcService.selectSkuInfoByforCodes(barcode);
                if (psSkuResult == null || CollectionUtils.isEmpty(psSkuResult.getProSkus())) {
                    // 获取系统条码差异配置
                    String businessSystem = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue()
                            .get(OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_SYSTEM);
                    // 条码差异配置未设置
                    if (businessSystem == null || "".equals(businessSystem) || !businessSystem.contains("|")) {
                        hasErrorInfo = true;
                        sbErrorInfo.append(barcode);
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    } else {
                        String[] vals = businessSystem.split("\\|");
                        // 匹配条码差异配置值
                        String val = vals[0];
                        // 默认不匹配
                        boolean hasmatch = false;
                        String[] prefix = val.split(",");
                        for (int i = 0; i < prefix.length; i++) {
                            if (barcode.startsWith(prefix[i])) {
                                hasmatch = true;
                                //通过差异值继续匹配系统条码
                                ProductSku skuInfo = psRpcService.selectProductSku(vals[1]);
                                if (skuInfo == null) {
                                    hasErrorInfo = true;
                                    sbErrorInfo.append(barcode);
                                    sbErrorInfo.append(msg);
                                    sbErrorInfo.append("\r\n");
                                }else{
                                    skuCode.put(barcode,skuInfo.getId());
                                }
                                break;
                            }
                        }
                        if (!hasmatch) {
                            hasErrorInfo = true;
                            sbErrorInfo.append(barcode);
                            sbErrorInfo.append(msg);
                            sbErrorInfo.append("\r\n");
                        }
                    }
                }else{
                    List<ProSku> proSkuList = psSkuResult.getProSkus();
                    if(proSkuList.size() > 1){
                        hasErrorInfo = true;
                        sbErrorInfo.append(barcode);
                        sbErrorInfo.append("在系统中查询到多个商品条码;");
                        sbErrorInfo.append("\r\n");
                    }else{
                        ProSku proSku = proSkuList.get(0);
                        skuCode.put(barcode,proSku.getId());
                    }
                }
            }
        }
        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在，退出转单操作";
            boolean updateStatusRes = ipVipReturnOrderService.updateRemark(sbErrorInfo.toString(), orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            orderInfo.setSkuCodeMap(skuCode);
            return new ProcessStepResult<>(StepStatus.SUCCESS, "检查商品是否存在成功，进入下一阶段");
        }
    }
}

