package com.jackrain.nea.oc.oms.process;

import com.jackrain.nea.ip.service.JdCancelOrderTransferService;
import com.jackrain.nea.oc.oms.mapper.IpBJdOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.JingdongTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.oc.oms.services.patrol.JDDiscountMoneyService;
import com.jackrain.nea.resource.SystemUserResource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-07-09 13:39
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class TransferJDorderProcessTest {


    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    @Autowired
    private JingdongTransferOrderProcessImpl jingdongTransferOrderProcess;

    @Autowired
    private JdCancelOrderTransferService jdCancelOrderTransferService;
    @Autowired
    private IpBJdOrderMapper jdOrderMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private JDDiscountMoneyService jdDiscountMoneyService;


    @Test
    public void test1() {
//        List<Long> list = new ArrayList<>();
//        list.add(29529L);
//        List<IpBJdOrder> orders = jdOrderMapper.selectBatchIds(list);
//        List<OcBOrder> ocBOrders = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().in("source_code", orders.get(0).getOrderId()));


        // jdCancelOrderTransferService.existDeal(orders.get(0),ocBOrders);
        IpJingdongOrderRelation jingdongOrderRelation = this.ipJingdongOrderService.selectJingdongOrder("97778698300");
        jingdongTransferOrderProcess.start(jingdongOrderRelation, false, SystemUserResource.getRootUser());
    }

    @Test
    public void test2() {
        List<Long> longs = jdDiscountMoneyService.discountMoney(null);
        System.out.println(longs);
    }

}
