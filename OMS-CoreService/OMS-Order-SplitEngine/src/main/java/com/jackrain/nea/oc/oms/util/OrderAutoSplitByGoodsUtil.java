package com.jackrain.nea.oc.oms.util;


import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: 江家雷
 * @since: 2020/8/8
 * create at : 2020/8/8 15:27
 */
@Component
@Slf4j
public class OrderAutoSplitByGoodsUtil {
    @Reference(group = "ps-ext", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;

    /**
     * 获取组合商品或福袋的明细+挂靠赠品(组合商品先不考虑数量)
     *
     * @param orderItemList
     * @return
     */
    public List<OrderItemGroup> getCombinedOrGiftBag(List<OcBOrderItem> orderItemList) {
        List<OrderItemGroup> orderItemGroupList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderItemList)) {
            return orderItemGroupList;
        }
        // 所有赠品 且 gift_relation不为空
        Map<String, List<OcBOrderItem>> giftMap = orderItemList.stream()
                .filter(item -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())
                        && StringUtils.isNotBlank(item.getGiftRelation()))
                .collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
        // 将所有非赠且pro_type=1 或 pro_type=2的商品 按照GROUP_GOODS_MARK分组
        Map<String, List<OcBOrderItem>> combineMap = orderItemList.stream()
                .filter(item -> !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift()) &&
                        (Long.valueOf(SkuType.GIFT_PRODUCT).equals(item.getProType())
                        || Long.valueOf(SkuType.COMBINE_PRODUCT).equals(item.getProType())))
                .collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        // 得到所有非赠品的商品
        List<OcBOrderItem> items = orderItemList.stream()
                .filter(it -> !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift()))
                .collect(Collectors.toList());
        for (OcBOrderItem item : items) {
            if (!Long.valueOf(SkuType.NO_SPLIT_COMBINE).equals(item.getProType())
                    || OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())) {
                continue;
            }
            List<OcBOrderItem> temp = new ArrayList<>();
            temp.add(item);

            String giftRelation = Optional.ofNullable(item.getGiftRelation()).orElse("");
            String groupGoodsMark = Optional.ofNullable(item.getGroupGoodsMark()).orElse("");
            temp.addAll(combineMap.get(groupGoodsMark));
            if (StringUtils.isNotBlank(giftRelation) && CollectionUtils.isNotEmpty(giftMap.get(giftRelation))) {
                temp.addAll(giftMap.get(giftRelation));
            }
            OrderItemGroup orderItemGroup = new OrderItemGroup();
            orderItemGroup.setItems(temp);
            orderItemGroup.setCode(item.getPsCSkuEcode() + groupGoodsMark + giftRelation);
            orderItemGroup.setPsCProEcode(item.getPsCProEcode());
            orderItemGroup.setPsCSkuEcode(item.getPsCSkuEcode());
            orderItemGroup.setNum(item.getQty().intValue());
            orderItemGroup.setSex(item.getSex());
            orderItemGroup.setPsCBrandId(item.getPsCBrandId());
            orderItemGroupList.add(orderItemGroup);
            orderItemGroup.setIsCombinedOrGiftBag(true);
        }
        return orderItemGroupList;
    }

    /**
     * 获取普通商品及其下挂商品，并做好分组
     * @param orderItemList
     * @return
     */
    public List<OrderItemGroup> getGoodsList(List<OcBOrderItem> orderItemList) {
        if (CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        List<OrderItemGroup> orderItemGroupList = new ArrayList<>();
        Map<String, List<OcBOrderItem>> giftMap = orderItemList.stream()
                .filter(item -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())
                        && StringUtils.isNotBlank(item.getGiftRelation()))
                .collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
        // 得到所有非赠品的商品
        List<OcBOrderItem> items = orderItemList.stream()
                .filter(it -> !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift()))
                .collect(Collectors.toList());
        for (OcBOrderItem item : items) {
            if ((!Long.valueOf(SkuType.NORMAL_PRODUCT).equals(item.getProType())
                    && !Long.valueOf(SkuType.PRE_SALE_PRODUCT).equals(item.getProType()))) {
                continue;
            }
            List<OcBOrderItem> temp = new ArrayList<>();
            temp.add(item);
            String giftRelation = Optional.ofNullable(item.getGiftRelation()).orElse("");
            if (StringUtils.isNotBlank(giftRelation)
                    && giftMap != null
                    && CollectionUtils.isNotEmpty(giftMap.get(giftRelation))) {
                temp.addAll(giftMap.get(giftRelation));
            }
            OrderItemGroup orderItemGroup = new OrderItemGroup();
            orderItemGroup.setItems(temp);
            orderItemGroup.setCode(item.getPsCSkuEcode() + giftRelation);
            orderItemGroup.setPsCProEcode(item.getPsCProEcode());
            orderItemGroup.setPsCSkuEcode(item.getPsCSkuEcode());
            orderItemGroup.setNum(item.getQty().intValue());
            orderItemGroup.setSex(item.getSex());
            orderItemGroup.setPsCBrandId(item.getPsCBrandId());
            //商品档案目前没有品类字段，先用大类模拟 Largeseries
            Long prId = item.getPsCProId();
            PsCPro psCPro=this.queryProByIds(prId);
//            orderItemGroup.setPsCProdimId(psCPro!=null?psCPro.getPsCProCategoryId():null);
            orderItemGroupList.add(orderItemGroup);
        }
        return orderItemGroupList;
    }


    /***
     * 将订单明细商品分组
     * @param orderItemList
     * @return
     */
    public List<OrderItemGroup> getGoodGroupList(List<OcBOrderItem> orderItemList) {
        List<OrderItemGroup> allList = new ArrayList<>();
        List<OrderItemGroup> goodsList = this.getGoodsList(orderItemList);
        List<OrderItemGroup> giftBagList = this.getCombinedOrGiftBag(orderItemList);
        if (CollectionUtils.isNotEmpty(goodsList)) {
            allList.addAll(goodsList);
        }
        if (CollectionUtils.isNotEmpty(giftBagList)) {
            allList.addAll(giftBagList);
        }
        return allList;
    }

    /**
     * 获取没有挂靠关系的赠品
     * @param orderItemList
     * @return
     */
    public List<OcBOrderItem> getGiftList(List<OcBOrderItem> orderItemList) {
        if (CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        List<OcBOrderItem> results = new ArrayList<>();
        List<String> giftRelationList = orderItemList.stream()
                .filter(it -> !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                        && StringUtils.isNotEmpty(it.getGiftRelation()))
                .map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
        // 赠品明细  gift_relation为空或gift_relation字段值与所有正常商品的gift_relation也不一样则认为非挂靠赠品
        for (OcBOrderItem item : orderItemList) {
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())) {
                if (StringUtils.isEmpty(item.getGiftRelation())) {
                    results.add(item);
                } else if (CollectionUtils.isEmpty(giftRelationList) || !giftRelationList.contains(item.getGiftRelation())) {
                    results.add(item);
                }
            }
        }
        return results;
    }

    public static class OrderItemGroup {

        private List<OcBOrderItem> items;

        private String code;

        private String psCProEcode;

        private String psCSkuEcode;

        private Integer num;

        private Long sex;

        private Boolean isCombinedOrGiftBag = false;

        private Long psCBrandId;

        private Long psCProdimId;

        public List<OcBOrderItem> getItems() {
            return items;
        }

        public void setItems(List<OcBOrderItem> items) {
            this.items = items;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getPsCProEcode() {
            return psCProEcode;
        }

        public void setPsCProEcode(String psCProEcodes) {
            this.psCProEcode = psCProEcodes;
        }

        public String getPsCSkuEcode() {
            return psCSkuEcode;
        }

        public void setPsCSkuEcode(String psCSkuEcode) {
            this.psCSkuEcode = psCSkuEcode;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public Boolean getIsCombinedOrGiftBag() {
            return isCombinedOrGiftBag;
        }

        public void setIsCombinedOrGiftBag(Boolean combinedOrGiftBag) {
            isCombinedOrGiftBag = combinedOrGiftBag;
        }

        public Long getSex() {
            return sex;
        }

        public void setSex(Long sex) {
            this.sex = sex;
        }

        public Long getPsCProdimId() {
            return psCProdimId;
        }

        public void setPsCProdimId(Long psCProdimId) {
            this.psCProdimId = psCProdimId;
        }

        public Long getPsCBrandId() {
            return psCBrandId;
        }

        public void setPsCBrandId(Long psCBrandId) {
            this.psCBrandId = psCBrandId;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj instanceof OrderItemGroup) {
                OrderItemGroup itemGroup = (OrderItemGroup) obj;
                return code.equals(itemGroup.getCode());
            }
            return false;
        }

        @Override
        public int hashCode() {
            return code.hashCode();
        }
    }

    /**
     * <AUTHOR>
     * @Date 13:44 2021/4/29
     * @Description 根据商品编码查询商品档案
     */
    public PsCPro queryProByIds(Long proId) {
        List<Integer> list = new ArrayList<Integer>();
        list.add(proId.intValue());
        List<PsCPro> proList = skuLikeQueryCmd.queryProByIds(list);
        if (CollectionUtils.isNotEmpty(proList)) {
            return proList.get(0);
        }
        return null;
    }
}