package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.AlibabaAscpOrderCancelTransferCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.AlibabaAscpOrderCancelTransferProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 天猫直发取消订单
 * @author: 郑小龙
 * @date: 2020-06-04 11:57
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class AlibabaAscpOrderCancelTransferCmdImpl implements AlibabaAscpOrderCancelTransferCmd {

    @Autowired
    private IpBAlibabaAscpOrderCancelService orderCancelService;
    @Autowired
    private AlibabaAscpOrderCancelTransferProcessImpl cancelTransferProcess;

    @Override
    public ValueHolderV14<ReturnOrderResult> startCancelTransfer(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<ReturnOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("startCancelTransfer,ChannelType：{}", orderNo, "startCancelTransfer"), transferOrderRequest.getChannelType());

                }
                if (transferOrderRequest.getChannelType() == ChannelType.ALIBABAASCP) {
                    IpBAlibabaAscpOrderCancelRelation cancelRelation = orderCancelService.getIpBAlibabaAscpOrderCancelRelation(orderNo);
                    if (cancelRelation != null) {
                        ProcessStepResultList processStepResultList = cancelTransferProcess.start(cancelRelation,
                                false, transferOrderRequest.getOperateUser());

                        if (!processStepResultList.isProcessSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFailedProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("startCancelTransfer,异常信息:{}", "startCancelTransfer"), Throwables.getStackTraceAsString(ex));
        }
        return resultValueHolderV14;
    }
}
