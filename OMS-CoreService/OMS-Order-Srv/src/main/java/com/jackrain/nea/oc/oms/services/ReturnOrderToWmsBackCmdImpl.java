package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.ReturnOrderToWmsBackCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * description：退单传wing结果返回
 *
 * <AUTHOR>
 * @date 2021/7/16
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class ReturnOrderToWmsBackCmdImpl implements ReturnOrderToWmsBackCmd {
    @Autowired
    private RefundOrderToWmsBackService refundOrderToWmsBackService;

    @Value("${r3.oc.oms.return.order.wing.noticeBack.newMethod:true}")
    private boolean noticeBackUseNewMethod;

    @Value("${r3.oc.oms.return.order.wing.resultBack.newMethod:true}")
    private boolean resultBackUseNewMethod;



    @Override
    public ValueHolderV14 noticeBack(String msg) {

        if (noticeBackUseNewMethod) {
            return refundOrderToWmsBackService.wingToWmsNoticeBack(msg);
        }
        return refundOrderToWmsBackService.handleWmsNoticeReturnBack(msg);
    }

//    @Override
//    public ValueHolderV14 resultBack(String msg) {
//
//        if (resultBackUseNewMethod) {
//            return refundOrderToWmsBackService.wingToWmsInResultBack(msg);
//        }
//        return refundOrderToWmsBackService.wmsInResultBack(msg);
//    }

    @Override
    public ValueHolderV14 preSaleSinkResultBack(String msg) {
        return refundOrderToWmsBackService.preSaleSinkResultBack(msg);
    }

    @Override
    public ValueHolderV14 preSaleSinkOrderDeliveryCallBack(String msg) {
        return refundOrderToWmsBackService.preSaleSinkOrderDeliveryCallBack(msg);
    }

    @Override
    public JSONObject customerQueryOrderOut(String param) {
        return refundOrderToWmsBackService.customerQueryOrderOut(param);
    }

}
