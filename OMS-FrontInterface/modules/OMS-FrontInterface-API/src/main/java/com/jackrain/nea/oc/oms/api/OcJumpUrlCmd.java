package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 退换货订单列表跳转页面接口
 *
 * @author: 郑立轩
 * @since: 2019/3/12
 * create at : 2019/3/12 15:03
 */
public interface OcJumpUrlCmd {
    ValueHolderV14<String> jumpUrl(JSONObject
                                           object, User user);
}
