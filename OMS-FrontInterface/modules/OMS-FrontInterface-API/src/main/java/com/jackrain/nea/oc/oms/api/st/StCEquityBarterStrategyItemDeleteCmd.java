package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.dto.StCEquityBarterStrategyItemDeleteRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除命令接口
 **/
public interface StCEquityBarterStrategyItemDeleteCmd {

    /**
     * 删除对等换货策略明细
     *
     * @param request 删除请求参数
     * @param user 操作用户
     * @return 删除结果
     */
    ValueHolderV14<String> deleteStrategyItems(StCEquityBarterStrategyItemDeleteRequest request, User user);
}
