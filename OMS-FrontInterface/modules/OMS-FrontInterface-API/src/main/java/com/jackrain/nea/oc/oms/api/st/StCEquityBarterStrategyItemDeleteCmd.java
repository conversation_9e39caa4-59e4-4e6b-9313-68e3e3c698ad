package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除命令接口
 **/
public interface StCEquityBarterStrategyItemDeleteCmd {

    /**
     * 删除对等换货策略明细
     *
     * @param shopType 店铺类型：1-指定店铺；2-所选指定店铺
     * @param shopIds 店铺id的list(当店铺类型为指定店铺时必填)
     * @param exchangeSkuId 换货商品id
     * @param exchangeQty 换货数量
     * @param equitySkuId 对等商品id
     * @param equityQty 对等数量
     * @param user 操作用户
     * @return 删除结果
     */
    ValueHolderV14<String> deleteStrategyItems(Integer shopType,
                                               List<Long> shopIds,
                                               Long exchangeSkuId,
                                               BigDecimal exchangeQty,
                                               Long equitySkuId,
                                               BigDecimal equityQty,
                                               User user);
}
