package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OrderCancleWmsCmd;
import com.jackrain.nea.oc.oms.services.OrderCancleWmsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-05-13 10:36
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderCancleWmsCmdImpl implements OrderCancleWmsCmd {

    @Autowired
    private OrderCancleWmsService orderCancleWmsService;

    @Override
    public ValueHolderV14 cancleWms(List<Long> ids, User loginUser) {
        return orderCancleWmsService.cancleWms(ids, loginUser);
    }
}
