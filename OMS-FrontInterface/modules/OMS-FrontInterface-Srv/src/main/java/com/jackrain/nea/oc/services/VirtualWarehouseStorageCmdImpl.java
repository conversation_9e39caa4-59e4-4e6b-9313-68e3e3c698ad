package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.VirtualWarehouseStorageCmd;
import com.jackrain.nea.oc.oms.services.VirtualWarehouseStorageService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 退换货订单-虚拟仓库入库
 *
 * @author: tqh
 * @since: 2019/10/9
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class VirtualWarehouseStorageCmdImpl implements VirtualWarehouseStorageCmd {
    @Autowired
    VirtualWarehouseStorageService virtualWarehouseStorageService;

    @Override
    public ValueHolderV14 execute(List<Long> ids, User user) throws NDSException {
        return virtualWarehouseStorageService.virtualWarehouseStorage(ids, user);
    }
}
