package com.jackrain.nea.oc.services.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFInvoiceApplyTransCmd;
import com.jackrain.nea.oc.oms.services.invoice.AcFInvoiceApplyTransService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 发票申请手工转换
 * @author: caomalai
 * @create: 2022-09-17 13:56
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFInvoiceApplyTransCmdImpl extends CommandAdapter implements AcFInvoiceApplyTransCmd {
    @Autowired
    private AcFInvoiceApplyTransService acFInvoiceApplyTransService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("手动转换 AcFInvoiceApplyTransCmdImpl param：{}","发票申请") , param);
        }
        User user = session.getUser();
        try {
            return acFInvoiceApplyTransService.transfer(param, user);
        }catch(Exception e){
            log.error(LogUtil.format("手动转换异常","发票申请"),e);
            return ValueHolderUtils.fail("手动转换失败！"+e.getMessage());
        }
    }
}
