package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.StCEquityBarterStrategyItemDeleteCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除命令实现
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCEquityBarterStrategyItemDeleteCmdImpl implements StCEquityBarterStrategyItemDeleteCmd {

//    @Autowired
//    private StCEquityBarterStrategyItemDeleteService deleteService;

    @Override
    public ValueHolderV14<String> deleteStrategyItems(Integer shopScope,
                                                      List<Long> shopIds,
                                                      Long exchangeSkuId,
                                                      BigDecimal exchangeQty,
                                                      Long equitySkuId,
                                                      BigDecimal equityQty,
                                                      User user) {
//        return deleteService.deleteStrategyItems(shopScope, shopIds, exchangeSkuId,
//                                               exchangeQty, equitySkuId, equityQty, user);
        return null;
    }
}
