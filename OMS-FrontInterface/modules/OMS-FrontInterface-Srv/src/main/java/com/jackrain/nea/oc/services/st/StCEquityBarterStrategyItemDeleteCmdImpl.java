package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.dto.StCEquityBarterStrategyItemDeleteRequest;
import com.jackrain.nea.oc.oms.api.st.StCEquityBarterStrategyItemDeleteCmd;
import com.jackrain.nea.st.service.StCEquityBarterStrategyItemDeleteService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除命令实现
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCEquityBarterStrategyItemDeleteCmdImpl implements StCEquityBarterStrategyItemDeleteCmd {

    @Autowired
    private StCEquityBarterStrategyItemDeleteService deleteService;

    @Override
    public ValueHolderV14<String> deleteStrategyItems(StCEquityBarterStrategyItemDeleteRequest request, User user) {
        return deleteService.deleteStrategyItems(request, user);
    }
}
