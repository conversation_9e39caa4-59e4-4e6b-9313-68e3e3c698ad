package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCEquityBarterStrategyItemDeleteCmd;
import com.jackrain.nea.oc.oms.services.StCEquityBarterStrategyItemDeleteService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除命令实现
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCEquityBarterStrategyItemDeleteCmdImpl extends CommandAdapter implements StCEquityBarterStrategyItemDeleteCmd {

    @Autowired
    private StCEquityBarterStrategyItemDeleteService deleteService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return deleteService.execute(querySession);
    }
}
