package com.jackrain.nea.oc.oms.model.alibaba.ascp;

/**
 * 退款状态
 */
public enum AlibabaAscpOrderRefundRefundEnum {
    WAIT_SELLER_AGREE("WAIT_SELLER_AGREE", 1),
    WAIT_BUYER_RETURN_GOODS("WAIT_BUYER_RETURN_GOODS", 2),
    WAIT_SELLER_CONFIRM_GOODS("WAIT_SELLER_CONFIRM_GOODS", 3),
    SUCCESS("SUCCESS", 4),
    SELLER_REFUSE_BUYER("SELLER_REFUSE_BUYER", 5),
    CLOSED("CLOSED", 6),
    SELLER_REFUSE_BUYER_RETURN_GOODS("SELLER_REFUSE_BUYER_RETURN_GOODS", 20);

    private String name;
    private Integer code;

    AlibabaAscpOrderRefundRefundEnum(String name, Integer code) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getValueByKey(Integer key) {

        String s = "";
        if (key == null) {
            return s;
        }
        for (AlibabaAscpOrderRefundRefundEnum e : AlibabaAscpOrderRefundRefundEnum.values()) {
            if (e.getCode().equals(key)) {
                s = e.getName();
                break;
            }
        }
        return s;
    }
}
