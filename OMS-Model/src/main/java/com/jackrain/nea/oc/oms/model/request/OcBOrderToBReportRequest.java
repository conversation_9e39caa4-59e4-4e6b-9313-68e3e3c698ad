package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OcBOrderToBReportRequest
 * @Description 新零售订单监控
 * <AUTHOR>
 * @Date 2024/11/25 17:43
 * @Version 1.0
 */
@Data
public class OcBOrderToBReportRequest implements Serializable {
    private static final long serialVersionUID = 1987203188484249142L;

    @JSONField(name = "CREATIONDATE")
    private String creationDate;

    private String startDate;

    private String endDate;

    @J<PERSON>NField(name = "TID")
    private String tid;

    @J<PERSON><PERSON>ield(name = "REMARK")
    private String remark;

    @JSONField(name = "USER_NICK")
    private String userNick;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpcShopId;

    @JSONField(name = "SKU_ECODE")
    private String skuEcode;

    @JSONField(name = "ORDER_STATUS")
    private List<String> orderStatusList;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_ID")
    private List<Long> cpCShopIdList;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private List<Long> logisticsIdList;

    @JSONField(name = "PS_C_PRO_ID")
    private List<Long> proIdList;

    @JSONField(name = "SALES_CENTER_ID")
    private List<Long> salesCenterIdList;

    @JSONField(name = "SALES_DEPARTMENT_ID")
    private List<Long> salesDepartmentIdList;

    private Long logisticsId;

    private Long salesDepartmentId;

    private String salesDepartmentCode;

    private List<Integer> omsOrderStatusList;

    @JSONField(name = "startindex")
    private Integer startindex;

    @JSONField(name = "range")
    private Integer range;

    private String db;

    private Long limitId;
}
