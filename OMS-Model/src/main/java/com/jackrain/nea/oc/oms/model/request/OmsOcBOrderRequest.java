package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/11/4 1:35 下午
 * @Version 1.0
 */
@Data
public class OmsOcBOrderRequest implements Serializable {
    /**
     * 封装订单主表对象
     */
    private OmsOcBOrder ocBOrder;
    /**
     * 封装订单明细表对象
     */
    private List<OmsOcBOrderItem> omsOcBOrderItemList;

    /**
     * 用户对象
     */
    private User user;


}
