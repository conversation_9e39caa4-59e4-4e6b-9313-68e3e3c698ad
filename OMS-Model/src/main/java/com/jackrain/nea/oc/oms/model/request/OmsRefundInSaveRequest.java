package com.jackrain.nea.oc.oms.model.request;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/7/19
 */

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OmsRefundInSaveRequest implements Serializable {


    private static final long serialVersionUID = -6460360084608862129L;

    /**
     * 入库类型
     * 1, 正常退单
     * 0, 无名件入库
     */
    private Integer returnInType;

    /**
     * 退单信息
     */
    private ReturnOrder returnOrder;

    /**
     * 商品信息
     */
    private List<OrderLine> orderLines;


    /**
     * 虚拟入库状态
     */
    private Integer virtualInStatus;

    @Data
    public static class ReturnOrder implements Serializable {

        private static final long serialVersionUID = 2048288129489484148L;

        /**
         * orderType 单据类型,THRK=退货入库单，HHRK=换货入库, B2BRK =  2b入库
         */
        private String orderType;

        /**
         * wms单据编号 returnOrderId 仓库系统订单编码
         */
        private String returnOrderId;

        /**
         * 外部业务编码,消息ID,用于去重,ISV对于同一请求，分配一个唯一性的编码。用来保证因为网络等原因导致重复传输，请求不会被重复处理
         */
        private String outBizCode;

        /**
         * 退换货单号/入库通知单号: 退货单编码,必填
         */
        private String returnOrderCode;
        /**
         * 仓库 仓库编码,必填
         */
        private String warehouseCode;

        /**
         * 库位编码（富乐）
         */
        private String storageLocation;

        /**
         * 物流 物流公司编码 必填
         */
        private String logisticsCode;

        /**
         * 物流公司名称
         */
        private String logisticsName;

        /***
         * 运单号
         */
        private String expressCode;

        /**
         * 退货原因
         */
        private String returnReason;

        /**
         * 确认入库时间,YYYY-MM-DDHH:MM:SS
         */
        private String orderConfirmTime;

        /**
         * 备注
         */
        private String remark;

        private SenderInfo senderInfo;

        private JSONObject extendProps;

    }

    @Data
    public static class SenderInfo implements Serializable {

        private static final long serialVersionUID = 8945452681124421997L;

        /**
         * 姓名
         */
        private String name;

        /**
         * 移动电话
         */
        private String mobile;

        /**
         * 公司名称
         */
        private String company;

        /**
         * 邮编
         */
        private String zipCode;

        /**
         * 固定电话
         */
        private String tel;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 国家二字码
         */
        private String countryCode;

        /**
         * 省份
         */
        private String province;

        /**
         * 城市
         */
        private String city;

        /**
         * 区域
         */
        private String area;

        /**
         * 村镇
         */
        private String town;

        /**
         * 详细地址
         */
        private String detailAddress;

        /**
         * 备注
         */
        private String remark;

    }

    @Data
    public static class OrderLine implements Serializable {

        private static final long serialVersionUID = -2070152697329891709L;

        /**
         * 单据行号
         */
        private String orderLineNo;

        /**
         * 是否异常标记（0=正常，1=异常）
         */
        private Integer abnormalFlag;

        /**
         * 交易平台订单
         */
        private String sourceOrderCode;
        /**
         * 平台交易子订单编码
         */
        private String subSourceCode;
        /**
         * 货主编码 必填
         */
        private String ownerCode;
        /**
         * 商品编码 必填
         */
        private String itemCode;
        /**
         * 商品仓储系统编码
         */
        private String itemId;
        /**
         * 库存类型(ZP=正品;CC=残次;JS=机损;XS=箱损;ZT=在途库存;默认为查所有类型的库存)
         */
        private String inventoryType;
        /**
         * 应收商品数量 必填
         */
        private BigDecimal planQty;
        /**
         * 批次编号
         */
        private String batchCode;
        /**
         * 生产日期(YYYY-MM-DD)
         */
        private String productDate;
        /**
         * 过期日期(YYYY-MM-DD)
         */
        private String expireDate;
        /**
         * 生产批号
         */
        private String produceCode;
        /**
         * 实收数量(要求batchs节点下所有的实收数量之和等于orderline中的实收数量) 必填
         */
        private BigDecimal actualQty;
        /**
         * 商品的二维码(类似电子产品的SN码;用来进行商品的溯源;多个二维码之间用分号;隔开)
         */
        private String qrCode;
        /**
         * 备注
         */
        private String remark;

        private List<Batch> batchs;

    }

    @Data
    public static class Batch implements Serializable {

        private static final long serialVersionUID = 2153325696600802287L;

        /**
         * 库存类型(ZP=正品;CC=残次;JS=机损;XS=箱损;ZT=在途库存;默认为查所有类型的库存)
         */
        private String inventoryType;

        /**
         * 批次编号
         */
        private String batchCode;
        /**
         * 生产日期(YYYY-MM-DD)
         */
        private String productDate;
        /**
         * 过期日期(YYYY-MM-DD)
         */
        private String expireDate;

        /**
         * 生产批号
         */
        private String produceCode;

        /**
         * 实收数量(要求batchs节点下所有的实收数量之和等于orderline中的实收数量) 必填
         */
        private BigDecimal actualQty;

        /**
         * 备注
         */
        private String remark;

    }

}
