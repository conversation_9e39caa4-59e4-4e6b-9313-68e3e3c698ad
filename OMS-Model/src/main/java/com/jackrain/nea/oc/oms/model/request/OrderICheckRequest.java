package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ming.fz
 * @Date: 2019-03-12
 * @Version 1.0
 */
@Data
public class OrderICheckRequest implements Serializable{
    @JSONField(name = "IDS")
    private Long[] ids;
    //SKU
    @JSONField(name = "TYPE")
    private Long type;
    //数量
    @JSONField(name = "ISCHECK")
    private Long isCheck;

    /**
     * 是否强制审核
     */
    @JSONField(name = "MANDATORY_AUDIT")
    private Boolean mandatoryAudit = false;


}
