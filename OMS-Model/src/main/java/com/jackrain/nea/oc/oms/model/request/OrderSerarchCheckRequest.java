package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: heliu
 * @since: 2019/5/5
 * create at : 2019/5/5 10:11
 */
@Data
public class OrderSerarchCheckRequest implements Serializable {

    @JSONField(name = "IDS")
    private Long[] ids;

    /**
     * 福袋
     */
    private Boolean isFortuneBag = false;

}