package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.relation.Page;
import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderSelectResult;

import java.io.Serializable;

/**
 * 查询订单-入参
 *
 * @author: xiwen.z
 * create at: 2019/3/15 0015
 */
public class QueryOrderRequest implements Serializable {

    private Page page; //分页信息

    private QueryOrderSelectResult queryInfo; // 下拉信息

    private QueryOrderCheckBoxResult status; // 订单状态信息

}
