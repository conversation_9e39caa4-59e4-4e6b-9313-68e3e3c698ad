    package com.jackrain.nea.oc.oms.model.request;

    import com.alibaba.fastjson.annotation.JSONField;
    import com.jackrain.nea.es.annotations.Field;
    import com.jackrain.nea.es.constans.FieldType;
    import lombok.Data;

    import java.io.Serializable;
    import java.math.BigDecimal;
    import java.util.Date;
    import java.util.List;

    /**
     * @ClassName ReverseO2oOrderRequest
     * @description:
     * @author: Lay.Jiang
     * @create: 2021-06-08 13:28
     **/
    @Data
    public class ReverseO2oOrderRequest implements Serializable{

        /** 固定一个店铺编码 */
        @JSONField(name = "CP_C_SHOP_ECODE")
        private String cpCShopEcode;

        /** 来源店铺编码 */
        @JSONField(name = "SOURCE_SHOP_CODE")
        private String sourceShopEcode;

        /** 来源店铺名称 */
        @JSONField(name = "SOURCE_SHOP_NAME")
        private String sourceShopName;

        /** 来源渠道 */
        @JSONField(name = "SOURCE_CHANNEL")
        private String sourceChannel;

        /** 来源平台编码 */
        @JSONField(name = "SOURCE_PLATFORM_CODE")
        private String sourcePlatformCode;

        /** TID	平台单号,合并单多个平台单号逗号隔开传输 */
        @JSONField(name = "TID")
        private String tid;

        /** 总部单据编号 */
        @JSONField(name = "O2O_SOURCE_CODE")
        private String o2oSourceCode;

        /** 卖家备注*/
        @JSONField(name = "SELLER_NICK")
        private String sellerNick;

        /** 买家昵称 */
        @JSONField(name = "BUYER_NICK")
        private String buyerNick;

        /** 收货人*/
        @JSONField(name = "RECEIVER_NAME")
        private String receiverName;

        /** 收货人电话*/
        @JSONField(name = "RECEIVER_PHONE")
        private String receiverPhone;

        /** 收货人手机号*/
        @JSONField(name = "RECEIVER_MOBILE")
        private String receiverMobile;

        /** 收货人省*/
        @JSONField(name = "RECEIVER_PROVINCE")
        private String receiverProvince;

        /** 收货人市*/
        @JSONField(name = "RECEIVER_CITY")
        private String receiverCity;

        /** 收货人区*/
        @JSONField(name = "RECEIVER_DISTRICT")
        private String receiverDistrict;

        /** 收货人地址*/
        @JSONField(name = "RECEIVER_ADDRESS")
        private String receiverAddress;

        /** 买家备注*/
        @JSONField(name = "BUYER_MESSAGE")
        private String buyerMessage;

        /** 卖家备注*/
        @JSONField(name = "SELLER_MEMO")
        private String sellerMemo;

        /** 下单时间*/
        @JSONField(name = "TRADE_CREATE_TIME")
        private Date tradeCreateTime;

        /** 支付时间*/
        @JSONField(name = "PAY_TIME")
        private Date payTime;

        /** 实付金额*/
        @JSONField(name = "PAYMENT")
        private BigDecimal payment;

        /** 邮费*/
        @JSONField(name = "POST_FEE")
        private BigDecimal postFee;

        /** 订单优惠金额*/
        @JSONField(name = "DISCOUNT_FEE")
        private BigDecimal discountFee;

        /** 调整金额*/
        @JSONField(name = "ADJUST_FEE")
        @Field(type = FieldType.Double)
        private BigDecimal adjustFee;

        /** 商品总数 */
        @JSONField(name = "NUM")
        @Field(type = FieldType.Double)
        private BigDecimal num;

        /** 备选字段01 */
        @JSONField(name = "RESERVE01")
        private String reserve01;

        /** 备选字段02 */
        @JSONField(name = "RESERVE02")
        private String reserve02;

        /** 备选字段03*/
        @JSONField(name = "RESERVE03")
        private String reserve03;

        /** 平台交易状态*/
        @JSONField(name = "STATUS")
        private String status;

        /** 出库时间*/
        @JSONField(name = "TRADE_END_TIME")
        private Date tradeEndTime;

        @JSONField(name = "ITEMS")
        private List<ReverseO2oOrderItem> items;

        @Data
        public static class ReverseO2oOrderItem implements Serializable {

            @JSONField(name = "OID")
            private String oid;

            @JSONField(name = "OUTER_SKU_ID")
            private String outerSkuId;

            @JSONField(name = "PRODUCT_CODE")
            private String productCode;

            @JSONField(name = "COLOR_CODE")
            private String colorCode;

            @JSONField(name = "SIZE_CODE")
            private String sizeCode;

            @JSONField(name = "NUM")
            private BigDecimal num;

            @JSONField(name = "PRICE")
            private BigDecimal price;

            @JSONField(name = "TITLE")
            private String title;

            @JSONField(name = "DISCOUNT_FEE")
            private BigDecimal discountFee;

            @JSONField(name = "ADJUST_FEE")
            private BigDecimal adjustFee;

        }
    }