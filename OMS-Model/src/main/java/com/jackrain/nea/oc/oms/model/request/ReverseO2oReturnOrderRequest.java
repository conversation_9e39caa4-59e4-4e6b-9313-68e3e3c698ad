package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ReverseO2oReturnOrderRequest
 * @description:
 * @author: Lay.Jiang
 * @create: 2021-06-10 13:16
 **/
@Data
public class ReverseO2oReturnOrderRequest implements Serializable {
    /**
     * 总部单据编号
     */
    @JSONField(name = "O2O_SOURCE_CODE")
    private String o2oSourceCode;

    /**
     * 总部退单编号
     */
    @JSONField(name = "O2O_RETURN_CODE")
    private String o2oReturnCode;

    /**
     * 平台售后单号
     */
    @JSONField(name = "RETURN_NO")
    private String returnNo;

    /**
     * 来源店铺编码
     */
    @JSONField(name = "SOURCE_SHOP_CODE")
    private String sourceShopEcode;

    /**
     * 来源店铺名称
     */
    @JSONField(name = "SOURCE_SHOP_NAME")
    private String sourceShopName;

    /**
     * 区分订单的来源系统，固定值ZB
     */
    @JSONField(name = "SOURCE_CHANNEL")
    private String sourceChannel;

    /**
     * 退货原因
     */
    @JSONField(name = "RETURN_REASON")
    private String returnReason;

    @JSONField(name = "COMPANY_CODE")
    private String companyCode;

    @JSONField(name = "COMPANY_NAME")
    private String companyName;

    /**
     * 退回物流单号
     */
    @JSONField(name = "LOGISTICS_NO")
    private String logisticsNo;

    /**
     * 买家昵称
     */
    @JSONField(name = "BUYER_NICK")
    private String buyerNick;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    /**
     * 退款明细
     */
    @JSONField(name = "ITEM_LIST")
    private List<ReturnOrderItem> itemList;

    @Data
    public static class ReturnOrderItem implements Serializable {

        /**
         * 退货数量
         */
        @JSONField(name = "RETURN_QUANTITY")
        private BigDecimal returnQuantity;

        /**
         * 子明细ID
         */
        @JSONField(name = "OID")
        private String oid;

        /**
         * 商品sku
         */
        @JSONField(name = "SKU")
        private String sku;

        @JSONField(name = "PRODUCT_CODE")
        private String productCode;

        @JSONField(name = "COLOR_CODE")
        private String colorCode;

        @JSONField(name = "SIZE_CODE")
        private String sizeCode;
    }
}