package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * HOLD单方案
 *
 * <AUTHOR>
 * @Date 2020/07/02 15:21:26
 */
@Data
public class StCHoldOrderRequest implements Serializable {
    @JSONField(name = "ST_C_HOLD_ORDER")
    private StCHoldOrderDO stCHoldOrder;

    @JSONField(name="ST_C_HOLD_ORDER_ITEM")
    private List<StCHoldOrderItemDO> stCHoldOrderItemDOList;

    @JSONField(name="ST_C_HOLD_PROVINCE_ITEM")
    private List<StCHoldProvinceItemDO> stCHoldProvinceItemDOList;

    @JSONField(name = "isStrategyTime")
    private String isStrategyTime; // 是否 调整策略时间

    @JSONField(name = "ID")
    private Long id;

    // 方案名称
    @JSONField(name = "ENAME")
    private String ename;

    // 店铺ID
    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;

    // 店铺名称
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    // 日期类型 1:下单时间 2:支付时间
    @JSONField(name = "DAY_TYPE")
    private Integer dayType;

    // 方案开始时间
    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    // 方案结束时间
    @JSONField(name = "END_TIME")
    private Date endTime;

    // hold单原因 1:直播hold单 2:买家HOLD单
    @JSONField(name = "HOLD_ORDER_REASON")
    private Integer holdOrderReason;

    // 订单标识
    @JSONField(name = "ORDER_FLAG")
    private String orderFlag;

    // 释放时点 1:指定时点释放 2:固定时长后释放
    @JSONField(name = "RELEASE_TIME_TYPE")
    private Integer releaseTimeType;

    // 指定时点释放
    @JSONField(name = "RELEASE_TIME")
    private Date releaseTime;

    // 固定时长后释放
    @JSONField(name = "FIXED_DURATION")
    private Integer fixedDuration;

    // 是否自动释放 Y:是 N:否
    @JSONField(name = "IS_AUTO_RELEASE")
    private String isAutoRelease;

    // 时间单位 1:分钟 2:小时 3:天
    @JSONField(name = "TIME_UNIT")
    private Integer timeUnit;

    // 方案状态 1:未审核 2:已审核 3:已作废 4:已结案
    @JSONField(name = "ESTATUS")
    private Integer estatus;

    // 具体hold直播场次
    @JSONField(name = "HOLD_LIVE_EVENTS")
    private String holdLiveEvents;


    // 提前时间
    @JSONField(name = "ADVANCE_TIME")
    private Integer advanceTime;

    // hold单原因
    @JSONField(name = "HOLD_DETENTION_ORDER_REASON")
    private Integer holdDetentionOrderReason;
}
