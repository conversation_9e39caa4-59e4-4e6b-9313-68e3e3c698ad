package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 退货仓库.修改
 *
 * @author: xiWen.z
 * create at: 2019/7/16 0016
 */
@Data
public class UpdateReturnOrderRequest implements Serializable {

    /**
     * 单号
     */
    @JSONField(name = "IDS")
    private Long[] ids;

    /**
     * 仓库
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_IN_ID")
    private Long cpCPhyWarehouseInId;

    /**
     * 退回物流公司id
     */
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    /**
     * 退回物流公司编码
     */
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    /**
     * 退回物流公司名称
     */
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    /**
     * 退回物流d单号
     */
    @JSONField(name = "LOGISTICS_NUMBER")
    private String LogisticsNumber;

    /**
     * 是否传WMS
     */
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;
}
