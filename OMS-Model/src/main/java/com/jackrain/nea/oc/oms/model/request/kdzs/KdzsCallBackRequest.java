package com.jackrain.nea.oc.oms.model.request.kdzs;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 快递助手回调数据类
 * <AUTHOR>
 * @date 2022年04月01日 20:07
 */
@Data
@ToString
public class KdzsCallBackRequest implements Serializable {

    /**
     * 快递公司CODE
     */
    private String cpCode;
    /**
     *  物流单号
     */
    private String mailNo;
    /**
     * 完整的物流轨迹信息
     */
    private List<LogisticsFullTrace> logisticsFullTraceList;
    /**
     * 当前最新物流信息描述内容
     */
    private String logisticsStatusDesc;
    /**
     * 当前最新物流状态
     */
    private String logisticsStatus;

}
