package com.jackrain.nea.oc.oms.model.request.naika;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName NaiKaOrderQueryRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/20 20:03
 * @Version 1.0
 */
@Data
public class NaiKaOrderQueryRequest implements Serializable {

    /**
     * 平台单号
     */
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    /**
     * 传奶卡系统
     */
    @JSONField(name = "TO_NAIKA_STATUS")
    private List<String> toNaiKaStatus;

    /**
     * 订单状态
     */
    @JSONField(name = "ORDER_STATUS")
    private List<String> orderStatus;

    /**
     * 店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private List<Long> cpcShopId;

    /**
     * 单据单号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 单据业务类型
     */
    @JSONField(name = "BUSINESS_TYPE_ID")
    private List<String> businessTypeId;

    /**
     * 单据业务类型
     */
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private List<String> businessTypeCode;

    /**
     * 时间
     */
    @JSONField(name = "CREATIONDATE")
    private String creationDate;

    /**
     * 奶卡卡号
     */
    @JSONField(name = "CARD_CODE")
    private String cardCode;
}
