package com.jackrain.nea.oc.oms.model.request.naika;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName NaiKaReturnQueryRequest
 * @Description 奶卡退单管理列表
 * <AUTHOR>
 * @Date 2022/7/25 14:03
 * @Version 1.0
 */
@Data
public class NaiKaReturnQueryRequest implements Serializable {

    /**
     * 平台单号
     */
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    /**
     * 店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private List<Long> cpcShopId;

    /**
     * 单据单号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 时间
     */
    @JSONField(name = "CREATIONDATE")
    private String creationDate;

    /**
     * 单据类型
     */
    @JSONField(name = "BILL_TYPE")
    private List<String> billType;

    /**
     * 单据业务类型
     */
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private List<String> businessTypeCode;


    /**
     * 退款状态
     */
    @JSONField(name = "RETURN_STATUS")
    private List<String> returnStatus;

    /**
     * 自动退款状态
     */
    @JSONField(name = "CARD_AUTO_VOID")
    private List<String> cardAutoVoid;

    /**
     * 平台退款单号
     */
    @JSONField(name = "T_RETURN_ID")
    private String tReturnId;
}
