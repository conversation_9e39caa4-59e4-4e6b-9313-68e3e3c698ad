package com.jackrain.nea.oc.oms.model.request.sap;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 田普
 * @since : 2020/6/23
 * create at : 2020/6/23 15:17
 */
@Data
public class B2cOrderOutStockRequest implements Serializable {
    private OcBOrder ocBOrder;
    private List<OcBOrderItem> orderItemList;
}
