package com.jackrain.nea.oc.oms.model.request.sap;

import com.jackrain.nea.oc.oms.extmodel.OcBReturnOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 田普
 * @since : 2020/6/29
 * create at : 2020/6/29 23:16
 */
@Data
public class B2cOrderRefundRequest implements Serializable {
    private OcBReturnOrder ocBReturnOrder;
    private List<OcBReturnOrderItem> returnOrderItemList;
}
