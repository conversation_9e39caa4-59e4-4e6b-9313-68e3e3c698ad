package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单审核
 *
 * @author: ming.fz
 * create at: 2019/3/15
 */
@Data
public class CheckOrderResult implements Serializable {    //订单id
    private Long id;
    //有相同订单存在时，返回给前端的提示标识 1为有可合并的订单
    private String isActive;
    //可合并订单提示消息
    private String message;
    //审核失败原因
    private OmsAuditFailedReason failedReason;
}
