package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: chenxiulou
 * @description: 反馈寻仓结果相应
 * @since: 2019-07-01
 * create at : 2019-07-01 11:18
 */
@Data
public class FeedbackDeliveryResult implements Serializable {
    //订单号
    @JSONField(name = "order_sn")
    private String order_sn;
    //结果代码：SUCCESS代表成功，FAIL代表失败
    @JSONField(name = "code")
    private String code;
    //提示信息
    @JSONField(name = "msg")
    private String msg;
    //当前状态
    @JSONField(name = "status")
    private String status;
}
