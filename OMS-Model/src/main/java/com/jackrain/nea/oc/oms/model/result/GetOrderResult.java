package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wa<PERSON><PERSON><PERSON>
 * @Date: 2019-03-07 19:49
 * @Version 1.0
 */
//订单主表
@Data
public class GetOrderResult extends OcBOrder implements Serializable {

    @JSONField(name = "PLATFORM_NAME")
    private String platformName;

    @JSONField(name = "PAY_NAME")
    private String payName;

    @JSONField(name = "ORDER_TIME")
    private String orderTime;

    @JSONField(name = "ORDER_ADDRESS")
    private String orderAddress;

    @JSONField(name = "ORDER_STATUS_NAME")
    private String orderStatusName;

    @JSONField(name = "PLATFORM_STATUS_NAME")
    private String platformStatusName;

    @JSONField(name = "PAY_DATE")
    private String payDate;

    @JSONField(name = "PRESALE_DEPOSIT_DATE")
    private String presaleDepositDate;

    /**
     * 标签处理集合
     */
    @JSONField(name = "ORDERTAGLIST")
    private List<QueryOrderTagResult> orderTagList;

    // @20200804 task#23951 退款单需要的买家手机号码
    @JSONField(name = "VIP_PHONE")
    private String vipPhone;


    /**
     * 门店接单状态 2020/09/03 黄志优
     */
    @JSONField(name = "STORE_DELIVERY_STATUS_NAME")
    private String storeDeliveryStatusName;

    /**
     * <AUTHOR>
     * @Date 16:59 2021/7/29
     * @Description 单据类型
     */
    @JSONField(name = "ORDER_TYPE_NAME")
    private String ordertypeName;

    @JSONField(name = "ORIG_ORDER_SHOP_TITLE")
    private String origOrderShopTitle;

}
