package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 * 发票管理新增check
 */
@Data
public class InvoiceSaveCheckReceiverResult implements Serializable {

    private static final long serialVersionUID = 682725589481782838L;
    /**
     * 订单明细id
     */
    private List<Long> ids;
    /**
     * 订单id
     */
    private List<Long> ocBOrderIds;
    /**
     * 店铺id
     */
    private Long cpCShopId;
    /**
     * 平台单号
     */
    private String tid;

    /**
     * 对应开票明细总开票金额
     */
    private BigDecimal amt;
    /**
     * 收件人信息
     */
    private ReceiverInfo receiverInfo;

    @Data
    public static class ReceiverInfo implements Serializable {
        private static final long serialVersionUID = -4671878362459780558L;
        /**
         * 邮箱
         */
        private String email;
        /**
         * 手机
         */
        private String phone;
        /**
         * 收件人
         */
        private String receiver;
        /**
         * 收件人地址
         */
        private String receiverAddress;
    }
}
