package com.jackrain.nea.oc.oms.model.result;


/**
 * @Auther: 黄志优
 * @Date: 2020/12/7 11:47
 * @Description:
 */
public enum MergeReturnResult {

    MERGE_SUCCESS(1, "合单成功"),
    MERGE_PART_SUCCESS(2, "合单部分成功"),
    MERGE_IS_NULL(3, "合单数据为空"),
    MERGE_FAILED(0, "合单失败");

    private Integer code;
    private String message;

    MergeReturnResult(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
