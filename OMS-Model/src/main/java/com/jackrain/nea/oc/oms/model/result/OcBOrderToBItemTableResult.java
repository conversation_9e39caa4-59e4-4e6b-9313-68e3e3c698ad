package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午4:20
 * @describe :
 */

@Data
public class OcBOrderToBItemTableResult implements Serializable{

    @J<PERSON>NField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @J<PERSON><PERSON>ield(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @J<PERSON>NField(name = "ARRIVAL_DAYS")
    private Integer arrivalDays;

    @J<PERSON><PERSON>ield(name = "UNFULLCAR_COST")
    private BigDecimal unfullcarCost;

    @JSONField(name = "TRANSFER_COST")
    private BigDecimal transferCost;

    @J<PERSON><PERSON>ield(name = "TOTAL_COST")
    private BigDecimal totalCost;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    /*是否辐射仓*/
    @JSONField(name = "IS_RADIATION_WAREHOUSE")
    private String isRadiationWarehouse;

    /**
     * 调拨工厂（1-山东工厂；2-河北工厂）
     */
    @JSONField(name = "FACTORY")
    private Integer factory;

    @JSONField(name = "WAREHOUSE_COST")
    private BigDecimal warehouseCost;


    /**
     * 0是零担 1是整车
     */
    @JSONField(name = "TYPE")
    private Integer type;

}
