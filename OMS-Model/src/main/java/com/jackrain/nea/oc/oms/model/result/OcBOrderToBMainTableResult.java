package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午4:03
 * @describe :
 */
@Data
public class OcBOrderToBMainTableResult implements Serializable {

    @J<PERSON><PERSON>ield(name = "SOURCE_CODE")
    private String sourceCode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @J<PERSON><PERSON>ield(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @J<PERSON>NField(name = "SELLER_MEMO")
    private String sellerMemo;
    
    @J<PERSON><PERSON>ield(name = "INSIDE_REMARK")
    private String insideRemark;
}
