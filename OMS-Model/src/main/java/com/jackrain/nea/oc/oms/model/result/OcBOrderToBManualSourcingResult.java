package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午3:32
 * @describe :
 */
@Data
public class OcBOrderToBManualSourcingResult implements Serializable {

    private OcBOrderToBMainTableResult mainTableResult;

    private List<OcBOrderToBItemTableResult> itemTableResultList;

    private List<OcBOrderToBStorageInfoResult> storageInfoResultList;

    private List<OcBOrderStoOutInfoResult> stoOutInfoResultList;

}
