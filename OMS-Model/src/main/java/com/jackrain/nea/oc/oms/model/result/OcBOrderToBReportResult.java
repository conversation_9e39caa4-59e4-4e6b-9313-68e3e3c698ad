package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName OcBOrderToBReportResult
 * @Description 新零售订单监控
 * <AUTHOR>
 * @Date 2024/11/25 19:43
 * @Version 1.0
 */
@Data
public class OcBOrderToBReportResult implements Serializable {
    private static final long serialVersionUID = 4976029996809838135L;


    private Long id;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE", format = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 平台单号
     */
    @JSONField(name = "TID")
    private String tid;

    /**
     * OM单号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 店铺
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    /**
     * 商品名称
     */
    @JSONField(name = "SKU_ENAME")
    private String skuEname;

    @JSONField(name = "SKU_ECODE")
    private String skuEcode;

    /**
     * 效期范围
     */
    @JSONField(name = "EXPIRY_DATE_RANGE")
    private String expiryDateRange;

    /**
     * 运营名称
     */
    @JSONField(name = "USER_NICK")
    private String userNick;

    /**
     * 区域
     */
    @JSONField(name = "SALES_DEPARTMENT_NAME")
    private String salesDepartmentName;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCWarehouseName;

    /**
     * 数量
     */
    @JSONField(name = "QTY")
    private Integer qty;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "ORDER_STATUS")
    private Integer orderStatus;

    @JSONField(name = "ORDER_STATUS_NAME")
    private String orderStatusName;

    @JSONField(name = "GIFT_ATTR")
    private String giftAttr;

    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @JSONField(name = "REFUND_STATUS")
    private String refundStatus;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

}
