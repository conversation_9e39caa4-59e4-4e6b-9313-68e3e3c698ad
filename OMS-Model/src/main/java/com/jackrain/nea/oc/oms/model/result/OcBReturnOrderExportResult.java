package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.extmodel.OcBReturnOrderItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 李龙飞
 * @create: 2019-06-05 14:53
 **/
@Data
public class OcBReturnOrderExportResult implements Serializable {

    List<ExtOcBReturnOrder> orderList;
    List<ExtOcBReturnOrderExchange> exchangeList;
    List<ExtOcBReturnOrderRefund> refundList;
    List<OcBReturnOrderItem> itemList;
}
