package com.jackrain.nea.oc.oms.model.result;


import com.jackrain.nea.oc.oms.vo.ExecuteErrorVO;
import lombok.Data;

import java.util.List;

@Data
public class OrderDetentionFutureResult {

    private List<ExecuteErrorVO> failList;

    private Integer successCount;

    private Integer failCount;

    public OrderDetentionFutureResult(List<ExecuteErrorVO> failList, Integer successCount) {
        this.failList = failList;
        this.successCount = successCount;
        this.failCount = failList.size();
    }

}
