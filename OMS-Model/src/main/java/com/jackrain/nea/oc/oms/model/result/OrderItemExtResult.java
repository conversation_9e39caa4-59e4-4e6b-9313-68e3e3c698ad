package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.model.SgBPhyInStorageItemExt;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 拆单初始化查询实体
 *
 * @author: heliu
 * @since: 2019/7/5
 * create at : 2019/7/5 10:20
 */
@Data
public class OrderItemExtResult implements Serializable {

    /**
     * 原订单id
     */
    private Long orig_order_id;

    /**
     * 原订单明细id
     */
    private Long orig_order_item_id;

    /**
     * 商品code
     */
    private String ps_c_pro_ecode;

    /**
     * 商品名称
     */
    private String ps_c_pro_ename;

    /**
     * 颜色id
     */
    private Long ps_c_clr_id;

    /**
     * 颜色编码
     */
    private String ps_c_clr_ecode;

    /**
     * 颜色名称
     */
    private String ps_c_clr_ename;

    /**
     * 尺寸id
     */
    private Long ps_c_size_id;

    /**
     * 尺寸编码
     */
    private String ps_c_size_ecode;

    /**
     * 尺寸名称
     */
    private String ps_c_size_ename;

    /**
     * 条码id
     */
    private Long ps_c_sku_id;

    /**
     * 条码编码
     */
    private String ps_c_sku_ecode;

    /**
     * 条码名称
     */
    private String ps_c_sku_name;

    /**
     * 标准价
     */
    private BigDecimal price_list;

    /**
     * 成交价格
     */
    private BigDecimal price;

    /**
     * 优惠金额
     */
    private BigDecimal amt_discount;

    /**
     * 调整金额
     */
    private BigDecimal adjust_amt;

    /**
     * 购买数量
     */
    private BigDecimal qty;

    /**
     * 是否赠品
     */
    private Integer is_gift;

    /**
     * 原发货实体仓库id
     */
    private Long cp_c_phy_warehouse_id;

    /**
     * 原发货实体仓库ecode
     */
    private String cp_c_phy_warehouse_ecode;

    /**
     * 原发货实体仓库ename
     */
    private String cp_c_phy_warehouse_ename;

    /**
     * sku库存查询集合
     */
    private List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExt;

    /**
     * 待拆分数量
     */
    private BigDecimal waiting_split_num;

    /**
     * 拆分数量
     */
    private BigDecimal split_num;

    /**
     * 商品类型
     */
    private Long proType;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 平台单号
     */
    private Integer platform;

    /**
     * 聚合仓id
     */
    private  Map<Long,String> shareStoreIds;

}