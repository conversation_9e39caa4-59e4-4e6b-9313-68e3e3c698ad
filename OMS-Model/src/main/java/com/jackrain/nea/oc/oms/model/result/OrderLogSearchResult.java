package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * @author: 郑立轩
 * @since: 2019/4/23
 * create at : 2019/4/23 13:57
 */
@TableName(value = "oc_b_order_log")
@Data
public class OrderLogSearchResult {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    @JSONField(name = "BILL_NO")
    private String billNo;
    @JSONField(name = "USER_NAME")
    private String userName;
    @JSONField(name = "ERROR_INFO")
    private String errorInfo;
    @JSONField(name = "IP_ADDRESS")
    private String ipAddress;
    @JSONField(name = "LOG_PARAM")
    private String logParam;
    @JSONField(name = "LOG_MESSAGE")
    private String logMessage;
    @JSONField(name = "LOG_TYPE")
    private String logType;
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
    @JSONField(name = "OWNERNAME")
    private String ownerename;
    @JSONField(name = "ISACTIVE")
    private String isactive;
    @JSONField(name = "CREATIONDATE")
    private String creationdate;
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;


}
