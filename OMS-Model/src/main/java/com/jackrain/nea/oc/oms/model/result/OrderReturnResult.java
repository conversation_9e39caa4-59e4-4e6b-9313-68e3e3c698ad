package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 退换货列表返回实体类
 *
 * @author: 郑立轩
 * @since: 2019/3/27
 * create at : 2019/3/27 12:42
 */

@Data
public class OrderReturnResult extends OcBReturnOrder implements Serializable {

    /**
     * 所有sku
     */
    private List<JSONObject> allSkuItem;

    /**
     * 退单状态
     */
    @JSONField(name = "RETURN_STATUS_NAME")
    private String returnStatusName;

    /**
     * 是否传wms
     */
    @JSONField(name = "IS_PASS_WMS_NAME")
    private String isPassWmsName;

    /**
     * 实体仓
     */
    @JSONField(name = "SHOP_NAME")
    private String shopName;

    /***
     * 平台名称
     */
    @JSONField(name = "PLAT_NAME")
    private String platName;

    /**
     * 入库数量
     */
    @JSONField(name = "QTY_IN")
    private BigDecimal qtyIn;

    /**
     * 退货类型
     */
    @JSONField(name = "RETURN_PRO_TYPE_NAME")
    private String returnProTypeName;

    /**
     * 拦截状态
     */
    @JSONField(name = "INTERCERPT_STATUS_NAME")
    private String intercerptStatusName;

    /**
     * 退货状态
     */
    @JSONField(name = "PRO_RETURN_STATUS_NAME")
    private String proReturnStatusName;

    /**
     * 发货实体仓库.名称CP_C_PHY_WAREHOUSE_ID_NAME
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID_NAME")
    private String cpCPhyWarehouseIdName;

    /**
     * 入库实体仓库.名称
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_IN_ID_NAME")
    private String cpCPhyWarehouseInIdName;

    /**
     * sap 状态
     */
    @JSONField(name = "TO_SAP_STATUS_NAME")
    private String toSapStatusName;

    @JSONField(name = "IS_RESERVED_NAME")
    private String isReservedName;

    /**
     * 确认状态
     */
    @JSONField(name = "CONFIRM_TYPE_NAME")
    private String confirmTypeName;

    /**
     * 同步平台退款状态
     */
    @JSONField(name = "PLATFORM_REFUND_STATUS_NAME")
    private String platformRefundStatusName;
//    /**
//     * 同步奶卡系统状态
//     */
//    private Integer toNaikaStatus;
//
//    /**
//     * 同步奶卡状态描述
//     */
//    private String toNaikaStatusDesc;

    /**
     * 扩展退货表
     */
    private List<OcBReturnOrderRefund> ocBReturnOrderRefunds;

    /**
     * 扩展换货表
     */
    private List<OcBReturnOrderExchange> ocBReturnOrderExchanges;

    public OrderReturnResult() {
    }


}
