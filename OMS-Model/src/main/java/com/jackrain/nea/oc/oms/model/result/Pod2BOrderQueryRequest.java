package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName Pod2BOrderQueryRequest
 * @Description
 * <AUTHOR>
 * @Date 2024/8/29 16:51
 * @Version 1.0
 */
@Data
public class Pod2BOrderQueryRequest implements Serializable {
    private static final long serialVersionUID = -2196270228418118117L;

    /**
     * 第几页
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 出库通知单号
     */
    private String onNo;

    /**
     * 发货仓库
     */

    private String warehouseCode;

    /**
     * 物流公司
     */
    private String logisticsCode;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 省
     */
    private String buyerProvince;

    /**
     * 市
     */
    private String buyerCity;

    /**
     * 区
     */
    private String buyerArea;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 销售部门
     */
    private String salesDepartment;

    /**
     * 审核员
     */
    private String auditor;

    /**
     * 开始创建时间
     */
    private String startCreateTime;

    /**
     * 开始创建时间
     */
    private String endCreateTime;

    /**
     * 开始审核时间
     */
    private String startAuditTime;

    /**
     * 结束审核时间
     */
    private String endAuditTime;

    /**
     * 开始配货时间
     */
    private String startDistributeTime;

    /**
     * 结束配货时间
     */
    private String endDistributeTime;

    /**
     * 创建时间
     */
    private Date createDate;


    /**
     * 仓库名称
     */
    private String warehouseName;
}
