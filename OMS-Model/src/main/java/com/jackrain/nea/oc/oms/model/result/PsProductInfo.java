package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-21
 * create at : 2019-05-21 11:19 AM
 * 调用俊鹏ps获取商品信息
 */
@Data
public class PsProductInfo {
    private String IS_GIFT;
    private Long sizeId;
    private String PS_C_PRO_ECODE;
    private String colorName;
    private String GBCODE;
    private Long colorId;
    private String PS_C_PRO_ENAME;
    private BigDecimal PRICELIST;
    private String IS_GROUP;
    private String sizeName;
    private Long WareType;
    private String ECODE;
    private String sizeCode;
    private String colorCode;
    private Long PS_C_PRO_ID;
    private String SPEC;
    private Long skuId;
    /*
     * 吊牌价
     */
    private BigDecimal tagPrice;
    /*
     * 性别
     */
    private Long sex;
}
