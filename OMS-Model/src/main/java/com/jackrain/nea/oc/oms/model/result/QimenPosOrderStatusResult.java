package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 奇门POS-订单状态结果
 *
 * @Auther: 黄志优
 * @Date: 2020/8/30 13:16
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QimenPosOrderStatusResult implements Serializable {

    /**
     * 零售发货单的订单状态(配货中返回1；仓库发货返回2；平台发货返回3)
     */
    @JSONField(name = "cdo_status")
    private Integer cdoStatus;

    /**
     * 零售发货单的退款状态(无退款返回0；退款中返回1)
     */
    @JSONField(name = "is_refund")
    private Integer isRefund;

}
