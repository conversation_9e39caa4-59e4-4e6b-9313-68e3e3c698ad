package com.jackrain.nea.oc.oms.model.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 退换货订单列表查询结果集
 *
 * @author: 郑立轩
 * @since: 2019/3/11
 * create at : 2019/3/11 14:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryChangingOrRefundingResult implements Serializable {
    private List<OrderReturnResult> queryResult;
    //总数量
    private Integer totalSize;
    //总页数
    private Integer totalNum;
    //页大小
    private Integer pageSize;
    //当前页
    private Integer pageNum;


}
