package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.Data;

@Data
public class QueryEsListResult {
    /**
     * es 查询的真实数据
     */
    private String ids;
    /**
     * 是否深度分页
     */
    private ValueHolderV14 checkPageOverDeepSizeVh;
    /**
     * 是否unGzip的状态
     */
    private boolean unGzip;


    /**
     * 参数转换后的结构化
     */
    private JSONObject paramJsn;

    /**
     * 请求包装
     */
    private OrderQueryRequest queryDto;

}
