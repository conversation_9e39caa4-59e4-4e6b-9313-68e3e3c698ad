package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 封装的退换货订单表 和 退货入库明细表的数据
 *
 * @author: 夏继超
 * @since: 2019/4/3
 * create at : 2019/4/3 16:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOcbReturnAndRefundItemResult implements Serializable {
    //退换货订单信息
    OcBReturnOrder ocBReturnOrder;
    //实体仓的名字
    String shopName;
    //退货入库明细
    List<OcBReturnOrderRefund> productItems = new ArrayList<>();
}
