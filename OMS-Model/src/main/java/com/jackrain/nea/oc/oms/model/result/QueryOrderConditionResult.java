package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryOrderConditionResult implements Serializable {

    /**
     * 标签
     */
    private List<QueryOrderTagResult> label;

    /**
     * 下拉查询条件
     */
    private List<QueryOrderSelectResult> queryInfo;

    /**
     * 表头信息
     */
    private List<QueryOrderTableHeaderResult> tableHeader;

    /**
     * 高级查询
     */
    private List<JSONObject> highSearch;

}
