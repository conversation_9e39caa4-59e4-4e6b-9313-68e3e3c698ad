package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 根据sku分组统计金额model
 *
 * @author: heliu
 * @since: 2019/7/22
 * create at : 2019/7/22 13:51
 */
@Data
public class QueryOrderItemGroupByResult implements Serializable {

    /**
     * skuID
     */
    private String ooid;

    /**
     * 汇总的调整金额合计
     */
    private BigDecimal adjustAmtAmount;

    /**
     * 汇总的商品优惠金额
     */
    private BigDecimal amtDiscountAmount;

    /**
     * 汇总的分销金额合计
     */
    private BigDecimal distributionPriceAmount;

    /**
     * 汇总的明细平摊金额合计
     */
    private BigDecimal orderSplitAmount;


}