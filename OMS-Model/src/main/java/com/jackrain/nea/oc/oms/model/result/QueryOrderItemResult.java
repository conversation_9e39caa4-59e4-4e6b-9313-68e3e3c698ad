package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细查询结果
 *
 * @author: xiwen.z
 * create at: 2019/3/14 0014
 */
@Data
public class QueryOrderItemResult implements Serializable {

    /**
     * 明细Id
     */
    private Long proId;
    /**
     * skuID
     */
    private Long skuId;
    /**
     * 商品id
     */
    private Long psCproId;
    /**
     * 数量
     */
    private BigDecimal qty;
    /**
     * 成交价格
     */
    private BigDecimal price;
    /**
     * 单行实际成交价格
     */
    private BigDecimal realAmt;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 商品信息
     * 图像
     */
    private String image;
    /**
     * 商品编码
     */
    private String ecode;
    /**
     * 尺码
     */
    private String sizes;
    private Long sizeId;
    private String sizeEcode;
    /**
     * 颜色
     */
    private String clrs;
    private Long clrsId;
    private String clrsEcode;

    /**
     * 条码编码
     */
    private String skuEcode;
    /**
     * 商品名称
     */
    private String proEname;
    /**
     * 退款金额
     */
    private BigDecimal amtRefund;
    /**
     * 国标码
     */
    private String barCode;
    /**
     * 吊牌价
     */
    private BigDecimal priceList;
    /**
     * 规格
     */
    private String skuSpec;

    /**
     * 单件实际成交价
     */
    private BigDecimal amtRefundSingle;
    /**
     * 子订单编号(明细编号)
     */
    private String oOId;


    /**
     * 结算单价
     */
    private BigDecimal priceSettle;

    /**
     * 结算总额
     */
    private BigDecimal totPriceSettle;

    /**
     * 单件实际成交价(成交单价)
     */
    private BigDecimal priceActual;

    /**
     * 缺货数量
     */
    private BigDecimal qtyLost;

    /**
     * 性别
     */
    private Long sex;

    /**
     * 性别扩展
     */
    private String sexName;


    /**
     * 已退数量
     */
    private BigDecimal qtyRefund;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 吊牌价
     */
    @JSONField(name = "PRICE_TAG")
    private BigDecimal priceTag;
    /**
     * 京东优惠券金额
     */
    @JSONField(name = "AMT_JINGDONG_COUPON")
    private BigDecimal amtJingdongCoupon;

    /**
     * 是否是赠品
     */
    @JSONField(name = "IS_GIFT")
    private String isGift;

    /**
     * 赠品类型
     */
    @JSONField(name = "GIFT_TYPE")
    private String giftType;


    /**
     * 赠品挂靠关系
     */
    @JSONField(name = "GIFT_RELATION")
    private String giftRelation;

    /**
     * 平台商品条码
     */
    @JSONField(name = "PS_C_SKU_PT_ECODE")
    private String psCSkuPtEcode;

    /**
     * 平台商品名称
     */
    @JSONField(name = "PT_PRO_NAME")
    private String ptProName;
    /**
     * 条码名称
     */
    @JSONField(name = "PS_C_SKU_ENAME")
    private String psCSkuEname;
    /**
     * 商品类型
     */
    @JSONField(name = "pro_type")
    private Long proType;

    /**
     * 商品已退数量
     */
    @JSONField(name = "QTY_HAS_RETURN")
    private BigDecimal qtyHasReturn;

    /**
     * 已申请退货数量
     */
    @JSONField(name = "QTY_RETURN_APPLY")
    private BigDecimal qtyReturnApply;

    /**
     * 可退金额
     */
    @JSONField(name = "RETURNABLE_AMOUNT")
    private BigDecimal returnableAmount;
}
