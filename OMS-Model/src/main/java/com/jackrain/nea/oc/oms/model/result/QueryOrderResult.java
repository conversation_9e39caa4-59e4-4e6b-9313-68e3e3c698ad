package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderInvoiceInform;
import lombok.Data;

import java.util.List;

/**
 * 查询订单列表结果
 *
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
public class QueryOrderResult extends OcBOrder {

    /**
     * 标签处理集合
     */
    @JSONField(name = "ORDERTAGLIST")
    private List<QueryOrderTagResult> orderTagList;
    /**
     * 商品信息集合
     */
    @JSONField(name = "QUERYORDERITEMRESULTLIST")
    private List<QueryOrderItemResult> queryOrderItemResultList;

    /**
     * 订单类型
     */
    @JSONField(name = "ORDERTYPENAME")
    private String orderTypeName;
    /**
     * 订单状态
     */
    @JSONField(name = "ORDERSTATUSNAME")
    private String orderStatusName;
    /**
     * 平台
     */
    @JSONField(name = "PLATFORMNAME")
    private String platFormName;
    /**
     * 付款方式
     */
    @JSONField(name = "PAYTYPENAME")
    private String payTypeName;
    /**
     * 订单占单状态
     */
    @JSONField(name = "OCCUPYSTATUSNAME")
    private String occupyStatusName;
    /**
     * wms撤回状态
     */
    @JSONField(name = "WMSCANCELSTATUSNAME")
    private String wmsCancelStatusName;
    /**
     * 自动审核状态
     */
    @JSONField(name = "AUTOAUDITSTATUSNAME")
    private String autoAuditStatusName;
    /**
     * 是否生成开标通知
     */
    @JSONField(name = "ISGENINVOICENOTICENAME")
    private String isGeninvoiceNoticeName;
    /**
     * 退货状态
     */
    @JSONField(name = "RETURNSTATUSNAME")
    private String returnStatusName;
    /**
     * 是否生成零售调拨单
     */
    @JSONField(name = "ISTODRPNAME")
    private String isToDrpName;

    /**
     * 出库通知单.单据编号
     */
    @JSONField(name = "SG_B_PHY_OUT_NOTICES_BILL_NO")
    private String sgBPhyOutNoticesBillNo;

    /**
     * 收货信息
     */
    @JSONField(name = "JOIN_RECEIVER_ADDRESS")
    private String joinReceiverAddress;
    /**
     * 收货地址
     */
    @JSONField(name = "REGION_RECEIVER_ADDRESS")
    private String regionReceiverAddress;

    /**
     * 缺货数量
     */
    @JSONField(name = "TOT_QTY_LOST")
    private String totQtyLost;

    /**
     * 开票状态
     */
    @JSONField(name = "INVOICE_STATUS_NAME")
    private String invoiceStatusName;

    /**
     * 开票信息集合
     */
    @JSONField(name = "INVOICE_FORM_LIST")
    private List<OcBOrderInvoiceInform> invoiceFormList;

    /**
     * 刷单类型
     */
    @JSONField(name = "SCALPING_TYPE_NAME")
    private String scalpingTypeName;

    /**
     * 是否换货未入库
     */
    @JSONField(name = "RESERVE_BIGINT03_NAME")
    private String reserveBigint03Name;
    /**
     * JITX是否可发货
     */
    @JSONField(name = "IS_FORBIDDEN_DELIVERY_NAME")
    private String isForbiddenDeliveryName;

    /**
     * JITX要求合包
     */
    @JSONField(name = "JITX_REQUIRES_MERGE_NAME")
    private String jitxRequiresMergeName;

    /**
     * 淘宝.预售状态
     */
    @JSONField(name = "RESERVE_VARCHAR03_NAME")
    private String reserveVarchar03Name;

    /**
     * 锁单状态
     */
    @JSONField(name = "RESERVE_BIGINT09_NAME")
    private String reserveBigint09Name;

    /**
     * 传结算状态
     */
    @JSONField(name = "TO_SETTLE_STATUS_NAME")
    private String toSettleStatusName;

    /**
     * 传SAP状态
     */
    @JSONField(name = "TO_SAP_STATUS_NAME")
    private String toSapStatusName;

    /**
     * 序号
     */
    @JSONField(name = "SERIAL_NO")
    private Integer serialNo;

    /**
     * 退款状态
     */
    @JSONField(name = "REFUND_STATUS_NAME")
    private String refundStatusName;

    /**
     * 付款状态
     */
    @JSONField(name = "PAY_STATUS_NAME")
    private String payStatusName;

    /**
     * 平台订单状态
     */
    @JSONField(name = "PLATFORM_STATUS_NAME")
    private String platformStatusName;

    /**
     * 直播平台
     *
     * @20200803 wu.lb
     */
    @JSONField(name = "LIVE_PLATFORM_NAME")
    private String livePlatformName;

    // @20200804 task#23951 退款单需要的买家手机号码
    @JSONField(name = "VIP_PHONE")
    private String vipPhone;

    /**
     * 门店接单状态 2020/08/27 黄志优
     */
    @JSONField(name = "STORE_DELIVERY_STATUS_NAME")
    private String storeDeliveryStatusName;

    /**
     * 审核失败类型 20210117版本 黄志优增加
     */
    @JSONField(name= "AUDIT_FAILED_TYPE_NAME")
    private String auditFailedTypeName;

    @JSONField(name= "IS_HOLD_NAME")
    private String isHoldName;

    @JSONField(name= "IS_O2O_NAME")
    private String isO2oName;

    @JSONField(name= "IS_SAME_CITY_PURCHASE_NAME")
    private String isSameCityPurchaseName;


    @JSONField(name= "CP_C_WAREHOUSE_EXCHANGE_ID")
    private Long cpCWarehouseExchangeId;

    @JSONField(name= "CP_C_WAREHOUSE_EXCHANGE_NAME")
    private String cpCWarehouseExchangeName;

    @JSONField(name= "CP_C_WAREHOUSE_EXCHANGE_CODE")
    private String cpCWarehouseExchangeCode;

    /**
     * 是否寻源失败
     */
    @JSONField(name = "IS_OCCUPY_STOCK_FAIL_NAME")
    private String isOccupyStockFailName;

    /**
     * hold原因
     */
    @JSONField(name = "HOLD_REASON_NAME")
    private String holdReasonName;

    /**
     * 卡单原因
     */
    @JSONField(name = "DETENTION_REASON_NAME")
    private String detentionReasonName;

    /**
     * 销售商品属性
     */
    @JSONField(name = "SALE_PRODUCT_ATTR_NAME")
    private String saleProductAttrName;

    @JSONField(name = "MAIN_ESTIMATE_CON_TIME")
    private String mainEstimateConTime;

    public QueryOrderResult() {

    }

}
