package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryOrderSelectResult implements Serializable {

    private String displayName;
    private String queryName;
    private String value;
    private String type;

    private int sort;
    private List<QueryOrderCheckBoxResult> list = new ArrayList<>();
    private JSONObject selectTab;

}
