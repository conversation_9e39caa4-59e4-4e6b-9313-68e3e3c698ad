package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 退货入库主子表的vo类
 *
 * @author: 夏继超
 * @since: 2019/4/1
 * create at : 2019/4/1 19:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryRefundInResult implements Serializable {
    OcBRefundIn ocBRefundIn;
    List<OcBRefundInProductItem> inProductItem;
    OcBOrder ocBOrder;//用来查询店铺信息
}
