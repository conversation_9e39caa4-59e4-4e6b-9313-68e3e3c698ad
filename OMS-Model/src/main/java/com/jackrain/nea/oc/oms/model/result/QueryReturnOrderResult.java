package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.Data;

/**
 * 退换货订单
 *
 * @author: xiWen.z
 * create at: 2019/7/8 0008
 */
@Data
public class QueryReturnOrderResult extends OcBReturnOrder {

    /**
     * 发货实体仓库.名称CP_C_PHY_WAREHOUSE_ID_NAME
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID_NAME")
    private String cpCPhyWarehouseIdName;

    /**
     * 入库实体仓库.名称
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_IN_ID_NAME")
    private String cpCPhyWarehouseInIdName;

    @J<PERSON><PERSON><PERSON>(name = "TO_DRP_STATUS_NAME")
    private String toDrpStatusName;

    /**
     * 传奶卡系统
     */
    @JSONField(name = "TO_NAIKA_STATUS_NAME")
    private String toNaikaStatusName;
}
