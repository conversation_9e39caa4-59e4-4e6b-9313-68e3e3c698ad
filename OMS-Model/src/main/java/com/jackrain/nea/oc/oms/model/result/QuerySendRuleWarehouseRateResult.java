package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.st.model.StCSendRuleWarehouseRate;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: heliu
 * @since: 2019/4/23
 * create at : 2019/4/23 11:19
 */
@Data
public class QuerySendRuleWarehouseRateResult extends StCSendRuleWarehouseRate implements Serializable {

    /**
     * 实际计算比例
     */
    private BigDecimal actualProportion;
}