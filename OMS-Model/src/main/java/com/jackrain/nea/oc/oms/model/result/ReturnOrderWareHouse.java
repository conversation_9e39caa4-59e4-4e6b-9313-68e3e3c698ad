package com.jackrain.nea.oc.oms.model.result;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: xiWen.z
 * create at: 2019/11/20 0020
 */
public class ReturnOrderWareHouse implements Serializable {


    private static final long serialVersionUID = -1831267484978673023L;


    private Map<Long, String> map;


    public void setMap(Map<Long, String> map) {
        this.map = map;
    }

    public Map<Long, String> getMap() {
        if (this.map == null) {
            this.map = new HashMap<>();
            this.map.put(null, "");
        }
        return this.map;
    }
}
