package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: heliu
 * @since: 2019/5/23
 * create at : 2019/5/23 10:03
 */
@Data
public class WareHouseResult implements Serializable {

    /**
     * skuCode
     */
    private String skuCode;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * wareHosueId
     */
    private Long wareHosueId;

    public WareHouseResult(String skuCode, Long wareHosueId) {
        this.skuCode = skuCode;
        this.wareHosueId = wareHosueId;
    }

    public WareHouseResult(Long skuId, Long wareHosueId) {
        this.skuId = skuId;
        this.wareHosueId = wareHosueId;
    }

    public WareHouseResult() {
    }
}