package com.jackrain.nea.oc.oms.model.result.kdzs;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 快递助手回调返回值
 *
 *  成功 code=100，msg=“”
 *  失败 code=200，msg=“接收失败”
 *
 * <AUTHOR>
 * @date 2022年04月01日 20:27
 */
@Data
@ToString
public class KdzsCallBackResponse implements Serializable {
    /**
     *  状态码
     */
    Integer code;
    /**
     * 消息
     */
    String msg;

    KdzsCallBackResponse(){

    }

    KdzsCallBackResponse(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public static KdzsCallBackResponse builder(Integer code, String msg){
       return new KdzsCallBackResponse(code,msg);
    }

}
