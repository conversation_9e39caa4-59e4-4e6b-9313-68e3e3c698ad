package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AcFInvoiceReturnRedOffsetItem
 * @Description
 * @Date 2022/8/31 下午7:23
 * @Created by wuhang
 */
@TableName(value = "ac_f_invoice_return_red_offset_item")
@Data
public class AcFInvoiceReturnRedOffsetItem extends BaseModel {
    private static final long serialVersionUID = -1L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**  */
    @JSONField(name = "AC_F_INVOICE_RETURN_RED_OFFSET_ID")
    private Long acFInvoiceReturnRedOffsetId;

    /** 商品编码 */
    @JSONField(name = "PRODUCT_CODE")
    private String productCode;

    /** 商品名称 */
    @JSONField(name = "PRODUCT_NAME")
    private String productName;

    /** sku编码 */
    @JSONField(name = "SKU_CODE")
    private String skuCode;

    /** sku名称 */
    @JSONField(name = "SKU_NAME")
    private String skuName;

    /** 单位 */
    @JSONField(name = "UNIT")
    private String unit;

    /** 退货数量 */
    @JSONField(name = "RETURN_COUNT")
    private Integer returnCount;

    /** 退款金额 */
    @JSONField(name = "REFUND_AMOUNT")
    private BigDecimal refundAmount;

    /** 赠品标识,0否1是 */
    @JSONField(name = "GIFT_FLAG")
    private String giftFlag;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}
