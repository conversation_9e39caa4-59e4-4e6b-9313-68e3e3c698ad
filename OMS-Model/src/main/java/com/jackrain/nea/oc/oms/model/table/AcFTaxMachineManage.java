package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName AcFTaxMachineManage
 * @Description
 * @Date 2022/9/13 上午10:09
 * @Created by wuhang
 */
@TableName(value = "ac_f_tax_machine_manage")
@Data
public class AcFTaxMachineManage extends BaseModel {

    private static final long serialVersionUID = 2769768353892891414L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 关联用户档案id */
    @JSONField(name = "USER_ID")
    private String userId;

    /** 税盘号 */
    @JSONField(name = "TAX_MACHINE")
    private String taxMachine;

    /** 备注 */
    @J<PERSON><PERSON><PERSON>(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIF<PERSON>RENAME")
    private String modifierename;
}
