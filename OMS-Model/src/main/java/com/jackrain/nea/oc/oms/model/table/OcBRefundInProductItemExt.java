package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-03-26
 * create at:  2019-03-26 13:11
 * 扩展了一个扫描数量
 */
@Data
public class OcBRefundInProductItemExt extends OcBRefundInProductItem {

    @JSONField(name = "QTY_SCAN")
    private BigDecimal qtyScan;

    @JSONField(name = "SKU_SPEC")
    private String skuSpec;

}
