package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: 夏继超
 * @since: 2019/6/8
 * create at : 2019/6/8 17:14
 */
@Data
public class OcBRefundInProductItemExtend extends OcBRefundInProductItem implements Serializable {
    //是否无原单条码
    @JSONField(name = "ISWITHOUTORIGENAME")
    private String isWithoutOrigEname;
    //是否是匹配
    @JSONField(name = "ISMATCHNAME")
    private String isMatchName;
    // 是否生成调整单
    @JSONField(name = "ISGENADJUSTENAME")
    private String isGenAdjustEname;
    // 商品标记
    @JSONField(name = "PRODUCTMARKNAME")
    private String productMarkName;
}
