package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "oc_b_sap_sales_data_record_item")
@Data
public class OcBSapSalesDataRecordItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "OC_B_SAP_SALES_DATA_RECORD_ID")
    private Long ocBSapSalesDataRecordId;

    @JSONField(name = "SKU")
    private String sku;

    @JSONField(name = "LINE_TYPE")
    private String lineType;

    @JSONField(name = "LINE_CATEGORY")
    private String lineCategory;

    @JSONField(name = "QTY")
    private Integer qty;

    @JSONField(name = "UNIT")
    private String unit;

    @JSONField(name = "CP_C_STORE_ID")
    private Integer cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "FACTORY_CODE")
    private String factoryCode;

    @JSONField(name = "AMT")
    private BigDecimal amt;

    @JSONField(name = "PRO_TYPE")
    private String proType;

    @JSONField(name = "BATCH")
    private String batch;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "CYCLE_QTY")
    private Long cycleQty;

    @JSONField(name = "RESIDUE_QTY")
    private Long residueQty;

    /**
     * 成本
     */
    @JSONField(name = "PRICE_COST")
    private BigDecimal priceCost;
}