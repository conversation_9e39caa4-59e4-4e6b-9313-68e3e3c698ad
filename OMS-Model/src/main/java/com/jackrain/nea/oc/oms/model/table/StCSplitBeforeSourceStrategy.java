package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @program: r3-oc-oms
 * @description: 寻源前拆单策略
 * @author: caomalai
 * @create: 2022-07-14 14:39
 **/
@TableName(value = "st_c_split_before_source_strategy")
@Data
public class StCSplitBeforeSourceStrategy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 策略名称
     */
    @JSONField(name = "ENAME")
    private String ename;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

}
