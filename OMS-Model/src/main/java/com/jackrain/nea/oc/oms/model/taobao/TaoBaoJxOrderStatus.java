package com.jackrain.nea.oc.oms.model.taobao;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单状态
 **/
public class TaoBaoJxOrderStatus {

    private TaoBaoJxOrderStatus() {

    }

    /**
     * 付款成功，待供应商发货
     */
    public static final String WAIT_FOR_SUPPLIER_DELIVER = "WAIT_FOR_SUPPLIER_DELIVER";
    /**
     * 20191815 新增 交易完成
     */
    public static final String TRADE_FINISHED = "TRADE_FINISHED";
    /**
     * 20191815 新增 等待分销商收货
     */
    public static final String WAIT_FOR_APPLIER_STORAGE = "WAIT_FOR_APPLIER_STORAGE";
    /**
     * 20191121 新增 交易关闭
     */
    public static final String TRADE_CLOSED = "TRADE_CLOSED";


}
