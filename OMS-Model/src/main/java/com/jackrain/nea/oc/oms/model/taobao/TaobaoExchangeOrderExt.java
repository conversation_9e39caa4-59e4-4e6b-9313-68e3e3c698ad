package com.jackrain.nea.oc.oms.model.taobao;

import lombok.Getter;

/**
 * 换货相关枚举类
 */
public class TaobaoExchangeOrderExt {

    /**
     * 换货中间表状态 (注意:平台下下来就是中文)
     * <p>
     * 换货待处理(1), 待买家退货(2), 买家已退货，待收货(3),
     * 换货关闭(4), 换货成功(5), 待买家修改(6),
     * 待发出换货商品(12), 待买家收货(13), 请退款(14)
     */
    @Getter
    public enum ExchangeOrderCentreStatus {

        EXCHANGE_STAY_HANDLE(1, "换货待处理"), //换货待处理(1)

        STAY_BUYER_RETURN(2, "待买家退货"), //待买家退货(2)

        BUYER_RETURN_COLLECT_GOODS(3, "买家已退货，待收货"), //买家已退货，待收货(3)

        EXCHANGE_CLOSE(4, "换货关闭"), //换货关闭(4)

        EXCHANGE_SUCCESS(5, "换货成功"), //换货成功(5)

        WAIT_BUYER_MODIFY(6, "待买家修改"), //待买家修改(6)

        WAIT_ISSUE_EXCHANGE_GOODS(12, "待发出换货商品"), //待发出换货商品(12)

        WAIT_BUYER_COLLECT_GOODS(13, "待买家收货"), //待买家收货(13)

        REFUND_PLEASE(14, "请退款"); //请退款(14)

        ExchangeOrderCentreStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        private Integer code;

        private String name;
    }

    /**
     * { "id": 1, "text": "待审核" },
     * { "id": 2, "text": "缺货" },
     * { "id": 3, "text": "已审核" },
     * { "id": 4, "text": "配货中" },
     * { "id": 5, "text": "仓库发货" },
     * { "id": 6, "text": "平台发货" },
     * { "id": 7, "text": "已取消", "css": { "style": "background-color:#A9A9A9" } },
     * { "id": 8, "text": "系统作废", "css": { "style": "background-color:#A9A9A9" } },
     * { "id": 9, "text": "预售" },
     * { "id": 10, "text": "代发" },
     * { "id": 11, "text": "物流已送达" },
     * { "id": 12, "text": "交易完成" },
     * { "id": 13, "text": "未付款" },
     * { "id": 21, "text": "待传wms" },
     * { "id": 50, "text": "待分配" },
     * { "id": 51, "text": "预售待发货" },
     * { "id": 52, "text": "预售缺货" }];
     */


}
