#\u56FD\u9645\u5316\u4FE1\u606F
HTTPCODE_200=\u8BF7\u6C42\u6210\u529F
HTTPCODE_303=\u767B\u5F55\u5931\u8D25
HTTPCODE_400=\u8BF7\u6C42\u53C2\u6570\u51FA\u9519
HTTPCODE_401=\u6CA1\u6709\u767B\u5F55
HTTPCODE_403=\u6CA1\u6709\u6743\u9650
HTTPCODE_404=\u627E\u4E0D\u5230\u9875\u9762
HTTPCODE_408=\u8BF7\u6C42\u8D85\u65F6
HTTPCODE_409=\u53D1\u751F\u51B2\u7A81
HTTPCODE_410=\u5DF2\u88AB\u5220\u9664
HTTPCODE_423=\u5DF2\u88AB\u9501\u5B9A
HTTPCODE_500=\u670D\u52A1\u5668\u51FA\u9519
EMAIL.SET_HOST=\u8BBE\u7F6E\u7CFB\u7EDF\u5C5E\u6027\uFF1Amail.smtp.host={}
EMAIL.PRE_TALK=\u51C6\u5907\u83B7\u53D6\u90AE\u4EF6\u4F1A\u8BDD\u5BF9\u8C61\uFF01
EMAIL.ERROR_TALK=\u83B7\u53D6\u90AE\u4EF6\u4F1A\u8BDD\u5BF9\u8C61\u65F6\u53D1\u751F\u9519\u8BEF\uFF01
EMAIL.PRE_MIME=\u51C6\u5907\u521B\u5EFAMIME\u90AE\u4EF6\u5BF9\u8C61\uFF01
EMAIL.ERROR_MIME=\u521B\u5EFAMIME\u90AE\u4EF6\u5BF9\u8C61\u5931\u8D25\uFF01
EMAIL.SET_AUTH=\u8BBE\u7F6Esmtp\u8EAB\u4EFD\u8BA4\u8BC1\uFF1Amail.smtp.auth={}
EMAIL.SET_SUBJECT=\u8BBE\u7F6E\u90AE\u4EF6\u4E3B\u9898[{}]\uFF01
EMAIL.ERROR_SUBJECT=\u8BBE\u7F6E\u90AE\u4EF6\u4E3B\u9898\u53D1\u751F\u9519\u8BEF\uFF01
EMAIL.ERROR_BODY=\u8BBE\u7F6E\u90AE\u4EF6\u6B63\u6587\u65F6\u53D1\u751F\u9519\u8BEF\uFF01
EMAIL.ADD_ATTEND=\u589E\u52A0\u90AE\u4EF6\u9644\u4EF6[{}]\uFF01
EMAIL.SET_TO=\u8BBE\u7F6E\u6536\u4FE1\u4EBA[{}]\uFF01
EMAIL.SET_COPYTO=\u8BBE\u7F6E\u6284\u9001\u4EBA[{}]\uFF01
EMAIL.SENDING=\u6B63\u5728\u53D1\u9001\u90AE\u4EF6....
EMAIL.SEND_SUCC=\u53D1\u9001\u90AE\u4EF6\u6210\u529F\uFF01
EMAIL.SEND_ERR=\u90AE\u4EF6\u53D1\u9001\u5931\u8D25\uFF01
THIRDPARTY.LOGIN.NOTOKEN=\u672A\u83B7\u53D6\u5230%s\u767B\u5F55\u4EE4\u724C\uFF01
TASKID_IS_NULL=\u5B9A\u65F6\u4EFB\u52A1Id\u4E0D\u80FD\u4E3A\u7A7A.
LOGIN_FAIL=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF.
ACCOUNT_IS_NULL=\u5E10\u53F7\u4E0D\u80FD\u4E3A\u7A7A.
PASSWORD_IS_NULL=\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A.
USER_ID_IS_NULL=\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A.
USER_IS_NULL=\u7528\u6237Id[%1$s]\u9519\u8BEF.
ID_IS_NULL=Id\u4E0D\u80FD\u4E3A\u7A7A.
TASKGROUP_IS_NULL=\u4EFB\u52A1\u7EC4\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A.
TASKNAME_IS_NULL=\u4EFB\u52A1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A.
-have-special-filter=\u6EE1\u8DB3\u7279\u5B9A\u8FC7\u6EE4\u5668
-satisfy-=\u6EE1\u8DB3
combobox-select=--\u8BF7\u9009\u62E9--
number-format1=\u683C\u5F0F\uFF1A(\u64CD\u4F5C\u7B26\u5982 > = < >= )\u4EFB\u610F\u6570\u5B57
format-length=\u957F\u5EA6
date-format1=\u65E5\u671F\uFF1A(\u64CD\u4F5C\u7B26\u5982 > = < >= )yyyy/mm/dd, \u6216 yyyy/mm/dd - yyyy/mm/dd
string-format1=\u5B57\u7B26\u4E32\uFF0C\u9996\u5B57\u6BCD\u4E3A\u82F1\u6587\u7684 = \u53F7\u65F6\u5C06\u8FDB\u884C\u7CBE\u786E\u5339\u914D\uFF0C\u5426\u5219\u91C7\u7528\u6A21\u7CCA\u5339\u914D\u7684\u65B9\u5F0F
number-format2=\u6570\u5B57
date-format2=\u65E5\u671F\uFF1Ayyyy/mm/dd \u6216 yyyy/mm/dd HH24:MI:SS
date-format3=\u65E5\u671F\uFF1Ayyyy/mm/dd
string-format2=\u5B57\u7B26\u4E32
correspond-=\u5BF9\u5E94\u7684
-exists-=\u5B58\u5728
sql-and=\u5E76\u4E14
sql-and-not=\u5E76\u4E14\u4E0D\u5305\u542B
sql-or=\u6216\u8005
sql-or-not=\u6216\u8005\u4E0D\u5305\u542B
user-not-found=\u7528\u6237\u672A\u627E\u5230
no-permission=\u65E0\u6743\u9650
secret-length-too-short=\u5BC6\u94A5\u957F\u5EA6\u4E0D\u80FD\u5C0F\u4E8E6\u4F4D
two-secret-not-identical=\u4E24\u6B21\u5BC6\u94A5\u4E0D\u4E00\u81F4
secret-should-be-different-to-password=\u8BF7\u4F7F\u7528\u548C\u7528\u6237\u5BC6\u7801\u4E0D\u76F8\u540C\u7684\u5BC6\u94A5
db-error=\u6570\u636E\u5E93\u5F02\u5E38
finish-and-counter-set-to=\u5B8C\u6210AgileOTP\u5BC6\u94A5\u4FEE\u6539,\u8BF7\u8BBE\u7F6EAgileOTP\u9009\u9879\u4E2D\u7684\u8BA1\u6570\u5668\u4E3A
user-not-allow-to-use-otp=\u5F53\u524D\u7528\u6237\u6CA1\u6709\u5F00\u542F\u4E00\u6B21\u6027\u53E3\u4EE4\u8BA4\u8BC1
exception=\u5F02\u5E38
invalid-login-ip=\u975E\u6388\u6743\u767B\u5F55IP\u5730\u5740
inactive-user-exception=\u5F53\u524D\u7528\u6237\u88AB\u51BB\u7ED3\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
max-session-query-count-reached=\u5F53\u524D\u4F1A\u8BDD\u6253\u5F00\u7684\u67E5\u8BE2\u6570\u91CF\u8FC7\u591A\uFF0C\u8BF7\u5148\u5173\u95ED\u4E00\u4E9B\uFF01
olap-role-not-found=OLAP\u89D2\u8272\u672A\u627E\u5230\uFF01
system-timeout-please-try-later=\u7CFB\u7EDF\u54CD\u5E94\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
query-not-set=\u672A\u9009\u62E9\u67E5\u8BE2
queue-job-swtiched=\u961F\u5217\u5BF9\u5E94\u7684\u5904\u7406\u7EBF\u7A0B\u7684\u72B6\u6001\u88AB\u5207\u6362
cron-expression-error=\u5B9A\u65F6\u8868\u8FBE\u5F0F\u9519\u8BEF
start-queuejob=\u542F\u52A8
stop-queuejob=\u505C\u6B62
not-tree-table=\u5F53\u524D\u8868\u5E76\u975E\u6811\u578B\u7ED3\u6784
pls-check-plan-inout-for-picklist=\u8BF7\u68C0\u67E5\u8BA1\u5212\u8D27\u8FD0\u5355\u4E0E\u68C0\u8D27\u5355\u5728\u540C\u4E00\u4E2A\u4ED3\u5E93\u5185\uFF0C\u5E76\u4E14\u672A\u5B8C\u6210
unknown-order-doctype=\u672A\u77E5\u7684\u8BA2\u5355\u7C7B\u578B
fail-to-create-task=\u521B\u5EFA\u4EFB\u52A1\u5931\u8D25
select-at-least-one=\u81F3\u5C11\u9009\u62E9\u4E00\u6761\u8BB0\u5F55
invalid-product-attribute-at-line=\u8BBE\u7F6E\u7684\u7269\u6599\u5C5E\u6027\u5B9E\u4F8B\u9519\u8BEF\uFF0C\u884C
product-attribute-should-be-null-at-line=\u4E0D\u5E94\u8BE5\u8BBE\u7F6E\u5C5E\u6027\u5B9E\u4F8B\uFF0C\u884C
finished=\u5B8C\u6210
updated-count=\u66F4\u65B0\u884C\u6570:
clear-added-storage=\u89E3\u9664\u9884\u589E\u5E93\u5B58
clear-preserved-storage=\u89E3\u9664\u9884\u51CF\u5E93\u5B58
pre-add-storage=\u9884\u589E\u5E93\u5B58
pre-reserve-storage=\u9884\u51CF\u5E93\u5B58
wait-for-approve=\u7B49\u5F85\u5BA1\u6279
rejected=\u5BA1\u6838\u672A\u901A\u8FC7
accepted=\u5BA1\u6838\u901A\u8FC7
no-record-changed=\u6CA1\u6709\u8BB0\u5F55\u88AB\u66F4\u65B0
audit-process-name=\u5BA1\u6838\u6D41\u7A0B\u540D\u79F0
audit-phase-name=\u5BA1\u6838\u9636\u6BB5\u540D\u79F0
audit-info=\u5BA1\u6838\u4FE1\u606F
submit-info=\u63D0\u4EA4\u4FE1\u606F
complete=\u5B8C\u6210
please-set-assignee=\u8BF7\u8BBE\u7F6E\u4EE3\u529E\u4EBA
can-not-assign-to-self=\u4E0D\u80FD\u8BBE\u7F6E\u4EE3\u529E\u4EBA\u4E3A\u81EA\u5DF1
check-system-log=\u547D\u4EE4\u6267\u884C\u51FA\u73B0\u5F02\u5E38\uFF0C\u8BF7\u67E5\u8BE2\u7CFB\u7EDF\u65E5\u5FD7\u83B7\u53D6\u8BE6\u7EC6\u4FE1\u606F
not-exists-or-invalid=\u4E0D\u5B58\u5728\u6216\u4E0D\u53EF\u7528\uFF0C\u6216\u4E0D\u7B26\u5408\u5F53\u524D\u4F4D\u7F6E\u8981\u6C42
line=\u884C
total-records-created-is=\u521B\u5EFA\u7684\u8BB0\u5F55\u6570
finished-import=\u5B8C\u6210\u5BFC\u5165
consumed-to=\u5927\u7EA6\u8017\u65F6
seconds=\u79D2
operate-table=\u8868
total-lines=\u603B\u8BF7\u6C42\u884C\u6570
success-import=\u6210\u529F\u5BFC\u5165
fail-import=\u5931\u8D25
object-already-submitted-no-delete=\u5DF2\u63D0\u4EA4\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
total-records-deleted=\u5220\u9664\u7684\u8BB0\u5F55\u6570
total-records-updated=\u4FEE\u6539\u7684\u8BB0\u5F55\u6570
object-already-submitted=\u5BF9\u8C61\u5DF2\u7ECF\u88AB\u63D0\u4EA4\u8FC7\u4E86
object-status-error-may-submitted-or-deleted=\u5BF9\u8C61\u4E0D\u5728\u5F53\u524D\u8868\u6240\u8BBE\u5B9A\u7684\u72B6\u6001(\u5DF2\u63D0\u4EA4\u5230\u5176\u4ED6\u72B6\u6001, \u6216\u5220\u9664)
parent-record-not-found=\u7236\u8868\u8BB0\u5F55\u672A\u627E\u5230\u6216\u72B6\u6001\u5DF2\u6539\u53D8\uFF0C\u8BF7\u786E\u8BA4
must-be-admin-to-execute-this-command=\u5FC5\u987B\u5177\u6709\u7BA1\u7406\u5458\u8EAB\u4EFD\u65B9\u53EF\u6267\u884C\u6B64\u547D\u4EE4
no-write-permission=\u6CA1\u6709\u5199\u6743\u9650
no-read-perission=\u6CA1\u6709\u8BFB\u6743\u9650
directory-not-found=\u672A\u6307\u660E\u64CD\u4F5C\u6240\u5728\u76EE\u5F55, \u6216\u76EE\u5F55\u672A\u627E\u5230
operator-not-found=\u672A\u6307\u660E\u64CD\u4F5C\u5458
object-not-found=\u672A\u627E\u5230\u5BF9\u8C61
this-command-will-run-in-background-with-log-file=\u5F53\u524D\u64CD\u4F5C\u5C06\u88AB\u7CFB\u7EDF\u653E\u5728\u540E\u53F0\u8FD0\u884C\uFF0C\u64CD\u4F5C\u7ED3\u675F\u540E\u5C06\u4EA7\u751F\u62A5\u544A\u653E\u5728\u4F60\u7684\u4E2A\u4EBA\u6587\u4EF6\u5939\u4E2D\uFF0C\u6587\u4EF6\u540D\u4E3A
can-not-check-records=\u65E0\u6CD5\u6267\u884C\u8BB0\u5F55\u7684\u5B58\u5728\u6027\u68C0\u67E5
security-filter-exception=\u5B89\u5168\u8BA4\u8BC1\u8FC7\u6EE4\u5668\u51FA\u73B0\u5F02\u5E38
can-not-find-client-domain-for-user=\u65E0\u6CD5\u6839\u636E\u7528\u6237\u4FE1\u606F\u83B7\u53D6\u516C\u53F8\u57DF\u540D
not-specify-client=\u5F53\u524D\u67E5\u8BE2\u672A\u6307\u660E\u6240\u5C5E\u516C\u53F8\u4FE1\u606F
no-connection=\u6CA1\u6709\u6570\u636E\u5E93\u94FE\u63A5
error-at-line=\u51FA\u73B0\u5F02\u5E38\u5728\u884C
group-permission-update=\u7EC4\u6743\u9650\u4FEE\u6539\u6210\u529F
sql-between=\u4ECB\u4E8E
sql-to=\u81F3
sql-contains=\u542B\u6709
date-format-error=\u65E5\u671F\u683C\u5F0F\u9519\u8BEF
no-delete-permission-on-all-pls-delete-one-by-one=\u5F53\u524D\u7528\u6237\u6CA1\u6709\u5168\u90E8\u6240\u9009\u7684\u5BF9\u8C61\u4E0A\u5220\u9664\u6743\u9650\uFF0C\u8BF7\u9010\u4E2A\u5220\u9664
request-to-delete=\u88AB\u8BF7\u6C42\u5220\u9664
failed-count=\u5931\u8D25\u7684\u884C\u6570
detail-msg=\u8BE6\u7EC6\u4FE1\u606F
can-not-delete-since-submitted=\u5DF2\u63D0\u4EA4\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
pls-select-items=\u8BF7\u9009\u4E2D\u8981\u4FEE\u6539\u7684\u6761\u76EE\u9879
updated-lines=\u5B8C\u6210\u4FEE\u6539\u7684\u884C\u6570
error-in-permission-check=\u5728\u5224\u65AD\u6743\u9650\u65F6\u51FA\u73B0\u5F02\u5E38
no-submit-permission-on-all-pls-submit-one-by-one=\u6240\u9009\u8BB0\u5F55\u4E2D\u5B58\u5728\u60A8\u65E0\u6743\u9650\u6216\u5DF2\u63D0\u4EA4\u7684\u884C\uFF0C\u8BF7\u5237\u65B0\u9875\u9762\u540E\u9010\u4E2A\u63D0\u4EA4(\u5F53\u524D\u64CD\u4F5C\u88AB\u5168\u90E8\u53D6\u6D88)
some-objects-changed=\u90E8\u5206\u5BF9\u8C61\u4E0D\u5728\u5F53\u524D\u8868\u6240\u8BBE\u5B9A\u7684\u72B6\u6001(\u5DF2\u63D0\u4EA4\u5230\u5176\u4ED6\u72B6\u6001, \u6216\u5220\u9664)\uFF0C\u8BF7\u68C0\u67E5\uFF01
some-objects-status-changed=\u5355\u636E\u96C6\u5408\u72B6\u6001\u4E0D\u552F\u4E00\uFF0C\u8BF7\u5237\u65B0\uFF01
pls-record-otp-info-with-secret=\u6570\u636E\u5E93\u5DF2\u66F4\u65B0\u3002\u8BF7\u8BB0\u5F55\u4E0B\u5217\u4FE1\u606F\u5230\u7528\u6237\u7684AgileOTP:\u5BC6\u94A5
otp-counter=\u8BA1\u6570\u5668
deprecated=\u8BF7\u6C42\u7684\u65B9\u6CD5\u6216\u8FC7\u7A0B\u5DF2\u4F5C\u5E9F
invalid-doc-number=\u65E0\u6548\u7684\u5355\u636E\u53F7
invalid-session=\u65E0\u6548\u7684\u4F1A\u8BDD\u8FDE\u63A5
insufficient-permission=\u6743\u9650\u4E0D\u8DB3
update-doc-failed=\u4FEE\u6539\u5355\u636E\u72B6\u6001\u5931\u8D25
doc-state-updated=\u5355\u636E\u72B6\u6001\u66F4\u65B0\u5B8C\u6210
rowindex=\u884C\u53F7
state=\u72B6\u6001
errmsg=\u9519\u8BEF\u4FE1\u606F
jsonobj=\u8F85\u52A9
product-not-found=\u7269\u6599\u672A\u627E\u5230!
datenumber-format1=\u65E5\u671F\uFF1A(\u64CD\u4F5C\u7B26\u5982 > = < >= )yyyymmdd, \u6216 yyyymmdd ~ yyyymmdd
datenumber-format2=8\u4F4D\u65E5\u671F\uFF0C\u598220070823
cxtab-not-found=\u5206\u7C7B\u6C47\u603B\u8868\u672A\u627E\u5230\u6216\u4E0D\u53EF\u7528
cxtabreport=\u4EA4\u53C9\u7EDF\u8BA1\u8868
creator=\u521B\u5EFA\u4EBA
creationdate=\u521B\u5EFA\u65E5\u671F
filter-description=\u6761\u4EF6\u8BBE\u7F6E
task-generated=\u4EFB\u52A1\u521B\u5EFA\u6210\u529F
process-instance-failed=\u4EFB\u52A1\u6267\u884C\u5931\u8D25
axis-h=\u6A2A\u8F74\u5B9A\u4E49
axis-v=\u7EB5\u8F74\u5B9A\u4E49
fact-desc=\u5EA6\u91CF\u5B9A\u4E49
no-free-space-for-user=\u7528\u6237\u7684\u5BFC\u51FA\u76EE\u5F55\u53EF\u7528\u7A7A\u95F4\u4E0D\u8DB3
axis-h-line=\u6A2A\u8F74\u7684\u884C
axis-v-line=\u7EB5\u8F74\u7684\u884C
fact-desc-line=\u5EA6\u91CF\u7684\u884C
is-invalid=\u9519\u8BEF
product-not-valid=\u7269\u6599\u5728\u5F53\u524D\u5355\u636E\u4E2D\u65E0\u6548
switch-close=\u5173\u95ED
switch-open=\u91CD\u5F00
object-saved-but-status-changed=\u4FDD\u5B58\u6210\u529F\uFF0C\u7531\u4E8E\u5BF9\u8C61\u72B6\u6001\u53D8\u5316\uFF0C\u8BF7\u5173\u95ED\u5F53\u524D\u7A97\u53E3
total-rows-found=\u603B\u884C\u6570
rows-permitted=\u6388\u6743\u64CD\u4F5C\u884C\u6570
update-success=\u64CD\u4F5C\u6210\u529F\u884C\u6570
update-fail=\u64CD\u4F5C\u5931\u8D25\u884C\u6570
table-not-support-batchupdate=\u5F53\u524D\u8868\u4E0D\u652F\u6301\u6279\u91CF\u4FEE\u6539\u64CD\u4F5C\uFF0C\u8BF7\u9010\u6761\u4FEE\u6539\u4FDD\u5B58
no-submit-as-update-failed=\u4FDD\u5B58\u51FA\u73B0\u5F02\u5E38\uFF0C\u672A\u6267\u884C\u4EFB\u4F55\u884C\u7684\u63D0\u4EA4\u64CD\u4F5C\u3002\u8BF7\u4FEE\u6B63\u9519\u8BEF\u540E\u518D\u5C1D\u8BD5\u63D0\u4EA4\uFF01
specify-filter-for-big-table=\u5F53\u524D\u4E3A\u6D77\u91CF\u6570\u636E\u8868\uFF0C\u8BF7\u5148\u8BBE\u7F6E\u67E5\u8BE2\u6761\u4EF6
parameter-error=\u53C2\u6570\u9519\u8BEF
cxtab-task-generated=\u62A5\u8868\u5728\u540E\u53F0\u6392\u961F\u7B49\u5019\u8FD0\u884C\uFF0C\u60A8\u53EF\u4EE5\u7EE7\u7EED\u5176\u4ED6\u5DE5\u4F5C\uFF0C\u53EA\u8981\u4FDD\u6301\u5373\u65F6\u901A\u8BAF\u5668\u4E0A\u7EBF\u72B6\u6001\uFF0C\u62A5\u8868\u5B8C\u6210\u540E\u5C06\u7ACB\u523B\u901A\u77E5\u60A8\u3002
report-creation-failed-for-cxtab=\u62A5\u8868\u67E5\u8BE2\u5931\u8D25
error-msg=\u62A5\u9519\u4FE1\u606F
report-created=\u62A5\u8868\u67E5\u8BE2\u5B8C\u6BD5,\u7528\u65F6(\u79D2):
file-name=\u62A5\u8868\u6587\u4EF6\u540D
contains-error=\u5B58\u5728\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5
unique-constraint-violated=\u8BF7\u68C0\u67E5\u8F93\u5165\u9879\uFF0C\u6709\u6570\u636E\u91CD\u590D
columns-that-violate-unique-constraint=\u8F93\u5165\u7684\u6570\u636E\u5DF2\u5B58\u5728
in-table=\u6240\u5728\u4E1A\u52A1
unqiue-index-name=\u552F\u4E00\u7D22\u5F15
click-attach-url-to-download=\u70B9\u51FB\u9644\u4EF6\u94FE\u63A5\u4E0B\u8F7D\u62A5\u8868
in-one-week=\u5728\u4E00\u5468\u5185
not-submit=\u72B6\u6001\u5904\u4E8E\u672A\u63D0\u4EA4
error-verify-code=\u9A8C\u8BC1\u7801\u8F93\u5165\u9519\u8BEF
unknown-exception=\u7CFB\u7EDF\u5185\u90E8\u9519\u8BEF\uFF0C\u8BF7\u5173\u95ED\u7A97\u53E3\u91CD\u8BD5
template-file-not-found=\u6A21\u677F\u6587\u4EF6\u672A\u627E\u5230
template-file-error=\u6A21\u677F\u6587\u4EF6\u9519\u8BEF
website-created=\u7F51\u7AD9\u521B\u5EFA\u5B8C\u6210
website-creation-failed=\u7F51\u7AD9\u521B\u5EFA\u5931\u8D25
not-authorized-for-upload=\u5F53\u524D\u7528\u6237\u7981\u6B62\u4E0A\u4F20\u6587\u4EF6
not-authorized-for-browsing=\u5F53\u524D\u7528\u6237\u7981\u6B62\u6D4F\u89C8\u6587\u4EF6
invalid-command=\u65E0\u6548\u547D\u4EE4
invalid-resouce-type=\u4E0D\u652F\u6301\u6B64\u7C7B\u578B\u6587\u4EF6
invalid-current-folder=\u6307\u5B9A\u76EE\u5F55\u4E0D\u5B58\u5728
no-data=\u6CA1\u6709\u7B26\u5408\u6761\u4EF6\u7684\u6570\u636E
are-you-sure-to-execute=\u4F60\u786E\u8BA4\u8981\u6267\u884C-{0}-\u52A8\u4F5C\u5417?
forbid-none-menuobject=\u60A8\u8BD5\u56FE\u67E5\u770B\u7684\u5BF9\u8C61\u88AB\u7981\u6B62\u5355\u72EC\u663E\u793A
jreport-file-not-found=\u62A5\u8868\u6587\u4EF6\u672A\u627E\u5230
jreport-found-error=\u8BFB\u53D6\u62A5\u8868\u65F6\u53D1\u73B0\u5F02\u5E38
unsupported-class=\u4E0D\u652F\u6301\u7684\u62A5\u8868\u53C2\u6570\u7C7B
condition-error=\u62A5\u8868\u6761\u4EF6\u8BBE\u7F6E\u4E0D\u6B63\u786E\uFF0C\u8BF7\u68C0\u67E5
usbkey-error=USBKey\u4E0D\u6B63\u786E\u6216\u672A\u63D2\u5165
deleted=\u5220\u9664\u6210\u529F
file=\u6587\u4EF6
fail-to-delete=\u5220\u9664\u5931\u8D25
not-exists=\u4E0D\u5B58\u5728
reload-contract-as-update-not-succeed=\u64CD\u4F5C\u672A\u6210\u529F\uFF0C\u5408\u540C\u72B6\u6001\u5DF2\u6539\u53D8\u3002\u8BF7\u5230\u5408\u540C\u754C\u9762\u5237\u65B0\u540E\u91CD\u8BD5\uFF01
cube-creation-failed=\u62A5\u8868\u672A\u521B\u5EFA\u6210\u529F\uFF0C\u9700\u8981\u7F51\u7AD9\u7BA1\u7406\u5458\u534F\u52A9\u6392\u67E5\uFF0C\u8BF7\u901A\u77E5\u7BA1\u7406\u5458\u4EE5\u4E0B\u9519\u8BEF\u53F7
recent-reports=\u8FD1\u671F\u62A5\u8868
object-not-submitted=\u5355\u636E\u672A\u63D0\u4EA4\uFF0C\u4E0D\u80FD\u6267\u884C\u53D6\u6D88\u63D0\u4EA4\u52A8\u4F5C
could-not-find-modifier=\u65E0\u6CD5\u83B7\u53D6\u5355\u636E\u6700\u540E\u4FEE\u6539\u4EBA\u4FE1\u606F
no-permission-to-unsubmit=\u65E0\u6CD5\u53D6\u6D88\u63D0\u4EA4\uFF0C\u5FC5\u987B\u662F\u6700\u540E\u4FEE\u6539\u8BE5\u5355\u636E\u7684\u4EBA\u624D\u80FD\u53D6\u6D88\u63D0\u4EA4
ad_query.queryparam=\u53C2\u6570
contain=\u5305\u542B
no-contain=\u4E0D\u5305\u542B
axis-p=\u5206\u9875\u5B9A\u4E49
print-template-not-set=\u6253\u5370\u5931\u8D25\uFF0C\u8BF7\u5148\u8FDB\u884C\u6253\u5370\u8BBE\u7F6E
pls-input=\u8BF7\u8F93\u51650
image-success=\u56FE\u7247\u751F\u6210\u6210\u529F!
image-fail=\u56FE\u7247\u751F\u6210\u5931\u8D25!
ckeck-success=\u68C0\u67E5\u6210\u529F!
ckeck-fail=\u68C0\u67E5\u5931\u8D25!
clear-cache-fail=\u6E05\u9664\u7F13\u5B58\u5931\u8D25!
clear-cache-success=\u6E05\u9664\u7F13\u5B58\u6210\u529F!
clear-additional-links=\u9644\u4EF6\u94FE\u63A5\u5168\u90E8\u6E05\u9664!
additional-links-success=\u9644\u4EF6\u94FE\u63A5\u8BBE\u5B9A\u6210\u529F!
regexp-error=\u6B63\u5219\u8868\u8FBE\u5F0F\u9519\u8BEF!
used-password=\u5BC6\u7801\u5DF2\u7ECF\u88AB\u4F7F\u7528\u8FC7\u4E86,\u8BF7\u66F4\u6362!
password-invalid=\u5BC6\u7801\u65E0\u6548\uFF0C\u8BF7\u66F4\u6362!
two-dif-passwords=\u4E24\u6B21\u5BC6\u7801\u4E0D\u5339\u914D,\u8BF7\u91CD\u65B0\u8F93\u5165!
can-not-modify-user=\u65E0\u6CD5\u4FEE\u6539\u7528\u6237\u5230\u95E8\u6237\u5B89\u5168\u7CFB\u7EDF!
password-modified-success=\u5BC6\u7801\u4FEE\u6539\u6210\u529F!
status-modified-success=\u72B6\u6001\u4FEE\u6539\u6210\u529F!
data-not-set=\u6570\u636E\u672A\u8BBE\u7F6E!
data-not-saved=\u6570\u636E\u751F\u6210\u4F46\u5C1A\u672A\u4FDD\u5B58!
must-set-db=\u5FC5\u987B\u8BBE\u7F6E\u6570\u636E\u6E90!
must-set-table=\u5FC5\u987B\u8BBE\u7F6E\u76EE\u6807\u8868!
db-filter-condition=\u5FC5\u987B\u6307\u660E\u6570\u636E\u6E90\u8FC7\u6EE4\u6761\u4EF6\u6216\u5177\u4F53\u8BB0\u5F55!
ds-not-exist=\u6E90\u6570\u636E\u4E0D\u5B58\u5728\u6216\u672A\u627E\u5230!
data-copy-not-saved=\u6570\u636E\u590D\u5236\u6210\u529F,\u5C1A\u672A\u4FDD\u5B58!
execute-successfully=\u6267\u884C\u6210\u529F!
choose-menu=\u8BF7\u9009\u62E9\u6761\u76EE!
password-info=\u5BC6\u7801\u4FEE\u6539\u6210\u529F, \u8BF7\u81F3<a href='/login.jsp'>\u767B\u5F55\u754C\u9762\u767B\u5F55</a>
operate-fail=\u64CD\u4F5C\u5931\u8D25\uFF0C\u63D0\u4F9B\u7684\u4FE1\u606F\u542B\u6709\u9519\u8BEF!
dotnot-print=\u65E0\u6CD5\u6267\u884C\u6253\u5370:
req-approved=\u8BF7\u6C42\u88AB\u6279\u51C6!
req-commited=\u8BE5\u8BF7\u6C42\u5DF2\u7ECF\u88AB\u63D0\u4EA4\u8FC7\u4E86!
req-audit=\u8BF7\u6C42\u8FDB\u5165\u5BA1\u6838\u72B6\u6001!
req-reject=\u8BF7\u6C42\u88AB\u9A73\u56DE!
row_total=\u884C\u5408\u8BA1
enough_goods=\u6709
lack_goods=\u65E0
current-qlc=\u4F60\u7684\u9ED8\u8BA4\u6A21\u677F\u53D8\u66F4\u4E3A:
report-rows-exeed-limit=\u62A5\u8868\u592A\u5927\uFF0C\u6570\u636E\u884C\u8D85\u8FC7\u9650\u5236\uFF0C\u8BF7\u8865\u5145\u8FC7\u6EE4\u6761\u4EF6
fail-to-lock-table=\u5176\u4ED6\u7528\u6237\u6B63\u5728\u4FEE\u6539\u672C\u8BB0\u5F55\uFF0C\u8BF7\u7A0D\u4FAF\u518D\u8BD5
object-already-submitted-no-void=\u5DF2\u63D0\u4EA4\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u4F5C\u5E9F
total-records-void=\u4F5C\u5E9F\u7684\u8BB0\u5F55\u6570
no-void-permission-on-all-pls-do-one-by-one=\u5F53\u524D\u7528\u6237\u6CA1\u6709\u5168\u90E8\u6240\u9009\u7684\u5BF9\u8C61\u4E0A\u7684\u64CD\u4F5C\u6743\u9650\uFF0C\u8BF7\u9010\u4E2A\u64CD\u4F5Crequest-to-void=\u8BF7\u6C42\u4F5C\u5E9F
can-not-delete-since-valid=\u5FC5\u987B\u5728\u4F5C\u5E9F\u540E\u624D\u5141\u8BB8\u5220\u9664
object-must-be-active=\u5F53\u524D\u5BF9\u8C61\u4E0D\u80FD\u662F\u4F5C\u5E9F\u72B6\u6001
object-must-be-inactive=\u5F53\u524D\u5BF9\u8C61\u5FC5\u987B\u662F\u4F5C\u5E9F\u72B6\u6001
object-not-in-unsubmit-status=\u5F53\u524D\u5BF9\u8C61\u5FC5\u987B\u662F\u672A\u63D0\u4EA4\u72B6\u6001
cmd-argument-error=\u547D\u4EE4\u884C\u53C2\u6570\u4E0D\u6B63\u786E
command-not-find=\u547D\u4EE4\u4E0D\u5B58\u5728
no-command=\u9700\u8BBE\u7F6E\u547D\u4EE4
confirm-delete=\u786E\u8BA4\u5220\u9664\u5417?
record-found=\u7B26\u5408\u6761\u4EF6\u7684\u8BB0\u5F55\u6570
confirm-submit=\u786E\u8BA4\u63D0\u4EA4\u5417\uFF1F
confirm-void=\u786E\u8BA4\u4F5C\u5E9F\u5417\uFF1F
already-exists=\u5DF2\u7ECF\u5B58\u5728\uFF01
file-lines-greater-than=\u5C06\u5BFC\u51FA\u7684\u8BB0\u5F55\u884C\u6570\u5927\u4E8E\u5141\u8BB8\u503C
please-export-by-page=\u8BF7\u5206\u9875\u5BFC\u51FA\uFF01
monitor-not-installed=\u76D1\u63A7\u6A21\u677F\u672A\u5B89\u88C5\uFF0C\u8BF7\u5411\u7BA1\u7406\u5458\u786E\u8BA4\u3002
click-to-open-attach=\u70B9\u51FB\u6B64\u94FE\u63A5\u6216\u6309\u9F20\u6807\u53F3\u952E\u4E0B\u8F7D
guest-pls-login=\u4F1A\u8BDD\u8D85\u65F6\u6216\u5C1A\u672A\u767B\u5F55\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
current-qlc-to-admin-default=\u60A8\u9009\u62E9\u4E86DEFAULT\uFF0C\u8868\u793A\u5C06\u8DDF\u968F\u7BA1\u7406\u5458\u6307\u5B9A\u7684\u9ED8\u8BA4\u6A21\u677F\u800C\u53D8\uFF0C\u5F53\u524D\u4ED6\u6307\u5B9A\u7684\u6A21\u677F\u662F\uFF1A
pls-input-column1-for-column2=\u8BF7\u8F93\u5165c1\u7684\u5185\u5BB9\u4EE5\u786E\u8BA4c2\u5185\u5BB9\u7684\u6709\u6548\u6027
not-owner-or-not-found=\u4E0D\u662F\u521B\u5EFA\u4EBA\uFF0C\u6216\u8005\u672A\u627E\u5230\u8BB0\u5F55
error-dispatch-monitor-event=\u5728\u76D1\u63A7\u8FC7\u7A0B\u5F02\u5E38
empty-record=\u6CA1\u6709\u7B26\u5408\u6761\u4EF6\u7684\u8BB0\u5F55
input-first=\u8BF7\u5148\u8F93\u5165
object.add=\u65B0\u589E
object.create=\u4FDD\u5B58
object.modify=\u4FDD\u5B58
object.delete=\u5220\u9664
object.submit=\u63D0\u4EA4
object.copyfrom=\u590D\u5236\u4E8E
object.copyto=\u590D\u5236\u5230
object.listadd=\u6279\u91CF\u65B0\u589E
object.listcreate=\u4FDD\u5B58
object.listdelete=\u5220\u9664
object.listmodify=\u4FDD\u5B58
object.listsubmit=\u63D0\u4EA4
object.listimport=\u5BFC\u5165
object.listcopyfrom=\u4ECE..\u6279\u91CF\u590D\u5236
object.listcopyto=\u6279\u91CF\u590D\u5236\u5230..
object.newobject=\u65B0\u589E
object.gomodifypage=\u4FEE\u6539\u754C\u9762
object.goviewpage=\u67E5\u770B\u754C\u9762
object.otherviews=\u5176\u4ED6\u89C6\u56FE
obejct.request=\u7533\u62A5\u6838\u51C6
object.refresh=\u5237\u65B0
object.import=\u5BFC\u5165
object.print=\u6253\u5370
object.find=\u67E5\u627E
authenticator.success=\u767B\u5165
authenticator.failure=\u672A\u77E5\u9519\u8BEF
authenticator.error_server=\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF
authenticator.error_user_not_exists=\u7528\u6237\u4E0D\u5B58\u5728
authenticator.error_username_or_password=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF
authenticator.error_user_not_active=\u7528\u6237\u88AB\u7981\u7528
authenticator.error_locked_from_max_failure=\u7528\u6237\u5BC6\u7801\u9519\u8BEF\u6B21\u6570\u592A\u591A\uFF0C\u6682\u505C\u767B\u5F55
authenticator.error_need_usbkey=\u9700\u8981\u63D2\u5165USBKEY
authenticator.error_wrong_usbkey=USBKEY\u4E0D\u6B63\u786E
authenticator.error_session_timeout=
authenticator.error_veryfy_code=\u9A8C\u8BC1\u7801\u4E0D\u6B63\u786E
authenticator.error_username_format=\u7528\u6237\u540D\u683C\u5F0F\u4E0D\u6B63\u786E
authenticator.error_empty_password=\u5BC6\u7801\u672A\u8F93\u5165
authenticator.cookie_disabled=\u7F51\u7AD9\u9700\u8981Cookie\u652F\u6301\uFF0C\u8BF7\u4FEE\u6539\u6D4F\u89C8\u5668\u8BBE\u7F6E
cxtab-too-many-jobs-running=\u62A5\u8868\u961F\u5217\u5DF2\u6EE1\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002
cxtab-task-duplicate=\u60A8\u5728\u6700\u8FD1\u521A\u8BF7\u6C42\u6267\u884C\u4E86\u76F8\u540C\u6761\u4EF6\u7684\u62A5\u8868\uFF0C\u8BF7\u5728\u5386\u53F2\u62A5\u8868\u4E2D\u4E0B\u8F7D\uFF0C\u6216\u8010\u5FC3\u7B49\u5F85\u62A5\u8868\u8FD0\u884C\u7ED3\u675F
not-in-audit-status=\u4E0D\u5728\u5F85\u5BA1\u6838\u72B6\u6001,\u8BF7\u5237\u65B0\u9875\u9762
register-info=\u5C0A\u656C\u7684\u5BA2\u6237,\u60A8\u7684\u9A8C\u8BC1\u7801:
login-rememberMe-fail=\u7528\u6237\u672A\u627E\u5230\u6216\u8005\u7528\u6237\u6CA1\u6709\u5DF2\u8BB0\u4F4F\u7528\u6237
login-verify-fail=\u9A8C\u8BC1\u5931\u8D25
login-kickout-fail=\u60A8\u5DF2\u4ECE\u5176\u4ED6\u5730\u65B9\u767B\u9646\uFF0C\u8BF7\u91CD\u65B0\u767B\u9646
login-username-notfound=\u7528\u6237\u540D\u4E0D\u6B63\u786E
login-escrow-bind-notfound=\u8BF7\u67E5\u770B\u7528\u6237\u540D\u6216\u5BC6\u7801\u662F\u5426\u4E3A\u7A7A
login-faillogin-more=\u7528\u6237\u767B\u5F55\u5931\u8D25\u6B21\u6570\u8FC7\u591A
login-count-max=\u5F53\u524D\u5DF2\u8FBE\u5230\u6700\u5927\u5E76\u53D1\u4EBA\u6570
login-password-error=\u5BC6\u7801\u4E0D\u6B63\u786E
password-des-fail=\u52A0\u5BC6\u9519\u8BEF\uFF0C\u9519\u8BEF\u4FE1\u606F\uFF1A
authenticator.error_vVercode_code=\u9A8C\u8BC1\u7801\uFF0C\u8F93\u5165\u4E0D\u6B63\u786E
vVercode_code_timeout=\u9A8C\u8BC1\u7801\u5931\u6548\uFF0C\u8BF7\u91CD\u65B0\u9A8C\u8BC1
param_is_null=\u4F20\u5165\u53C2\u6570\u4E3A\u7A7A
this_billno_is_exists=\u5355\u636E\u7F16\u53F7\u5DF2\u5B58\u5728
admin_have_no_found=\u672A\u627E\u5230\u7BA1\u7406\u5458
password_is_wrong=\u5BC6\u7801\u9519\u8BEF
this_proname_is_exists=\u5546\u54C1\u540D\u79F0\u5DF2\u5B58\u5728
this_prono_is_exists=\u5546\u54C1\u6761\u7801\u5DF2\u5B58\u5728
this_srcname_is_exist=\u5206\u7EC4\u540D\u79F0\u5DF2\u5B58\u5728
discount_is_exists=\u6298\u6263\u5DF2\u5B58\u5728
form_have_no_found=\u672A\u627E\u5230\u62A5\u8868\u6A21\u677F
Verification_code_error=\u9A8C\u8BC1\u7801\u9519\u8BEF
verify_code_timeout=\u9A8C\u8BC1\u7801\u5931\u6548\uFF0C\u8BF7\u91CD\u65B0\u9A8C\u8BC1
authenticator.error_verify_code=\u9A8C\u8BC1\u7801\uFF0C\u8F93\u5165\u4E0D\u6B63\u786E
phone_error=\u624B\u673A\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E
user_non=\u6B64\u7528\u6237\u4E0D\u5B58\u5728
pageUploadControlUrl=http://imgshop.popcircle.cn
vip_have_no_found=\u672A\u627E\u5230\u6B64\u4F1A\u5458
cannotmissing=\u5FC5\u987B\u5B58\u5728
cannotnull=\u4E0D\u80FD\u4E3A\u7A7A
this_labelname_is_exist=\u4F1A\u5458\u6807\u7B7E\u5DF2\u5B58\u5728
titlename_is_exits=\u4F1A\u5458\u79F0\u8C13\u5DF2\u5B58\u5728
NO_PERMISSION=\u5BF9\u4E0D\u8D77\uFF01\u60A8\u6CA1\u6709\u64CD\u4F5C\u6B64\u529F\u80FD\u7684\u6743\u9650\uFF01
this_pro_is_down=\u6B64\u5546\u54C1\u4E3A\u5DF2\u4E0B\u67B6\u5546\u54C1
pro_have_no_found=\u672A\u627E\u5230\u6B64\u5546\u54C1
card_is_exits=\u6B64\u8BA1\u6B21\u5361\u5DF2\u5B58\u5728