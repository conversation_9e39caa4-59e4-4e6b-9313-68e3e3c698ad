# 对等换货策略明细删除接口调用流程

## 架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        HTTP请求                                  │
│  POST /api/cs/oc/oms/stCEquityBarterStrategy/item/delete        │
│  Body: JSONObject                                               │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Controller层                                  │
│  StCEquityBarterStrategyCtrl.deleteStrategyItems()              │
│  - 获取用户信息                                                   │
│  - 从JSONObject解析参数                                          │
│  - 调用Cmd接口                                                   │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                     API层                                       │
│  StCEquityBarterStrategyItemDeleteCmd.deleteStrategyItems()     │
│  - 接收7个直接参数                                               │
│  - 返回ValueHolderV14<String>                                   │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                   服务实现层                                      │
│  StCEquityBarterStrategyItemDeleteCmdImpl.deleteStrategyItems() │
│  - 直接调用Service层方法                                         │
│  - Dubbo服务提供者                                              │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    业务逻辑层                                     │
│  StCEquityBarterStrategyItemDeleteService.deleteStrategyItems() │
│  - 参数校验                                                      │
│  - 条件查询                                                      │
│  - 店铺筛选                                                      │
│  - 批量删除                                                      │
│  - 操作日志                                                      │
│  - 缓存清理                                                      │
└─────────────────────────────────────────────────────────────────┘
```

## 参数传递

### HTTP请求参数（JSONObject）
```json
{
    "shopScope": 1,
    "shopIds": [1001, 1002],
    "exchangeSkuId": 110101101101,
    "exchangeQty": 2.0000,
    "equitySkuId": 110100000030,
    "equityQty": 1.0000
}
```

### Cmd接口方法签名
```java
ValueHolderV14<String> deleteStrategyItems(
    Integer shopScope,           // 店铺范围：1-指定店铺；2-所选指定店铺
    List<Long> shopIds,          // 店铺id的list(当店铺范围为指定店铺时必填)
    Long exchangeSkuId,          // 换货商品id
    BigDecimal exchangeQty,      // 换货数量
    Long equitySkuId,            // 对等商品id
    BigDecimal equityQty,        // 对等数量
    User user                    // 操作用户
);
```

## 技术特点

1. **简洁明了**：接口方法签名清晰，参数含义明确
2. **类型安全**：使用强类型参数，避免类型转换错误
3. **易于测试**：参数独立，便于单元测试
4. **符合规范**：遵循项目现有的架构模式
5. **高性能**：避免了不必要的对象创建和序列化
