package aspect;

import groovy.util.logging.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> liang
 * @description
 * @date 2021/6/19
 */
@Aspect
@Component
@Slf4j
public class MdcLuncher {



    @Value("${app.id}")
    private String serverName;

    @Pointcut("execution(public * com.jackrain.nea.oc.oms.task..*(..))")
    public void AddMdc(){
    }

    @Around("AddMdc()")
    public Object  invoike(ProceedingJoinPoint point) throws Throwable{
        Object result;
        try{
            MDC.put("SERVER_NAME",serverName);
            result = point.proceed(point.getArgs());
        }finally {
            MDC.remove("SERVER_NAME");
        }
        return result;
    }

}
