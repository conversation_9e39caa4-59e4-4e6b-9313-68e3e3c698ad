package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * <AUTHOR> YCH
 * @Date : 2021-12-22 14:43
 * @Description : 退换货单相关服务
 **/
public interface OcBReturnOrderWmsCmd {

    /**
     * 页面手动取消退换货单
     */
    ValueHolderV14 cancelReturnOrder(List<Long> ids, User user) throws NDSException;

    /**
     * 退换货单确认
     *
     * @param ids  单据id
     * @param user 用户
     * @return ValueHolderV14
     * @throws NDSException
     */
    ValueHolderV14 confirmReturnOrder(List<Long> ids, User user) throws NDSException;

}
