package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.VipTimeOrderTransferCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.request.IpVipTimeOrderItemQueryRequest;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItem;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.VipTimeOrderProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 陈秀楼
 * @since : 2019-06-27
 * create at : 2019-06-27 20:00
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class VipTimeOrderTransferCmdImpl implements VipTimeOrderTransferCmd {

    @Autowired
    private VipTimeOrderProcessImpl vipTimeOrderProcessImpl;

    @Autowired
    private IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private IpVipTimeOrderItemService ipVipTimeOrderItemService;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferTimeOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult<IpVipTimeOrderRelation>> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("VipTimeOrderTransferCmdImpl.startTransferTimeOrder ChannelType: {}",
                            orderNo), transferOrderRequest.getChannelType());
                }
                if (transferOrderRequest.getChannelType() == ChannelType.VIPJITX) {
                    IpVipTimeOrderRelation orderRelation = this.ipVipTimeOrderService.selectTimeOrder(orderNo);
                    if (orderRelation != null && orderRelation.getIpBTimeOrderVip() != null) {
                        ProcessStepResultList processStepResultList = vipTimeOrderProcessImpl.start(orderRelation,
                                false, transferOrderRequest.getOperateUser());
                        if (!processStepResultList.isProcessFishSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFaileOrFinishProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }

//                    if (orderRelation == null || orderRelation.getIpBTimeOrderVip() == null) {
//                        ProcessStepResult<IpVipTimeOrderRelation> result = new ProcessStepResult<>(StepStatus.FAILED, "Received OrderMqInfo Not Exist!occupiedOrderSn=" + orderNo);
//                        errorStepResultList.add(result);
//                        hasError = true;
//                        failedNumber++;
//                    } else {
//                        ProcessStepResultList resultList = new ProcessStepResultList();
//                        Integer status = orderRelation.getIpBTimeOrderVip().getStatus();
//                        boolean isCanTransferOrderFlag = TimeOrderVipStatusEnum.CREATED.getValue().equals(status)
//                                || TimeOrderVipStatusEnum.OUT_STOCK.getValue().equals(status);
//                        if (isCanTransferOrderFlag) {
//                            resultList = vipTimeOrderProcessImpl.start(orderRelation, false, SystemUserResource.getRootUser());
//                            if (!resultList.isProcessFishSuccess()) {
//                                hasError = true;
//                                errorStepResultList.add(resultList.getLastFaileOrFinishProcessStepResult());
//                                failedNumber++;
//                            } else {
//                                successNumber++;
//                            }
//                        } else {
//                            IpBTimeOrderVip ipTimeOrderVip = orderRelation.getIpBTimeOrderVip();
//                            ipVipTimeOrderService.updateTimeOrderTransferStatus(TransferOrderStatus.TRANSFERRED.toInteger()
//                                    , "时效订单不是已创建或缺货，不进行转单，标记为已转换！", ipTimeOrderVip);
//                            if (log.isDebugEnabled()) {
//                                log.debug("Finished MqOrder Transfer.时效订单不是已创建或缺货，不进行转单 occupiedOrderSn="
//                                        + orderNo + ";Result=" + resultList);
//                            }
//                            successNumber++;
//                        }
//                    }
                }
            }
            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("VipTimeOrderTransferCmdImpl.startTransferTimeOrder.转换异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return resultValueHolderV14;
    }

    @Override
    public ValueHolderV14<List<IpBTimeOrderVipItem>> getIpBTimeOrderVipItemByMainId(IpVipTimeOrderItemQueryRequest request) {
        return ipVipTimeOrderItemService.getIpBTimeOrderVipItemByMainId(request);
    }
}
